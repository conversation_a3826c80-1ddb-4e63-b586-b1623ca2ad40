name: Push to <PERSON>
on:
  push:
    branches:
      - main
    paths-ignore:
      - "VERSION"

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: read

jobs:
  ci:
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      publish_git_sha: ${{ github.sha }}
      release_as_latest: false
      run_serial: false
      skip_tests: false
      debug_enabled: false
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

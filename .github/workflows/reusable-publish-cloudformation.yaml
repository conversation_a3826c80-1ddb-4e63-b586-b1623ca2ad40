name: Publish CloudFormation
on:
  workflow_call:
    inputs:
      release_as_latest:
        type: boolean
        description: "Release live to users as 'latest'"
        required: false
        default: false
      publish_release_tag:
        type: string
        description: "Publish with custom release tag. Optional"
        required: false
      publish_git_sha:
        type: string
        description: "Publish with git SHA. Optional"
        required: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  publish-cloudformation:
    if: ${{ inputs.release_as_latest == true || inputs.publish_release_tag != '' || inputs.publish_git_sha != '' }}
    runs-on: warp-ubuntu-2404-x64-8x
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::872608195481:role/github_ecr_full_access
          aws-region: us-east-1

      - name: Install Braintrust dependencies
        uses: ./.github/actions/deps

      - name: Publish CloudFormation templates to S3 as SHA
        if: ${{ inputs.publish_git_sha }}
        run: |
          eval "$(mise activate)"
          make chalice-cf BRAINSTORE_VERSION="${{ inputs.publish_git_sha }}"
          make publish-cf ARGS="${{ inputs.publish_git_sha }}"

      - name: Publish CloudFormation templates to S3 as latest
        if: ${{ inputs.release_as_latest }}
        run: |
          eval "$(mise activate)"
          make chalice-cf
          make publish-cf ARGS="latest"

      - name: Publish CloudFormation templates to S3 as custom release tag
        if: ${{ inputs.publish_release_tag }}
        run: |
          eval "$(mise activate)"
          make chalice-cf BRAINSTORE_VERSION="${{ inputs.publish_release_tag }}"
          make publish-cf ARGS="${{ inputs.publish_release_tag }}"

      - name: Add template locations to job summary
        run: |
          set -e
          echo "## CloudFormation Template Locations" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          cat api/out/template_locations.*.txt >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}

name: Vercel Production Deployment
on:
  push:
    branches:
      - main
  workflow_dispatch:
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  NODE_OPTIONS: ${{ vars.NODE_OPTIONS }}
  TURBO_TEAM: ${{ secrets.VERCEL_ORG_ID }}
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}
jobs:
  Deploy-Production:
    permissions:
      deployments: write
      pull-requests: write
      contents: read
    runs-on: warp-ubuntu-2404-x64-8x
    steps:
      - name: Start deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: Production

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ env.FETCH_DEPTH }}
          submodules: recursive

      - uses: ./.github/actions/deps

      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        env:
          VERCEL_SKIP_DOMAINS: ${{ vars.VERCEL_SKIP_DOMAINS }}
        run: |
          if [[ "$VERCEL_SKIP_DOMAINS" != "false" ]]; then
            SKIP_DOMAINS_FLAG="--skip-domain"
          else
            SKIP_DOMAINS_FLAG=""
          fi
          DEPLOY_OUTPUT=$(vercel deploy --archive=tgz --prebuilt --prod --yes "$SKIP_DOMAINS_FLAG" --token=${{ secrets.VERCEL_TOKEN }})
          DEPLOY_URL=$(echo "$DEPLOY_OUTPUT" | grep -o 'https://[^ ]*')
          echo "DEPLOY_URL=$DEPLOY_URL" >> $GITHUB_ENV

      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}
          status: ${{ job.status }}
          env: ${{ steps.deployment.outputs.env }}
          env_url: ${{ env.DEPLOY_URL }}

name: Release
on:
  push:
    branches:
      - main
    paths:
      - VERSION
  workflow_dispatch:
    inputs:
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      docker_run_serial:
        type: boolean
        description: "Run docker tests in serial mode rather than parallel"
        required: false
        default: true
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: write # Required for creating releases

jobs:
  create-draft-release:
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create-draft-release.outputs.release_id }}
      version: ${{ steps.create-draft-release.outputs.version }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: "VERSION" # Only checkout the VERSION file

      - name: Create GitHub Release
        id: create-draft-release
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const version = fs.readFileSync('VERSION', 'utf8').trim();
            core.setOutput('version', version);
            try {
              const response = await github.rest.repos.createRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                tag_name: version,
                name: version,
                body: `Release ${version}`,
                generate_release_notes: true,
                target_commitish: "main",
                draft: true,
                prerelease: false
              });
              core.info(`Created release: ${response.data.id}`);
              core.setOutput('release_id', response.data.id);
            } catch (error) {
              core.setFailed(`Failed to create release: ${error.message}`);
              throw error;
            }

  ci:
    needs: [create-draft-release]
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      release_as_latest: true
      publish_release_tag: ${{ needs.create-draft-release.outputs.version }}
      publish_git_sha: ${{ github.sha }}
      run_serial: ${{ github.event.inputs.run_serial || false }}
      docker_run_serial: ${{ github.event.inputs.docker_run_serial || true }}
      skip_tests: ${{ github.event.inputs.skip_tests || false }}
      debug_enabled: ${{ github.event.inputs.debug_enabled || false }}
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

  publish-release:
    needs: [create-draft-release, ci]
    runs-on: ubuntu-latest
    steps:
      - name: Mark release as non-draft
        uses: actions/github-script@v6
        with:
          script: |
            const releaseId = '${{ needs.create-draft-release.outputs.release_id }}';
            try {
              await github.rest.repos.updateRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                release_id: releaseId,
                draft: false
              });
              core.info(`Successfully published release ${releaseId}`);
            } catch (error) {
              core.setFailed(`Failed to publish release: ${error.message}`);
              throw error;
            }

name: Pull Request
on:
  pull_request:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "Pull request number to run CI for"
        required: true
        type: string

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: read
  actions: write # Required for workflow dispatch

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || inputs.pr_number }}
  cancel-in-progress: true

jobs:
  ci:
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      release_as_latest: false
      run_serial: false
      skip_tests: false
      debug_enabled: false
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

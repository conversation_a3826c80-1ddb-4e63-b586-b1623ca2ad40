name: Build and Test Backend
on:
  workflow_call:
    inputs:
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

    secrets:
      DOCKERHUB_TOKEN:
        required: true
      ORB_API_KEY:
        required: true
      DD_API_KEY:
        required: true
      POLAR_SIGNALS_API_KEY:
        required: true

jobs:
  integration-tests:
    runs-on: warp-ubuntu-2404-x64-8x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone
          submodules: recursive

      - name: Run integration tests
        timeout-minutes: 40
        if: ${{ !contains(github.event.pull_request.labels.*.name, 'skip-integration-tests') }}
        uses: ./.github/actions/test-bt-services
        with:
          run_serial: ${{ inputs.run_serial }}
          skip_tests: ${{ inputs.skip_tests }}
          DOCKERHUB_USERNAME: ${{ vars.DOCKERHUB_USERNAME }}
          DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
          ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
          DD_API_KEY: ${{ secrets.DD_API_KEY }}
          POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}

  unit-tests:
    runs-on: warp-ubuntu-2404-x64-8x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone
          submodules: recursive
      - uses: ./.github/actions/deps
      - uses: ./.github/actions/test-nobt-services
      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}

name: Braintrust CI/CD
on:
  workflow_call:
    inputs:
      release_as_latest:
        description: "Release live to users as 'latest'"
        type: boolean
        required: false
        default: false
      publish_release_tag:
        description: "Publish with custom release tag. Optional"
        type: string
        required: false
      publish_git_sha:
        type: string
        description: "Publish artifacts tagged with git SHA. Optional"
        required: false
      run_serial:
        type: boolean
        description: "Run tests in serial mode rather than parallel"
        required: false
        default: false
      docker_run_serial:
        type: boolean
        description: "Run docker tests in serial mode rather than parallel"
        required: false
        default: true
      skip_tests:
        type: boolean
        description: "Skip running tests"
        required: false
        default: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

    secrets:
      DOCKERHUB_TOKEN:
        required: true
      ORB_API_KEY:
        required: true
      DD_API_KEY:
        required: true
      POLAR_SIGNALS_API_KEY:
        required: true
      VERCEL_ORG_ID:
        required: true
      VERCEL_TOKEN:
        required: true

env:
  TURBO_TEAM: ${{ secrets.VERCEL_ORG_ID }}
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}

jobs:
  backend:
    uses: ./.github/workflows/reusable-build-test-backend.yaml
    with:
      run_serial: ${{ inputs.run_serial }}
      skip_tests: ${{ inputs.skip_tests }}
      debug_enabled: ${{ inputs.debug_enabled }}
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}

  brainstore:
    uses: ./.github/workflows/reusable-build-test-brainstore.yaml
    with:
      debug_enabled: ${{ inputs.debug_enabled }}
      skip_tests: ${{ inputs.skip_tests }}

  docker:
    uses: ./.github/workflows/reusable-build-test-docker.yaml
    permissions:
      id-token: write
      contents: read
    with:
      skip_tests: ${{ inputs.skip_tests }}
      run_serial: ${{ inputs.docker_run_serial }}
      debug_enabled: ${{ inputs.debug_enabled }}
      release_as_latest: ${{ inputs.release_as_latest }}
      publish_release_tag: ${{ inputs.publish_release_tag }}
      publish_git_sha: ${{ inputs.publish_git_sha }}
    secrets:
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}

  publish-lambdas:
    # This is only used by Terraform. Cloudformation publishes lambdas as part of its own build process.
    needs: [backend]
    uses: ./.github/workflows/reusable-publish-lambdas.yaml
    permissions:
      id-token: write
      contents: read
    with:
      release_as_latest: ${{ inputs.release_as_latest }}
      publish_release_tag: ${{ inputs.publish_release_tag }}
      publish_git_sha: ${{ inputs.publish_git_sha }}
      debug_enabled: ${{ inputs.debug_enabled }}

  publish-cloudformation:
    needs: [backend, brainstore, docker]
    uses: ./.github/workflows/reusable-publish-cloudformation.yaml
    permissions:
      id-token: write
      contents: read
    with:
      release_as_latest: ${{ inputs.release_as_latest }}
      publish_release_tag: ${{ inputs.publish_release_tag }}
      publish_git_sha: ${{ inputs.publish_git_sha }}
      debug_enabled: ${{ inputs.debug_enabled }}

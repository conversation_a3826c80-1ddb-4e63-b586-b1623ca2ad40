# This should be run after 'deps'.
name: Start Services
inputs:
  DOCKERHUB_USERNAME:
    required: true
    type: string
  DOCKERHUB_TOKEN:
    required: true
    type: string
  ORB_API_KEY:
    required: true
    type: string
runs:
  using: "composite"
  steps:
    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      # As of 2024-11-12, our org actions secret contains a braintrust free-tier
      # account which gets 200 pulls / 6 hours. We can consider upgrading to
      # Docker Pro if we continue to exceed rate limits.
      with:
        username: ${{ inputs.DOCKERHUB_USERNAME }}
        password: ${{ inputs.DOCKERHUB_TOKEN }}
    - name: Start services
      shell: bash
      run: |
        eval "$(mise activate)"
        cp app/.env.development app/.env.local
        BT_SERVICES_START_ARGS="--exclude watch --webapp-mode prod" make -j services
      env:
        ORB_API_KEY: ${{ inputs.ORB_API_KEY }}

    - name: Run App migrations
      shell: bash
      run: |
        eval "$(mise activate)"
        cd app && npx supabase migration up

    - name: Run API migrations
      shell: bash
      run: |
        eval "$(mise activate)"
        python api-schema/lambda_function.py --execute --run-all-in-foreground

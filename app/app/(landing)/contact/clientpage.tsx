"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { Input, inputClassName } from "#/ui/input";
import { ExternalLink } from "#/ui/link";
import { cn } from "#/utils/classnames";
import { DISCORD, INFO } from "#/utils/links";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const contactFormSchema = z.object({
  name: z.string(),
  email: z.string(),
  comment: z.string().optional(),
});

export const ContactClientPage = () => {
  const form = useForm<z.infer<typeof contactFormSchema> & { "": string }>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      comment: "",
    },
  });

  return (
    <div className="mb-44 px-4 sm:px-8">
      <section className="my-16">
        <h1 className="mb-4 text-6xl font-medium tracking-tight">
          Chat with us
        </h1>
      </section>
      <div className="w-full max-w-screen-sm font-inter">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (data) => {
              try {
                const res = await fetch("/contact/submit", {
                  method: "POST",
                  body: JSON.stringify(data),
                  headers: {
                    "Content-Type": "application/json",
                  },
                });
                const json = await res.json();

                if (!json.ok) {
                  toast.error(
                    "Something went wrong. Please email <NAME_EMAIL>.",
                  );
                  return;
                }

                form.reset({
                  name: "",
                  email: "",
                  comment: "",
                });
                toast.success("Thank you for registering");
              } catch (error) {
                toast.error(
                  "Something went wrong. Please email <NAME_EMAIL>.",
                );
              }
            })}
            className="flex flex-col gap-6"
          >
            <div className="flex flex-col gap-6 md:flex-row">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="text-base">Full name</FormLabel>
                    <FormControl>
                      <Input
                        className="text-base"
                        placeholder="Enter your full name"
                        {...field}
                        value={field.value ?? ""}
                        required
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="text-base">Work email</FormLabel>
                    <FormControl>
                      <Input
                        className="text-base"
                        placeholder="Enter your work email"
                        {...field}
                        type="email"
                        value={field.value ?? ""}
                        required
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={form.control}
                name="comment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">
                      How can we help?
                    </FormLabel>
                    <FormControl>
                      <textarea
                        className={cn(inputClassName, "min-h-30 text-base")}
                        placeholder="Your company needs"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div>
              <Button
                type="submit"
                size="lg"
                variant="border"
                className="text-base border-primary-950"
                isLoading={form.formState.isSubmitting}
                disabled={!form.formState.isValid}
              >
                Submit
              </Button>
            </div>
          </form>
        </Form>
      </div>
      <section className="mb-6 mt-12 md:mb-10 lg:mb-15 lg:mt-24">
        <div className="flex flex-col gap-6 text-primary-600">
          <div>
            Email us at{" "}
            <ExternalLink href={INFO}><EMAIL></ExternalLink>
          </div>
          <div>
            Join us on <ExternalLink href={DISCORD}>Discord</ExternalLink>
          </div>

          <div>
            548 Market St PMB 96611
            <br />
            San Francisco, California 94104-5401
            <br />
            (707) 682-7588
          </div>
        </div>
      </section>
    </div>
  );
};

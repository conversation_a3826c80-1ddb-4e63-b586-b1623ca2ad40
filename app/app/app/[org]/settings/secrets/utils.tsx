import { formatCurrency } from "#/ui/cost-input";
import {
  Anthropic,
  Azure,
  Bedrock,
  Cerebras,
  Databricks,
  Fireworks,
  Gemini,
  GoogleCloud,
  Groq,
  Lepton,
  Mistral,
  Ollama,
  OpenAI,
  Perplexity,
  Replicate,
  Together,
  XAI,
} from "#/ui/icons/providers";
import { isEmpty } from "#/utils/object";
import {
  modelProviderHasReasoning,
  ModelSchema,
  type ModelSpec,
} from "@braintrust/proxy/schema";
import { Box, Chrome } from "lucide-react";
import { z } from "zod";

export const CustomModelFormSchema = ModelSchema.pick({
  format: true,
  flavor: true,
  multimodal: true,
  displayName: true,
  locations: true,
  o1_like: true,
  reasoning: true,
  reasoning_budget: true,
  description: true,
}).merge(
  z.object({
    modelName: z.string().min(1, "Name cannot be empty"),
    input_cost_per_mil_tokens: z.string().nullish(),
    output_cost_per_mil_tokens: z.string().nullish(),
  }),
);

export const CustomModelFormArraySchema = z.array(CustomModelFormSchema);
type CustomModelFormArray = z.infer<typeof CustomModelFormArraySchema>;

export const providerReadableName = (name: string): string => {
  switch (name) {
    case "openai":
      return "OpenAI";
    case "together":
      return "Together.ai";
    case "google":
      return "Gemini";
    case "vertex":
      return "Vertex AI";
    case "azure_entra":
      return "Azure (Entra)";
    default:
      return `${name.charAt(0).toUpperCase()}${
        name.length > 1 ? name.substring(1) : ""
      }`;
  }
};

export const getProviderIcon = (name: string, size = 24) => {
  if (name.toLowerCase().startsWith("openai")) {
    return <OpenAI size={size} />;
  }
  if (name.toLowerCase().startsWith("azure")) {
    return <Azure size={size} />;
  }
  if (name.toLowerCase().startsWith("google")) {
    return <Gemini size={size} />;
  }
  if (name.toLowerCase().startsWith("perplexity")) {
    return <Perplexity size={size} />;
  }
  if (
    name.toLowerCase().startsWith("magistral") ||
    name.toLowerCase().startsWith("mistral") ||
    name.toLowerCase().startsWith("pixtral")
  ) {
    return <Mistral size={size} />;
  }
  if (name.toLowerCase().startsWith("anthropic")) {
    return <Anthropic size={size} />;
  }
  if (name.toLowerCase().startsWith("replicate")) {
    return <Replicate size={size} />;
  }
  if (name.toLowerCase().startsWith("ollama")) {
    return <Ollama size={size} />;
  }
  if (name.toLowerCase().startsWith("groq")) {
    return <Groq size={size} />;
  }
  if (name.toLowerCase().startsWith("together")) {
    return <Together size={size} />;
  }
  if (name.toLowerCase().startsWith("window")) {
    return <Chrome size={size} />;
  }
  if (
    name.toLowerCase().startsWith("bedrock") ||
    name.toLowerCase().includes("nova")
  ) {
    return <Bedrock size={size} />;
  }
  if (name.toLowerCase().startsWith("vertex")) {
    return <GoogleCloud size={size} />;
  }
  if (name.toLowerCase().startsWith("lepton")) {
    return <Lepton size={size} />;
  }
  if (name.toLowerCase().startsWith("cerebras")) {
    return <Cerebras size={size} />;
  }
  if (name.toLowerCase().startsWith("fireworks")) {
    return <Fireworks size={size} />;
  }
  if (
    name.toLowerCase().startsWith("grok") ||
    name.toLowerCase().startsWith("xai")
  ) {
    return <XAI size={size} />;
  }
  if (name.toLowerCase().startsWith("databricks")) {
    return <Databricks size={size} />;
  }

  return <Box size={size} />;
};

export function getDisplayName(fieldPath: string) {
  const pathParts = fieldPath.split(".");
  const leafName = pathParts[pathParts.length - 1];
  switch (leafName) {
    case "api_base":
      return "API base URL";
    case "api_version":
      return "API version";
    case "organization_id":
      return "Organization ID";
    default:
      return leafName
        .split("_")
        .map((part) => part.substring(0, 1).toUpperCase() + part.substring(1))
        .join(" ");
  }
}

export function getPlaceholderName(fieldPath: string) {
  const pathParts = fieldPath.split(".");
  const leafName = pathParts[pathParts.length - 1];
  switch (leafName) {
    case "api_base":
      return "API base URL";
    case "api_version":
      return "API version";
    case "organization_id":
      return "organization ID";
    default:
      return leafName.split("_").join(" ");
  }
}

export function customModelArrayToObject(
  modelsArray: CustomModelFormArray,
): Record<string, ModelSpec> {
  return modelsArray.reduce(
    (
      acc,
      {
        modelName,
        input_cost_per_mil_tokens,
        output_cost_per_mil_tokens,
        ...model
      },
    ) => ({
      ...acc,
      [modelName]: {
        ...model,
        reasoning:
          model.reasoning ||
          model.o1_like ||
          modelProviderHasReasoning?.[model.format]?.test(modelName),
        input_cost_per_mil_tokens: input_cost_per_mil_tokens
          ? parseFloat(input_cost_per_mil_tokens.replaceAll("$", ""))
          : undefined,
        output_cost_per_mil_tokens: output_cost_per_mil_tokens
          ? parseFloat(output_cost_per_mil_tokens.replaceAll("$", ""))
          : undefined,
      },
    }),
    {},
  );
}

export function customModelObjectToArray(
  modelsObject: Record<string, ModelSpec>,
): CustomModelFormArray {
  return Object.entries(modelsObject).map(
    ([
      modelName,
      { input_cost_per_mil_tokens, output_cost_per_mil_tokens, ...spec },
    ]) => ({
      ...spec,
      modelName,
      reasoning:
        spec.reasoning ||
        spec.o1_like ||
        modelProviderHasReasoning?.[spec.format]?.test(modelName),
      input_cost_per_mil_tokens: !isEmpty(input_cost_per_mil_tokens)
        ? formatCurrency(input_cost_per_mil_tokens)
        : undefined,
      output_cost_per_mil_tokens: !isEmpty(output_cost_per_mil_tokens)
        ? formatCurrency(output_cost_per_mil_tokens)
        : null,
    }),
  );
}

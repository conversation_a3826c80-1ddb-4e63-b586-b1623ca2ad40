"use client";

import { useEffect, useState } from "react";

import "react-image-crop/dist/ReactCrop.css";
import {
  featureFlagConfig,
  type FeatureFlags,
  useFeatureFlags,
} from "#/lib/feature-flags";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import * as semver from "semver";
import { Loading } from "#/ui/loading";
import { Skeleton } from "#/ui/skeleton";
import { FeatureFlagToggle } from "./feature-flag-toggle";
import { AlertTriangle } from "lucide-react";
import { useOrg, useUser } from "#/utils/user";
import { useQueryFunc } from "#/utils/react-query";
import { type getBrainstoreLicense } from "../api-url/actions";
import { MultiTenantApiURL } from "#/utils/user-types";

// See https://github.com/pacocoursey/next-themes/issues/169
export default function Page() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex w-full max-w-md flex-col gap-2">
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
        <Skeleton className="h-20" />
      </div>
    );
  }

  return <PageImpl />;
}

function PageImpl() {
  const { flags, setFlag } = useFeatureFlags();
  const { version: apiVersion } = useAPIVersion();

  const org = useOrg();
  const user = useUser();
  const isBraintrustSupport = !!(
    user.user?.email?.endsWith("@braintrustdata.com") ||
    user.user?.email?.endsWith("@braintrust.dev")
  );

  const isOnPrem = org.api_url !== MultiTenantApiURL;

  const { data: brainstoreLicense } = useQueryFunc<typeof getBrainstoreLicense>(
    {
      fName: "getBrainstoreLicense",
      args: { org_name: org.name },
    },
  );
  const hideBrainstore = isOnPrem && !brainstoreLicense;

  return (
    <div>
      <h2 className="mb-2 text-lg font-semibold">Feature flags</h2>
      <div className="mb-4 text-sm text-primary-600">
        Preview features that are under development. Expect to encounter bugs
        and changes.
      </div>
      {apiVersion !== null ? (
        <div className="flex flex-col gap-2 pt-2">
          {Object.entries(featureFlagConfig).map(([key, config]) => {
            if (config.isDebugFlag || isHidden(config, hideBrainstore)) {
              return null;
            }
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            const flag = key as keyof FeatureFlags;
            return (
              <FlagItem
                key={key}
                flag={flag}
                config={config}
                setFlag={setFlag}
                apiVersion={apiVersion}
                flags={flags}
                showHidden={false}
                hideBrainstore={hideBrainstore}
              />
            );
          })}

          <div className="flex flex-col gap-2 pt-10">
            <h3 className="flex items-center text-base font-semibold">
              <AlertTriangle className="mr-1 size-4 text-bad-700" />
              Debug flags
            </h3>
            <div className="mb-4 text-sm text-primary-600">
              The following flags are used for debugging purposes only and
              should only be enabled when working with Braintrust support.
            </div>
            {Object.entries(featureFlagConfig).map(([key, config]) => {
              if (!config.isDebugFlag || (config.onPremOnly && !isOnPrem)) {
                return null;
              }
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              const flag = key as keyof FeatureFlags;
              return (
                <FlagItem
                  key={key}
                  flag={flag}
                  config={config}
                  setFlag={setFlag}
                  apiVersion={apiVersion}
                  flags={flags}
                  showHidden={false}
                  hideBrainstore={hideBrainstore}
                />
              );
            })}
          </div>

          {isBraintrustSupport && (
            <div className="flex flex-col gap-2 pt-10">
              <h3 className="flex items-center text-base font-semibold">
                <AlertTriangle className="mr-1 size-4 text-bad-700" />
                Hidden flags
              </h3>
              <div className="mb-4 text-sm text-primary-600">
                The following flags are hidden from the UI and are only
                available to Braintrust support. Do not send customers
                screenshots of these flags.
              </div>
              {Object.entries(featureFlagConfig).map(([key, config]) => {
                if (!isHidden(config, hideBrainstore)) {
                  return null;
                }
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                const flag = key as keyof FeatureFlags;
                return (
                  <FlagItem
                    key={key}
                    flag={flag}
                    config={config}
                    setFlag={setFlag}
                    apiVersion={apiVersion}
                    flags={flags}
                    showHidden={true}
                    hideBrainstore={hideBrainstore}
                  />
                );
              })}
            </div>
          )}
        </div>
      ) : (
        <Loading />
      )}
    </div>
  );
}

const FlagItem = ({
  setFlag,
  flag,
  config,
  apiVersion,
  flags,
  showHidden,
  hideBrainstore,
}: {
  flag: keyof FeatureFlags;
  setFlag: (flag: keyof FeatureFlags, checked: boolean) => void;
  apiVersion: string;
  flags: FeatureFlags;
  config: (typeof featureFlagConfig)[keyof FeatureFlags];
  showHidden: boolean;
  hideBrainstore: boolean;
}) => {
  if (isHidden(config, hideBrainstore) && !showHidden) {
    return null;
  }

  if (config.dependsOn) {
    const isDependsOnEnabled = !!flags[config.dependsOn];
    if (!isDependsOnEnabled) {
      return null;
    }
  }

  const disabled = !apiVersion || semver.lt(apiVersion, config.minVersion);
  const checked = !!flags[flag];
  return (
    <FeatureFlagToggle
      key={flag}
      className="mb-4"
      flag={flag}
      checked={checked}
      disabled={disabled}
      setFlag={setFlag}
      title={config.title}
      description={config.description}
    />
  );
};

function isHidden(
  config: (typeof featureFlagConfig)[keyof FeatureFlags],
  hideBrainstore: boolean,
) {
  return (
    config.isHidden &&
    !(
      !hideBrainstore && config.title.toLocaleLowerCase().includes("brainstore")
    )
  );
}

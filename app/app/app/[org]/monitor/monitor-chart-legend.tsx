import { getMetricsLabel } from "./utils";

import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";
import {
  isLineHighlighted,
  isLineSelected,
  type HighlightState,
  type HighlightedGroup,
  type HighlightedGroupTypeEnum,
} from "#/ui/charts/highlight";
import {
  compareSelectionTypes,
  formatSelectionTypeName,
  type SelectionType,
  type SelectionTypesEnum,
} from "#/ui/charts/selectionTypes";
import { ChartSymbol } from "#/ui/charts/symbols";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { isEmpty } from "@braintrust/core";
import { type MouseEvent, useMemo, useState } from "react";
import { type TimeseriesAggregates } from "#/ui/charts/timeseries-data/chart-data.types";

type LegendLabelData = {
  groupVal: SelectionType;
  label: string;
  sumValue?: string;
  isSum?: boolean;
} & (
  | {
      type: "points";
      colorIndex: number;
    }
  | { type: "symbols"; symbolIndex: number }
);

const chartLegendCompareFn = (
  {
    groupVal: selectionTypeA,
    label: labelA,
  }: { groupVal: SelectionType; label: string },
  {
    groupVal: selectionTypeB,
    label: labelB,
  }: { groupVal: SelectionType; label: string },
) => {
  if (selectionTypeA.type !== selectionTypeB.type) {
    return compareSelectionTypes(selectionTypeA.type, selectionTypeB.type);
  }

  const aFloat = parseFloat(labelA);
  const bFloat = parseFloat(labelB);
  if (!Number.isNaN(aFloat) && !Number.isNaN(bFloat)) {
    return aFloat - bFloat;
  }

  if (labelA === labelB) return 0;
  if (labelB === "null") return -1;
  else if (labelA === "null") return 1;
  else if (labelA < labelB) return -1;
  else if (labelA > labelB) return 1;
  else return 0;
};

function toLegendLabelData(
  groupData: Record<string, Record<SelectionTypesEnum, number>>,
  legendType: HighlightedGroupTypeEnum,
  sort: boolean,
): LegendLabelData[] {
  const result = Object.entries(groupData).flatMap(([groupName, groupTypes]) =>
    Object.entries(groupTypes).map(([type, v]) => {
      const selectionType = {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        type: type as SelectionTypesEnum,
        value: groupName,
      };
      return {
        groupVal: selectionType,
        label: formatSelectionTypeName(selectionType),
        ...(legendType === "points"
          ? { type: "points" as const, colorIndex: v }
          : { type: "symbols" as const, symbolIndex: v }),
      };
    }),
  );
  if (sort) {
    return result.toSorted(chartLegendCompareFn);
  }
  return result;
}

const LegendLabel = (
  props: LegendLabelData & {
    className?: string;
    symbolClassName?: string;
    labelClassName?: string;
    onClick?: React.MouseEventHandler;
    onMouseEnter?: React.MouseEventHandler;
    onMouseLeave?: React.MouseEventHandler;
    aggregatedValue?: string;
    width: number;
  },
) => {
  const {
    className,
    symbolClassName,
    labelClassName,
    label,
    type,
    onClick,
    onMouseEnter,
    onMouseLeave,
    aggregatedValue,
    width,
  } = props;
  const symbol =
    type === "points" ? (
      <ChartSymbol
        className={cn(
          COLOR_CLASSNAMES[props.colorIndex] || DEFAULT_COLOR_CLASSNAME,
          className,
          symbolClassName,
        )}
        index={0}
        size={8}
      />
    ) : (
      <ChartSymbol
        className={cn("fill-primary-800", className, symbolClassName)}
        index={props.symbolIndex}
        size={8}
      />
    );

  const MARGIN_LEFT = 4;
  const MARGIN_RIGHT = 2;
  const PILL_RIGHT = 16;
  const maxWidth = `${Math.floor(width - MARGIN_RIGHT - PILL_RIGHT - MARGIN_LEFT)}px`;

  return (
    <div
      className={cn("flex flex-col items-end", className)}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {!isEmpty(aggregatedValue) && (
        <div className="text-sm font-semibold text-primary-900">
          {aggregatedValue}
        </div>
      )}
      <div className="flex items-center">
        <div
          className={cn("mr-0.5 truncate text-xs", labelClassName)}
          style={{ maxWidth: maxWidth }}
          title={label}
        >
          {label}
        </div>
        {symbol}
      </div>
    </div>
  );
};

const LegendLabels = ({
  className,
  legendLabelData,
  highlightState,
  addSelected,
  removeSelected,
  setSelected,
  highlight,
  clearHighlight,
  isLegendHovered,
  width,
}: {
  className?: string;
  legendLabelData: LegendLabelData[];
  highlightState: HighlightState;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: () => void;
  isLegendHovered: boolean;
  width: number;
}) => {
  return (
    <div className={className}>
      {legendLabelData.map((line, i) => {
        // if hover over legend, fade if not highlighted
        // else fade if not selected
        const isHighlighted = isLineHighlighted(line.groupVal, highlightState);
        const isSelected = isLineSelected(line.groupVal, highlightState);
        const labelFade = isLegendHovered ? !isHighlighted : !isSelected;
        return (
          <LegendLabel
            key={i}
            // not sure why, but margin bottom here prevents flickering between labels
            className={cn(
              "pb-1.5 cursor-pointer select-none mb-px transition-opacity",
            )}
            labelClassName={cn({
              "opacity-40": labelFade,
            })}
            symbolClassName={cn({
              "opacity-40": labelFade,
            })}
            width={width}
            onClick={(event: MouseEvent) => {
              const v = {
                groupVal: line.groupVal,
                type: line.type,
              };

              if (event.ctrlKey || event.metaKey) {
                removeSelected(v);
              } else if (event.shiftKey) {
                addSelected(v);
              } else {
                setSelected(v);
              }
            }}
            onMouseEnter={() => {
              highlight({
                groupVal: line.groupVal,
                type: line.type,
              });
            }}
            onMouseLeave={() => {
              clearHighlight();
            }}
            {...line}
          />
        );
      })}
    </div>
  );
};

const LEGEND_ENTRY_HEIGHT = 42;

export interface MonitorChartLegendProps {
  highlightState: HighlightState;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: () => void;
  seriesColorMap: Record<string, Record<SelectionTypesEnum, number>>;
  seriesAggregates: TimeseriesAggregates[];
  seriesMetadata: { selectionType: SelectionType; name: string }[];
  aggregateType: "sum" | "average";
  aggregateFormatter: (value: number) => string;
  sortLegend: boolean;
  height: number;
  width: number;
}

const MonitorChartLegend = ({
  highlightState,
  seriesColorMap,
  seriesAggregates,
  seriesMetadata,
  addSelected,
  removeSelected,
  setSelected,
  highlight,
  clearHighlight,
  aggregateType,
  aggregateFormatter,
  sortLegend,
  height,
  width,
}: MonitorChartLegendProps) => {
  const maxLegendItems = Math.floor(height / LEGEND_ENTRY_HEIGHT);

  const linesWithData = Object.fromEntries(
    seriesMetadata.map((data) => [
      data.selectionType.value,
      data.selectionType.type,
    ]),
  );

  const [isLegendHovered, setIsLegendHovered] = useState<boolean>(false);

  const aggregatedValues = useMemo(() => {
    return seriesMetadata.map((data, i) => {
      const { sum, count } = seriesAggregates[i];
      if (aggregateType === "sum") {
        return {
          selectionType: data.selectionType,
          value: sum,
          formatted: aggregateFormatter(sum),
        };
      }

      if (count === 0) {
        return {
          selectionType: data.selectionType,
          value: 0,
        };
      }
      const average = sum / count;

      return {
        selectionType: data.selectionType,
        value: average,
        formatted: aggregateFormatter(average),
      };
    });
  }, [seriesMetadata, seriesAggregates, aggregateType, aggregateFormatter]);

  const valueMap = useMemo(() => {
    return Object.fromEntries(
      aggregatedValues.map(({ selectionType, value }) => [
        `${selectionType.value}:${selectionType.type}`,
        {
          value: value.toLocaleString(),
          formatted: aggregateFormatter(value),
        },
      ]),
    );
  }, [aggregatedValues, aggregateFormatter]);

  const legendLabels = toLegendLabelData(seriesColorMap, "points", sortLegend)
    .filter(
      (legendLabel) =>
        linesWithData[legendLabel.groupVal.value] === legendLabel.groupVal.type,
    )
    .map((legendLabel) => {
      const key = `${legendLabel.groupVal.value}:${legendLabel.groupVal.type}`;
      const aggregate = valueMap[key];

      return {
        ...legendLabel,
        label: getMetricsLabel(legendLabel.groupVal.value),
        aggregatedValue: aggregate?.formatted ?? aggregate?.value,
      };
    });

  return (
    <Tooltip
      open={legendLabels.length > maxLegendItems ? undefined : false}
      delayDuration={50}
    >
      <TooltipTrigger asChild>
        <div
          className={cn("ml-7")}
          onMouseEnter={() => setIsLegendHovered(true)}
          onMouseLeave={() => setIsLegendHovered(false)}
        >
          <LegendLabels
            legendLabelData={legendLabels.slice(0, maxLegendItems)}
            highlightState={highlightState}
            addSelected={addSelected}
            removeSelected={removeSelected}
            setSelected={setSelected}
            highlight={highlight}
            clearHighlight={clearHighlight}
            isLegendHovered={isLegendHovered}
            width={width}
          />
          {legendLabels.length > maxLegendItems && (
            <div className="text-right text-xs text-primary-500">
              {legendLabels.length - maxLegendItems} more
            </div>
          )}
        </div>
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContent
          className="rounded-md border-0 shadow-md duration-0 bg-primary-50 data-[side=top]:duration-0"
          side="top"
          align="end"
          alignOffset={-12}
          sideOffset={6}
          style={{
            // make the tooltip appear over top of the content
            transform: "translate(0, 100%)",
            maxHeight: "calc(100vh - 240px)",
          }}
          avoidCollisions={false}
          onMouseEnter={() => setIsLegendHovered(true)}
          onMouseLeave={() => setIsLegendHovered(false)}
        >
          <LegendLabels
            legendLabelData={legendLabels}
            highlightState={highlightState}
            addSelected={addSelected}
            removeSelected={removeSelected}
            setSelected={setSelected}
            highlight={highlight}
            clearHighlight={clearHighlight}
            isLegendHovered={isLegendHovered}
            width={Math.max(width, 400)} // up to 400px wide in portal
          />
        </TooltipContent>
      </TooltipPortal>
    </Tooltip>
  );
};

MonitorChartLegend.displayName = "MonitorChartLegend";
export { MonitorChartLegend };

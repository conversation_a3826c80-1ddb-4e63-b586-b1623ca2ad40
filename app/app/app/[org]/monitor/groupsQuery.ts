import * as Query from "#/utils/btql/query-builder";
import { useMemo } from "react";
import { useBtql } from "#/utils/btql/btql";
import { CreatedField } from "#/utils/duckdb";
import { getMonitorBtqlArgs } from "./use-monitor-query";

function uniqueMetadataKeys(
  data?: { metadata_key: string }[],
): string[] | undefined {
  return data?.map((item) => item.metadata_key);
}

export function useGroupsQuery({
  projectId,
  startTime,
  from,
  experimentIds,
  enabled = true,
}: {
  projectId: string | null;
  startTime: string;
  from: "experiment" | "project_logs";
  experimentIds: string[];
  enabled?: boolean;
}): { data: string[] | undefined; loading: boolean } {
  const { data, loading } = useBtql({
    name: "Groups query",
    query: useMemo(
      () =>
        projectId && enabled
          ? {
              from:
                from === "experiment"
                  ? Query.from("experiment", experimentIds)
                  : Query.from("project_logs", [projectId]),
              unpivot: [
                {
                  alias: ["metadata_key", "key"],
                  expr: {
                    btql: "metadata",
                  },
                },
              ],
              dimensions: [
                { alias: "metadata_key", expr: { btql: "metadata_key" } },
              ],
              filter:
                // When we are querying experiments, experimentIds has already
                // been filtered by start time so there's no need to pass it to BTQL
                from === "project_logs"
                  ? Query.and({
                      op: "gt",
                      left: Query.ident(CreatedField),
                      right: {
                        op: "literal",
                        value: startTime,
                      },
                    })
                  : undefined,
            }
          : null,
      [projectId, startTime, from, experimentIds, enabled],
    ),
    ...getMonitorBtqlArgs({
      startTime,
      projectIds: projectId ? [projectId] : [],
    }),
  });
  return {
    data: uniqueMetadataKeys(data?.toArray().map((r) => r.toJSON())),
    loading,
  };
}

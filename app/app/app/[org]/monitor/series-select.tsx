import { Combobox } from "#/ui/combobox/combobox";
import { Button } from "#/ui/button";
import { getMetricsLabel } from "./utils";
import { Settings2 } from "lucide-react";

function SeriesSelect<T extends string>({
  selectedSeries,
  seriesNames,
  onChange,
  contentClassName,
}: {
  selectedSeries: T[];
  seriesNames: T[];
  onChange: (seriesId: T) => void;
  contentClassName?: string;
}) {
  return (
    <Combobox<{
      label: string;
      value: T;
      description?: string | null;
    }>
      contentClassName={contentClassName}
      noSearch
      stayOpenOnChange
      variant="button"
      align="start"
      selectedValues={selectedSeries ?? undefined}
      onChange={(_seriesName, option) => {
        if (option.value) {
          onChange(option.value);
        }
      }}
      options={seriesNames.map((series) => ({
        label: getMetricsLabel(series) ?? "",
        value: series,
      }))}
    >
      <Button Icon={Settings2} size="xs" />
    </Combobox>
  );
}

export { SeriesSelect };

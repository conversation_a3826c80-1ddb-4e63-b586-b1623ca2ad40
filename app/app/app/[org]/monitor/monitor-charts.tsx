import { useFullscreenMonitorChartState } from "#/ui/query-parameters";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useMemo, type PropsWithChildren } from "react";
import { CostChart } from "./charts/cost";
import { LatencyChart } from "./charts/latency";
import { RequestCountChart } from "./charts/request-count";
import { ScoresChart } from "./charts/scores";
import { TokenCountChart } from "./charts/token-count";
import { TTFTChart } from "./charts/ttft";
import { useCostQuery } from "./costQuery";
import { EmptyMonitorState } from "./empty-monitor-state";
import { useLatencyQuery } from "./latencyQuery";
import { useRequestCountQuery } from "./requestCountQuery";
import { useScoresQuery } from "./scoresQuery";
import { type ChartTimeFrame } from "./time-controls/time-range";
import { useTokenCountQuery } from "./tokenCountQuery";
import { useTTFTQuery } from "./ttftQuery";
import { useToolsQuery } from "./toolsQuery";
import { ToolCountChart } from "./charts/tools";

const ChartContainer = ({ children }: PropsWithChildren) => {
  return <div className="flex flex-1 flex-col gap-4">{children}</div>;
};

function MonitorCharts({
  chartTimeFrame,
  timeBucket,
  projectIds,
  groupBy,
  from,
  experimentIds,
  onBrush,
  isLoading: isLoadingProp,
}: {
  chartTimeFrame: ChartTimeFrame;
  timeBucket: "minute" | "hour" | "day";
  projectIds: string[];
  groupBy?: string;
  from: "project_logs" | "experiment";
  experimentIds: string[];
  onBrush: (v: [number, number] | null) => void;
  isLoading?: boolean;
}) {
  const [fullscreenChartState] = useFullscreenMonitorChartState();

  const { costData, loading: isFetchingCost } = useCostQuery(projectIds, {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  });

  const {
    data: latencyData,
    loading: isFetchingLatency,
    error: latencyQueryError,
  } = useLatencyQuery(projectIds, {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  });

  const { data: tokenCountData, loading: isFetchingTokenCount } =
    useTokenCountQuery(projectIds, {
      chartTimeFrame,
      timeBucket,
      groupBy,
      experimentIds,
      from,
    });

  const { data: ttftData, loading: isFetchingTTFT } = useTTFTQuery(projectIds, {
    chartTimeFrame,
    timeBucket,
    groupBy,
    experimentIds,
    from,
  });

  const { data: scoresData, loading: isFetchingScores } = useScoresQuery(
    projectIds,
    {
      chartTimeFrame,
      timeBucket,
      from,
      groupBy,
      experimentIds,
    },
  );

  const { data: requestCountData, loading: isFetchingRequestCount } =
    useRequestCountQuery(projectIds, {
      chartTimeFrame,
      timeBucket,
      groupBy,
      experimentIds,
      from,
    });

  const isLoading = [
    isLoadingProp,
    isFetchingCost,
    isFetchingLatency,
    isFetchingTokenCount,
    isFetchingTTFT,
    isFetchingScores,
    isFetchingRequestCount,
  ].some((loading) => loading);

  const isEmpty = [
    costData,
    latencyData,
    tokenCountData,
    ttftData,
    scoresData,
    requestCountData,
  ].every((data) => !data || data?.length === 0);

  const { data: toolData, loading: isLoadingTools } = useToolsQuery(
    projectIds,
    {
      chartTimeFrame,
      timeBucket,
      groupBy,
      experimentIds,
      from,
    },
  );

  const commonProps = useMemo(() => {
    return {
      timeBucket,
      chartTimeFrame,
      isFetchingData: isLoading,
      groupBy,
      from,
      fillEmptyTimeSlices: true,
      onBrush,
    };
  }, [timeBucket, chartTimeFrame, isLoading, groupBy, from, onBrush]);

  const isToolsEmpty = !toolData || toolData.length === 0;

  if (isEmpty && !isLoading) {
    return (
      <EmptyMonitorState
        rowType={from === "experiment" ? "experiment" : "logs"}
      />
    );
  } else if (
    latencyQueryError &&
    latencyQueryError.message.includes("too costly")
  ) {
    return (
      <TableEmptyState label="The monitor page is currently unavailable when running a self-hosted setup with Postgres-only" />
    );
  }

  const latency = <LatencyChart {...commonProps} data={latencyData} />;

  if (fullscreenChartState === "latency") {
    return <ChartContainer>{latency}</ChartContainer>;
  }

  const tokenCount = <TokenCountChart {...commonProps} data={tokenCountData} />;

  if (fullscreenChartState === "token-count") {
    return <ChartContainer>{tokenCount}</ChartContainer>;
  }

  const cost = <CostChart {...commonProps} data={costData} />;

  if (fullscreenChartState === "cost") {
    return <ChartContainer>{cost}</ChartContainer>;
  }

  const ttft = <TTFTChart {...commonProps} data={ttftData} />;

  if (fullscreenChartState === "ttft") {
    return <ChartContainer>{ttft}</ChartContainer>;
  }

  const requestCount = (
    <RequestCountChart
      {...commonProps}
      data={requestCountData}
      fillEmptyTimeSlices={false}
    />
  );

  if (fullscreenChartState === "request-count") {
    return <ChartContainer>{requestCount}</ChartContainer>;
  }

  const scores = (
    <ScoresChart
      {...commonProps}
      data={scoresData}
      fillEmptyTimeSlices={false}
    />
  );

  if (fullscreenChartState === "scores") {
    return <ChartContainer>{scores}</ChartContainer>;
  }

  const toolCount = (
    <ToolCountChart
      {...commonProps}
      data={toolData}
      isFetchingData={isLoading || isLoadingTools}
      toolMetric="count"
    />
  );

  if (fullscreenChartState === "tool_count") {
    return <ChartContainer>{toolCount}</ChartContainer>;
  }

  const toolErrorRate = (
    <ToolCountChart
      {...commonProps}
      data={toolData}
      isFetchingData={isLoading || isLoadingTools}
      toolMetric="error_rate"
    />
  );

  if (fullscreenChartState === "tool_error_rate") {
    return <ChartContainer>{toolErrorRate}</ChartContainer>;
  }

  const toolDuration = (
    <ToolCountChart
      {...commonProps}
      data={toolData}
      isFetchingData={isLoading || isLoadingTools}
      toolMetric="p50_duration"
    />
  );

  if (fullscreenChartState === "tool_p50_duration") {
    return <ChartContainer>{toolDuration}</ChartContainer>;
  }

  const chartContainerClassName = "flex h-48 md:h-64 md:flex-1 flex-col";

  return (
    <div className="flex flex-1 flex-col gap-4">
      <div className="flex flex-none flex-col gap-4 md:flex-row">
        <div className={chartContainerClassName}>{requestCount}</div>
        <div className={chartContainerClassName}>{latency}</div>
      </div>
      <div className="flex flex-none flex-col gap-4 md:flex-row">
        <div className={chartContainerClassName}>{cost}</div>
        <div className={chartContainerClassName}>{tokenCount}</div>
      </div>
      <div className="flex flex-none flex-col gap-4 md:flex-row">
        <div className={chartContainerClassName}>{ttft}</div>
        <div className={chartContainerClassName}>{scores}</div>
      </div>
      {!isToolsEmpty && (
        <>
          <div className="flex flex-none flex-col gap-4 md:flex-row">
            <div className={chartContainerClassName}>{toolCount}</div>
            <div className={chartContainerClassName}>{toolErrorRate}</div>
          </div>
          <div className="flex flex-none flex-col gap-4 md:flex-row">
            <div className={chartContainerClassName}>{toolDuration}</div>
            <div className={chartContainerClassName} />
          </div>
        </>
      )}
    </div>
  );
}

export { MonitorCharts };

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Bolt, Timer } from "lucide-react";
import { MetricsChart } from "./metrics-chart";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { getToolNames, type ToolsData } from "../toolsQuery";
import { useMemo } from "react";
import { type ToolMetric } from "../groups";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";

function ToolCountChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  fillEmptyTimeSlices = false,
  chartTimeFrame,
  from,
  toolMetric,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: ToolsData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  toolMetric: ToolMetric;
  onBrush: (v: [number, number] | null) => void;
}) {
  const toolNames = useMemo(() => (data ? getToolNames(data) : []), [data]);

  return (
    <MetricsChart<ToolsData, string>
      chartId={`tool_${toolMetric}`}
      title={
        toolMetric === "count"
          ? "Tool executions"
          : toolMetric === "error_rate"
            ? "Tool error rate"
            : "Tool duration (p50)"
      }
      icon={
        toolMetric === "count"
          ? Bolt
          : toolMetric === "error_rate"
            ? AlertCircle
            : Timer
      }
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={toolNames}
      availableSeries={toolNames}
      contentClassName="w-36"
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      chartTimeFrame={chartTimeFrame}
      from={from}
      aggregateType="sum"
      aggregateFormatter={(value) =>
        value.toLocaleString(undefined, {
          notation: "compact",
          compactDisplay: "short",
          maximumFractionDigits: 1,
        })
      }
      toolMetric={toolMetric}
      tickFormatter={
        toolMetric === "p50_duration"
          ? (v) =>
              formatValueForSelectionType(v, {
                value: "duration",
                type: "metric",
              })
          : undefined
      }
      onBrush={onBrush}
    />
  );
}

export { ToolCountChart };

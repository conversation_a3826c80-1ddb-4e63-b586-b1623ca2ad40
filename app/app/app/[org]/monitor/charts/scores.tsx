import { Percent } from "lucide-react";
import { type ScoresData } from "../scoresQuery";
import { MetricsChart } from "./metrics-chart";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";
import { getScoreNames } from "../scoresQuery";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { useMemo } from "react";

function ScoresChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  fillEmptyTimeSlices = false,
  chartTimeFrame,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: ScoresData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  const scoreNames = useMemo(() => (data ? getScoreNames(data) : []), [data]);

  return (
    <MetricsChart<ScoresData, string>
      title="Scores"
      chartId="scores"
      icon={Percent}
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={scoreNames}
      availableSeries={scoreNames}
      tickFormatter={(v) =>
        formatValueForSelectionType(v, { value: "scoreName", type: "score" })
      }
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      chartTimeFrame={chartTimeFrame}
      from={from}
      aggregateType="average"
      aggregateFormatter={(value) =>
        formatValueForSelectionType(value, {
          value: "scoreName",
          type: "score",
        })
      }
      onBrush={onBrush}
    />
  );
}

export { ScoresChart };

import { MessageCircle } from "lucide-react";
import { MetricsChart } from "./metrics-chart";
import {
  type RequestCountMetrics,
  type RequestCountMetricsData,
} from "../requestCountQuery";
import { type ChartTimeFrame } from "../time-controls/time-range";

function RequestCountChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  fillEmptyTimeSlices = false,
  chartTimeFrame,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: RequestCountMetricsData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  return (
    <MetricsChart<RequestCountMetricsData, RequestCountMetrics>
      title="Request count"
      chartId="request-count"
      icon={MessageCircle}
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={["count", "spans", "llm_count", "tool_count"]}
      availableSeries={["count", "spans", "llm_count", "tool_count"]}
      contentClassName="w-36"
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      chartTimeFrame={chartTimeFrame}
      from={from}
      aggregateType="sum"
      aggregateFormatter={(value) =>
        value.toLocaleString(undefined, {
          notation: "compact",
          compactDisplay: "short",
          maximumFractionDigits: 1,
        })
      }
      onBrush={onBrush}
    />
  );
}

export { RequestCountChart };

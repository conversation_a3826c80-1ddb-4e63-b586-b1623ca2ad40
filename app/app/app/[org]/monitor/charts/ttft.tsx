import { Clock } from "lucide-react";
import { type TTFTMetricsData, type TTFTMetrics } from "../ttftQuery";
import { MetricsChart } from "./metrics-chart";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";
import { type ChartTimeFrame } from "../time-controls/time-range";

function TTFTChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  chartTimeFrame,
  fillEmptyTimeSlices,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: TTFTMetricsData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  chartTimeFrame: ChartTimeFrame;
  fillEmptyTimeSlices: boolean;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  return (
    <MetricsChart<TTFTMetricsData, TTFTMetrics>
      title="Time to first token"
      chartId="ttft"
      icon={Clock}
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={["p50_time_to_first_token", "p95_time_to_first_token"]}
      availableSeries={["p50_time_to_first_token", "p95_time_to_first_token"]}
      tickFormatter={(v) =>
        formatValueForSelectionType(v, { value: "duration", type: "metric" })
      }
      chartTimeFrame={chartTimeFrame}
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      from={from}
      aggregateType="average"
      aggregateFormatter={(value) =>
        formatValueForSelectionType(value, {
          value: "duration",
          type: "metric",
        })
      }
      onBrush={onBrush}
    />
  );
}

export { TTFTChart };

import { Snail } from "lucide-react";
import { type LatencyMetricsData, type DurationMetrics } from "../latencyQuery";
import { MetricsChart } from "./metrics-chart";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";
import { type ChartTimeFrame } from "../time-controls/time-range";

function LatencyChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  fillEmptyTimeSlices = false,
  chartTimeFrame,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: LatencyMetricsData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  return (
    <MetricsChart<LatencyMetricsData, DurationMetrics>
      title="Latency"
      icon={Snail}
      chartId="latency"
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={["p50_duration", "p95_duration"]}
      availableSeries={["p50_duration", "p95_duration"]}
      tickFormatter={(v) =>
        formatValueForSelectionType(v, { value: "duration", type: "metric" })
      }
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      chartTimeFrame={chartTimeFrame}
      from={from}
      aggregateType="average"
      aggregateFormatter={(value) =>
        formatValueForSelectionType(value, {
          value: "duration",
          type: "metric",
        })
      }
      onBrush={onBrush}
    />
  );
}

export { LatencyChart };

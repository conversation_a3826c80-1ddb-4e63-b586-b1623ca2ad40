import { DollarSign } from "lucide-react";
import { MetricsChart } from "./metrics-chart";
import { type CostSummary } from "../costs";
import { formatValueForSelectionType } from "#/ui/charts/selectionTypes";
import { type ChartTimeFrame } from "../time-controls/time-range";

type CostMetrics = Extract<
  keyof CostSummary,
  | "totalCost"
  | "promptUncachedTokensCost"
  | "promptCachedTokensCost"
  | "promptCacheCreationTokensCost"
  | "completionTokensCost"
>;

const ALL_SERIES: CostMetrics[] = [
  "promptUncachedTokensCost",
  "promptCachedTokensCost",
  "promptCacheCreationTokensCost",
  "completionTokensCost",
  "totalCost",
];

function CostChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  fillEmptyTimeSlices = false,
  chartTimeFrame,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: CostSummary[] | null;
  isFetchingData: boolean;
  groupBy?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  return (
    <MetricsChart<CostSummary, CostMetrics>
      title="Total LLM cost"
      chartId="cost"
      icon={DollarSign}
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={ALL_SERIES}
      availableSeries={ALL_SERIES}
      tickFormatter={(v) =>
        formatValueForSelectionType(v, { value: "cost", type: "metric" })
      }
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      chartTimeFrame={chartTimeFrame}
      from={from}
      aggregateType="sum"
      aggregateFormatter={(value) =>
        formatValueForSelectionType(value, { value: "cost", type: "metric" })
      }
      onBrush={onBrush}
    />
  );
}

export { CostChart };

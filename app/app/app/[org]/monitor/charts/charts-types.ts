interface ChartTypeConfig {
  /** The x axis represents the timeframe */
  xAxisIsTimeFrame: boolean;
}

export type ChartType = "timeseries-lines" | "scatterplot";

const chartTypeConfig: Record<ChartType, ChartTypeConfig> = {
  "timeseries-lines": {
    xAxisIsTimeFrame: true,
  },
  scatterplot: {
    xAxisIsTimeFrame: false,
  },
};

export const isXAxisTime = (chartType: ChartType): boolean => {
  return chartTypeConfig[chartType].xAxisIsTimeFrame;
};

import { Blocks } from "lucide-react";
import {
  type TokenCountMetricsData,
  type TokenMetrics,
} from "../tokenCountQuery";
import { MetricsChart } from "./metrics-chart";
import { type ChartTimeFrame } from "../time-controls/time-range";

const ALL_SERIES: TokenMetrics[] = [
  "sum_prompt_uncached_tokens",
  "sum_prompt_cached_tokens",
  "sum_prompt_cache_creation_tokens",
  "sum_completion_tokens",
  "sum_tokens",
];

function TokenCountChart({
  timeBucket,
  data,
  isFetchingData,
  groupBy,
  chartTimeFrame,
  fillEmptyTimeSlices,
  from,
  onBrush,
}: {
  timeBucket: "minute" | "hour" | "day";
  data: TokenCountMetricsData[] | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  chartTimeFrame: ChartTimeFrame;
  fillEmptyTimeSlices: boolean;
  from: "experiment" | "project_logs";
  onBrush: (v: [number, number] | null) => void;
}) {
  return (
    <MetricsChart<TokenCountMetricsData, TokenMetrics>
      title="Token count"
      chartId="token-count"
      icon={Blocks}
      timeBucket={timeBucket}
      data={data}
      isFetchingData={isFetchingData}
      groupBy={groupBy}
      defaultSeries={ALL_SERIES}
      availableSeries={ALL_SERIES}
      contentClassName="w-36"
      chartTimeFrame={chartTimeFrame}
      fillEmptyTimeSlices={fillEmptyTimeSlices}
      from={from}
      aggregateType="sum"
      aggregateFormatter={(value) => value.toLocaleString()}
      onBrush={onBrush}
    />
  );
}

export { TokenCountChart };

// delete all of this???
import { But<PERSON> } from "#/ui/button";
import { useHighlightState } from "#/ui/charts/highlight";
import { Skeleton } from "#/ui/skeleton";
import { Expand, Shrink, type LucideIcon } from "lucide-react";
import { useState, useMemo, useCallback } from "react";
import { useOrg } from "#/utils/user";
import {
  getDataSeries,
  getGroupedDataSeries,
  getGroupedSeriesNames,
  getGroupNames,
  groupData,
  type ToolMetric,
  type MetricsData,
} from "../groups";
import { SeriesSelect } from "../series-select";
import { getTracesLink } from "../getMonitorLink";
import { useRouter } from "next/navigation";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { useFullscreenMonitorChartState } from "#/ui/query-parameters";
import { parseAsString, useQueryState } from "nuqs";
import { TimeseriesChart } from "#/ui/charts/timeseries/timeseries-chart";
import { metricsDataToTimeseriesData } from "#/ui/charts/timeseries-data/metrics-data-to-timeseries-data";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { fillEmptyDataPoints } from "#/ui/charts/multi-line-chart";
import { scaleTime } from "#/ui/charts/scale-time";

interface SeriesMetadataExtension {
  name: string;
  selectionType: SelectionType;
}

type MetricsChartProps<TData, TSeries extends string> = {
  title: string;
  chartId: string;
  icon: LucideIcon;
  timeBucket: "minute" | "hour" | "day";
  data: TData[] | null | undefined;
  isFetchingData: boolean;
  groupBy?: string;
  defaultSeries: TSeries[];
  availableSeries: TSeries[];
  tickFormatter?: (v: number, i: number) => string | null;
  contentClassName?: string;
  fillEmptyTimeSlices?: boolean;
  chartTimeFrame: ChartTimeFrame;
  from: "experiment" | "project_logs";
  aggregateType: "sum" | "average";
  aggregateFormatter: (value: number) => string;
  onBrush: (v: [number, number] | null) => void;
  toolMetric?: ToolMetric;
};

type LoadedDataMetricChartProps<TData, TSeries extends string> = Omit<
  MetricsChartProps<TData, TSeries>,
  "data"
> & {
  data: TData[];
};

function MetricsChartContent<
  TData extends MetricsData,
  TSeries extends string,
>({
  title,
  chartId,
  icon: Icon,
  timeBucket,
  data,
  groupBy,
  defaultSeries,
  availableSeries,
  tickFormatter,
  contentClassName = "w-32",
  chartTimeFrame,
  from,
  aggregateType,
  aggregateFormatter,
  toolMetric,
  onBrush,
  isFetchingData,
  fillEmptyTimeSlices,
}: LoadedDataMetricChartProps<TData, TSeries>) {
  const org = useOrg();
  const orgName = org.name;

  const router = useRouter();
  const [projectId] = useQueryState("projectId", parseAsString);
  const highlightState = useHighlightState();

  const [selectedSeries, setSelectedSeries] =
    useState<TSeries[]>(defaultSeries);

  const { seriesData, allSeriesNames } = useMemo(() => {
    const groupedData = groupBy ? groupData(data) : {};
    const allGroupNames = getGroupNames(groupedData);

    const seriesData = groupBy
      ? getGroupedDataSeries({
          groupedData,
          groupNames: allGroupNames,
          selectedSeries,
          toolMetric,
        })
      : getDataSeries({ data, selectedSeries, toolMetric });

    const allSeriesNames = groupBy
      ? getGroupedSeriesNames({
          groupNames: allGroupNames,
          selectedSeries: availableSeries,
        })
      : availableSeries;

    return { seriesData, allSeriesNames };
  }, [groupBy, data, selectedSeries, toolMetric, availableSeries]);

  const timeseriesData = useMemo(() => {
    const seriesMeta: SeriesMetadataExtension[] = allSeriesNames.map((v) => ({
      name: v,
      selectionType: {
        value: v,
        type: "score",
      },
    }));

    const filledSeriesData =
      seriesData.length === 0
        ? []
        : fillEmptyTimeSlices
          ? fillEmptyDataPoints({
              data: seriesData,
              xTimeRange: {
                allXValues: scaleTime({
                  startTime: new Date(chartTimeFrame.start).toISOString(),
                  totalDurationMS: chartTimeFrame.end - chartTimeFrame.start,
                  timeBucket,
                }),
                getEmptyMetadata: (time: number) => ({
                  time: new Date(time).toISOString(),
                  count: BigInt(0),
                }),
              },
              numberOfSeries: seriesMeta.length,
            })
          : seriesData;

    return metricsDataToTimeseriesData<SeriesMetadataExtension>(
      filledSeriesData,
      seriesMeta,
    );
  }, [
    seriesData,
    allSeriesNames,
    chartTimeFrame.start,
    chartTimeFrame.end,
    timeBucket,
    fillEmptyTimeSlices,
  ]);

  const onChartClick = useCallback(
    async (event: React.MouseEvent, timestamp: number) => {
      if (!projectId) return;

      const url = await getTracesLink({
        orgName: orgName,
        projectId: projectId,
        from,
        timeBucket,
        time: new Date(timestamp).toISOString(),
      });

      if (event.metaKey || event.ctrlKey || event.button === 1) {
        window.open(url, "_blank");
      } else {
        router.push(url);
      }
    },
    [orgName, projectId, from, router, timeBucket],
  );

  const [fullscreenChart, setFullscreenChart] =
    useFullscreenMonitorChartState();
  const isFullscreen = fullscreenChart === chartId;

  return (
    <div className="flex flex-1 flex-col rounded-lg border p-2 bg-primary-50 border-primary-100">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex flex-1 items-center gap-2 text-sm text-primary-800">
          <Icon className="size-4" />
          {title}
        </div>
        {highlightState.state.selected.length > 0 && (
          <Button
            size="xs"
            variant="ghost"
            className="text-primary-500"
            onClick={() => {
              highlightState.clearSelected();
            }}
          >
            Reset
          </Button>
        )}
        <Button
          variant="ghost"
          size="xs"
          className="text-primary-600"
          onClick={() => {
            setFullscreenChart(isFullscreen ? null : chartId);
          }}
          Icon={isFullscreen ? Shrink : Expand}
        />
        {data.length > 0 && (
          <SeriesSelect<TSeries>
            contentClassName={contentClassName}
            selectedSeries={selectedSeries}
            seriesNames={availableSeries}
            onChange={(series) => {
              if (selectedSeries.includes(series)) {
                setSelectedSeries(selectedSeries.filter((s) => s !== series));
              } else {
                // Keep same order as availableSeries by filtering availableSeries
                setSelectedSeries(
                  availableSeries.filter((s) =>
                    [...selectedSeries, series].includes(s),
                  ),
                );
              }
            }}
          />
        )}
      </div>
      <TimeseriesChart
        timeseriesData={timeseriesData}
        chartTimeFrame={chartTimeFrame}
        highlightState={highlightState}
        aggregateType={aggregateType}
        aggregateFormatter={aggregateFormatter}
        tickFormatter={tickFormatter}
        sortLegend={groupBy !== undefined}
        onClick={projectId ? onChartClick : undefined}
        onBrush={onBrush}
        isFetchingData={isFetchingData}
      />
    </div>
  );
}

function MetricsChart<TData extends MetricsData, TSeries extends string>(
  props: MetricsChartProps<TData, TSeries>,
) {
  const { data, isFetchingData, ...rest } = props;

  if (!data) {
    return <Skeleton className="min-h-48 flex-1" />;
  }

  return (
    <MetricsChartContent
      data={data}
      isFetchingData={isFetchingData}
      {...rest}
    />
  );
}

export { MetricsChart };

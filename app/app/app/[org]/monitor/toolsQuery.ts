import { useMemo } from "react";
import { type ParsedQuery, type Expr } from "@braintrust/btql/parser";
import * as Query from "#/utils/btql/query-builder";
import { CreatedField } from "#/utils/duckdb";
import { useBtql } from "#/utils/btql/btql";
import { getMonitorBtqlArgs } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

type QueryParams = {
  chartTimeFrame: ChartTimeFrame;
  timeBucket: "minute" | "hour" | "day";
  from: "experiment" | "project_logs";
  groupBy?: string;
  experimentIds: string[];
};

export function getToolNames(dataArray: ToolsData[]): string[] {
  const uniqueKeys = new Set<string>();

  for (const item of dataArray) {
    if (item.tools) {
      Object.keys(item.tools).forEach((key) => uniqueKeys.add(key));
    }
  }

  return Array.from(uniqueKeys);
}

export type ToolsData = {
  time: number;
  count: bigint;
  error_rate: number;
  p50_duration: number | null;
  tools: Record<
    string,
    {
      count: bigint;
      error_rate: number;
      p50_duration: number | null;
    }
  >;
  group?: string | null;
};

export function useToolsQuery(
  projectIds: string[],
  { chartTimeFrame, timeBucket, groupBy, experimentIds, from }: QueryParams,
): {
  data: ToolsData[] | undefined;
  loading: boolean;
  error: Error | undefined;
} {
  const startTime = new Date(chartTimeFrame.start).toISOString();
  const endTime = new Date(chartTimeFrame.end).toISOString();
  const query = useMemo<ParsedQuery | null>(() => {
    const filters: Expr[] = [
      {
        op: "eq",
        left: Query.ident("span_attributes", "type"),
        right: {
          op: "literal",
          value: "tool",
        },
      },
      {
        op: "gt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: startTime,
        },
      },
    ];

    filters.push({
      op: "lt",
      left: Query.ident(CreatedField),
      right: {
        op: "literal",
        value: endTime,
      },
    });

    if (projectIds.length === 0) {
      return null;
    }

    const fromClause =
      from === "experiment"
        ? Query.from("experiment", experimentIds)
        : Query.from("project_logs", projectIds);

    const dimensions: ParsedQuery["dimensions"] = [
      // {
      //   alias: "tool_name",
      //   expr: { btql: "span_attributes.name" },
      // },
      {
        alias: "time",
        expr: {
          op: "function",
          name: Query.ident(timeBucket),
          args: [Query.ident(CreatedField)],
        },
      },
    ];

    if (groupBy) {
      dimensions.push({
        alias: "group",
        expr: { btql: `metadata.${groupBy}` },
      });
    }

    const durationExpr = "metrics.end-metrics.start";

    return {
      from: fromClause,
      measures: [
        { alias: "last_updated", expr: { btql: "max(created)" } },
        { alias: "count", expr: { btql: "count(1)" } },
        {
          alias: "error_rate",
          expr: { btql: "count(error)/count(1)" },
        },
        {
          alias: "p50_duration",
          expr: { btql: `percentile(${durationExpr}, 0.5)` },
        },
      ],
      dimensions,
      pivot: [{ alias: "tools", expr: { btql: "span_attributes.name" } }],
      filter: Query.and(...filters),
      sort: [{ expr: { btql: "time" }, dir: "asc" }],
    };
  }, [
    projectIds,
    startTime,
    endTime,
    experimentIds,
    from,
    timeBucket,
    groupBy,
  ]);
  const { data, loading, error } = useBtql({
    query,
    name: "Tools query",
    ...getMonitorBtqlArgs({ startTime, projectIds }),
  });
  return {
    data: data?.toArray().map((r) => r.toJSON()),
    loading,
    error,
  };
}

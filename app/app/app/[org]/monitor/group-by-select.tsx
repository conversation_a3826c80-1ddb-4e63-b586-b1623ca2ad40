import { useQuery } from "@tanstack/react-query";
import { useGroupsQuery } from "./groupsQuery";
import { StretchHorizontal } from "lucide-react";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { fetchInferBtql } from "#/utils/btql/btql";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { Combobox } from "#/ui/combobox/combobox";
import { useMemo } from "react";
import { useIsClient } from "#/utils/use-is-client";
import { escapeIdentPath } from "#/utils/btql/path-helpers";

export const NONE_GROUP = "NONE";

function GroupBySelect({
  projectId,
  startTime,
  value,
  onChange,
  from,
  experimentIds,
}: {
  projectId: string;
  startTime: string;
  value: string;
  onChange: (newValue: string | null) => void;
  from: "experiment" | "project_logs";
  experimentIds: string[];
}) {
  const org = useOrg();
  const { flags } = useFeatureFlags();
  const { getOrRefreshToken } = useSessionToken();

  const { data, loading: groupsLoading } = useGroupsQuery({
    projectId,
    startTime,
    from,
    experimentIds,
    // Disable the groups query when schema inference is enabled
    enabled: !flags.schemaInference,
  });

  const { data: inferSchemaData, isLoading: inferSchemaLoading } = useQuery({
    queryKey: ["inferMetadataPathsSchema", org.id, projectId],
    queryFn: async ({ signal }: { signal: AbortSignal }) =>
      await fetchInferBtql({
        args: {
          query: {
            from:
              from === "experiment"
                ? Query.from("experiment", experimentIds)
                : Query.from("project_logs", [projectId]),
            infer: [
              { op: "ident", name: ["metadata"] },
              // NOTE: These should be used as measures, not groupings, but leaving this
              // commented out in case we still want to infer them?
              // { op: "ident", name: ["scores"] },
              // { op: "ident", name: ["metrics"] },
            ],
          },
          brainstoreRealtime: true,
        },
        flags,
        apiUrl: org.api_url,
        getOrRefreshToken,
        signal,
      }),
    gcTime: 1000 * 30,
    enabled: flags.schemaInference,
  });

  const options = useMemo(() => {
    // If schema inference is enabled, only use the inferred schema data
    if (flags.schemaInference) {
      return (
        inferSchemaData?.data?.map((item) => ({
          label: item.name.join("."),
          // The code that consumes this dropdown assumes that every field is a sub-field of
          // "metadata." so we slice here to remove it as a prefix. Eventually, we should probably
          // generalize that code.
          value: escapeIdentPath(item.name.slice(1)),
        })) ?? []
      );
    }

    // Otherwise, use only the groups data
    return (
      data?.map((item) => ({
        label: item,
        value: item,
      })) ?? []
    );
  }, [inferSchemaData, data, flags.schemaInference]);

  const isClient = useIsClient();

  return (
    <Combobox<{
      label: string;
      value: string;
    }>
      isLoading={
        isClient && (flags.schemaInference ? inferSchemaLoading : groupsLoading)
      }
      variant="button"
      buttonSize="xs"
      disabled={!isClient || options.length === 0}
      searchPlaceholder="Select grouping"
      selectedValue={value}
      onChange={(newValue) => {
        if (newValue === NONE_GROUP || newValue === value) {
          onChange(null);
          return;
        }

        onChange(newValue ?? "");
      }}
      options={options}
      renderComboboxDisplayLabel={({ label, value }) => {
        return <ButtonLabel label={value === "" ? undefined : label} />;
      }}
      placeholderLabel={<ButtonLabel />}
    />
  );
}

const ButtonLabel = ({ label }: { label?: string }) => {
  return (
    <span className="flex items-center gap-1 text-xs font-normal">
      <StretchHorizontal className="size-3" />
      {label && label !== "None" ? `Group by ${label}` : "Group"}
    </span>
  );
};

export { GroupBySelect };

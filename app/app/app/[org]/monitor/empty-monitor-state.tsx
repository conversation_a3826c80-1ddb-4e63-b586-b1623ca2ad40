import { ExternalLink } from "#/ui/link";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { LineChart } from "lucide-react";
import React from "react";
import { type RowType } from "./row-type-select";

export const EmptyMonitorState = ({ rowType }: { rowType: RowType }) => {
  if (rowType === "logs") {
    return (
      <TableEmptyState
        className="flex-1"
        Icon={LineChart}
        label={
          <>
            No logs monitoring data available in the specified time range. Try
            adjusting the filters or selecting a different metric.{" "}
            <p className="pt-4">
              To get started with logs,{" "}
              <ExternalLink href={"/docs/guides/logging"}>
                read this guide
              </ExternalLink>
              .
            </p>
          </>
        }
      />
    );
  }

  if (rowType === "experiment") {
    return (
      <TableEmptyState
        className="flex-1"
        Icon={LineChart}
        label={
          <>
            No experiment monitoring data available in the specified time range.
            Try adjusting the filters or selecting a different metric.{" "}
            <p className="pt-4">
              To get started with experiments,{" "}
              <ExternalLink
                href={"/docs/welcome/start#create-a-simple-evaluation-script"}
              >
                read this guide
              </ExternalLink>
              .
            </p>
          </>
        }
      />
    );
  }

  return null;
};

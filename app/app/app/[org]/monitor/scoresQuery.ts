import { useMemo } from "react";
import { type ParsedQuery, type Expr } from "@braintrust/btql/parser";
import * as Query from "#/utils/btql/query-builder";
import { CreatedField } from "#/utils/duckdb";
import { useBtql } from "#/utils/btql/btql";
import { getMonitorBtqlArgs } from "./use-monitor-query";
import { type ChartTimeFrame } from "./time-controls/time-range";

type QueryParams = {
  chartTimeFrame: ChartTimeFrame;
  timeBucket: "minute" | "hour" | "day";
  from: "experiment" | "project_logs";
  groupBy?: string;
  experimentIds: string[];
};

export function getScoreNames(dataArray: ScoresData[]): string[] {
  const uniqueKeys = new Set<string>();

  for (const item of dataArray) {
    if (item.scores) {
      Object.keys(item.scores).forEach((key) => uniqueKeys.add(key));
    }
  }

  return Array.from(uniqueKeys);
}

export type ScoresData = {
  time: number;
  avg: number;
  last_updated: number;
  count: bigint;
  scores: Record<string, { avg: number; last_updated: number; count: bigint }>;
  group?: string | null;
};

export function useScoresQuery(
  projectIds: string[],
  { chartTimeFrame, timeBucket, groupBy, experimentIds, from }: QueryParams,
): { data: ScoresData[] | undefined; loading: boolean } {
  const startTime = new Date(chartTimeFrame.start).toISOString();
  const endTime = new Date(chartTimeFrame.end).toISOString();
  const query = useMemo<ParsedQuery | null>(() => {
    const filters: Expr[] = [
      {
        op: "gt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: startTime,
        },
      },
      {
        op: "lt",
        left: Query.ident(CreatedField),
        right: {
          op: "literal",
          value: endTime,
        },
      },
    ];

    if (projectIds.length === 0) {
      return null;
    }

    const fromClause =
      from === "experiment"
        ? Query.from("experiment", experimentIds)
        : Query.from("project_logs", projectIds);

    const dimensions: ParsedQuery["dimensions"] = [
      {
        alias: "time",
        expr: {
          op: "function",
          name: Query.ident(timeBucket),
          args: [Query.ident(CreatedField)],
        },
      },
    ];

    if (groupBy) {
      dimensions.push({
        alias: "group",
        expr: { btql: `metadata.${groupBy}` },
      });
    }

    return {
      from: fromClause,
      pivot: [{ alias: "scores", expr: { btql: "score" } }],
      unpivot: [
        {
          expr: { btql: "scores" },
          alias: ["score", "value"],
        },
      ],
      measures: [
        { alias: "last_updated", expr: { btql: "max(created)" } },
        { alias: "count", expr: { btql: "count(1)" } },
        { alias: "avg", expr: { btql: "avg(value)" } },
      ],
      dimensions,
      filter: Query.and(...filters),
      sort: [{ expr: { btql: "time" }, dir: "asc" }],
    };
  }, [
    projectIds,
    startTime,
    endTime,
    experimentIds,
    from,
    timeBucket,
    groupBy,
  ]);
  const { data, loading } = useBtql({
    query,
    name: "Scores query",
    ...getMonitorBtqlArgs({ startTime, projectIds }),
  });
  return {
    data: data?.toArray().map((r) => r.toJSON()),
    loading,
  };
}

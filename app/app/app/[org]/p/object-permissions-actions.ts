"use server";

import type { AclObjectType } from "@braintrust/core/typespecs";
import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import {
  type ObjectPermissionsCheckboxes,
  objectPermissionsCheckboxesSchema,
  makeObjectPermissionsRequirements,
} from "./object-permissions-types";
import {
  type UserGroupWithObjectPermissions,
  type UserObjectType,
  userGroupWithObjectPermissionsSchema,
} from "#/ui/permissions/permissions-types";
import {
  permissionsCheckboxQuery,
  userGroupWithObjectPermissionsQuery,
} from "#/ui/permissions/permissions-query-util";
import { singleQuote } from "#/utils/sql-utils";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

export type GetObjectPermissionsInputs = {
  objectType: AclObjectType;
  objectId: string;
  userObjectType: UserObjectType;
  userGroupId: string;
};

export async function getObjectPermissions(
  {
    objectType,
    objectId,
    userObjectType,
    userGroupId,
  }: GetObjectPermissionsInputs,
  authLookupRaw?: AuthLookup,
): Promise<ObjectPermissionsCheckboxes> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!(objectType && objectId)) {
    throw new HTTPError(403, "Unauthorized");
  }

  const permissionsRequirements = makeObjectPermissionsRequirements(objectType);
  const {
    query: objectQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: objectType,
      aclPermission: "read_acls",
    },
    filters: {
      id: [objectId],
    },
  });
  const userGroupIdParam = queryParams.add(userGroupId);
  const fullQuery = `
    with
    object_access as (
      select id from (${objectQuery}) "t" limit 1
    )
    select jsonb_build_object(
        ${Object.entries(permissionsRequirements)
          .map(
            ([key, requirement]) => `
            ${singleQuote(key)}, (${permissionsCheckboxQuery({
              requirement,
              objectIdSubquery: "select id from object_access",
              userObjectType,
              userGroupIdParam,
            })})
          `,
          )
          .join(", ")}
    ) result
    from object_access
  `;
  const supabase = getServiceRoleSupabase();
  //console.log("Running query\n", fullQuery, "\nwith params\n", queryParams.params);
  const row = extractSingularRow({
    rows: (await supabase.query(fullQuery, queryParams.params)).rows,
    notFoundErrorMessage,
  });
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const out: Record<string, any> = {};
  for (const key of Object.keys(permissionsRequirements)) {
    out[key] = row.result[key];
  }
  //console.log("out =\n", out);
  return objectPermissionsCheckboxesSchema.parse(out);
}

export type GetUserGroupWithObjectPermissionsInput = {
  objectType: AclObjectType;
  objectId?: string;
  org_name?: string;
  project_name?: string;
  name?: string;
  skipRestrictObjectTypeCheck?: boolean;
};

export async function getUserGroupWithObjectPermissions(
  {
    objectType,
    objectId,
    org_name,
    project_name,
    name,
    skipRestrictObjectTypeCheck,
  }: GetUserGroupWithObjectPermissionsInput,
  authLookupRaw?: AuthLookup,
): Promise<UserGroupWithObjectPermissions[]> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!objectType) {
    throw new HTTPError(403, "Unauthorized");
  }
  const { query: objectQuery, queryParams } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: objectType,
      aclPermission: "read_acls",
    },
    filters: {
      id: objectId ? [objectId] : undefined,
      org_name,
      project_name,
      name,
    },
  });
  const fullQuery = `
    with
    object_access as (
      select id from (${objectQuery}) "t" limit 1
    )
    select * from (${userGroupWithObjectPermissionsQuery({
      objectType,
      objectIdSubquery: "select id from object_access",
      skipRestrictObjectTypeCheck,
    })}) "t"
  `;
  const supabase = getServiceRoleSupabase();
  //console.log(
  //  "Running query\n",
  //  substituteParamsDebug(fullQuery, queryParams.params),
  //);
  const { rows } = await supabase.query(fullQuery, queryParams.params);
  return userGroupWithObjectPermissionsSchema
    .array()
    .parse(rows.map((r) => r["item"]));
}

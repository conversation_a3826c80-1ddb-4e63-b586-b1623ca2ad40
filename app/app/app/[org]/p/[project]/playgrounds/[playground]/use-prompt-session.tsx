import {
  type PromptSessionMeta,
  type getPromptSessionMeta,
} from "./playground-actions";
import { useQueryFunc } from "#/utils/react-query";
import { type getPromptSessionSummary } from "../playground-actions";
import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";
import { TransactionIdField, useDBQuery, useParquetView } from "#/utils/duckdb";
import { mergeStates, type SavingState } from "#/ui/saving";
import { useMutableObject } from "#/utils/mutable-object";
import {
  promptSessionStateSchema,
  type SyncedPlaygroundBlock,
  syncedPlaygroundBlockArraySchema,
} from "#/ui/prompts/schema";
import { toast } from "sonner";
import { zodErrorToString } from "#/utils/validation";
import { z } from "zod";

export const usePromptSession = ({
  orgName,
  projectName,
  playgroundName,
  promptSessionMetaServer,
}: {
  orgName: string;
  projectName: string;
  playgroundName: string;
  promptSessionMetaServer: PromptSessionMeta | null;
}) => {
  const { data: promptSessionMeta, invalidate: refreshPromptSessionsMeta } =
    useQueryFunc<typeof getPromptSessionMeta>({
      fName: "getPromptSessionMeta",
      args: {
        org_name: orgName,
        project_name: projectName,
        playground: playgroundName,
      },
      serverData: promptSessionMetaServer ?? null,
      // This solution isn't perfect, but it relies on the fact that once a prompt session is mutated,
      // if you change pages (e.g. after deleting it), then the server data will include a new id, which
      // will "bust" the cache.
      // See discussion on https://github.com/braintrustdata/braintrust/pull/3913#discussion_r1898294811
      // for more ideas.
      keyArgs: [promptSessionMetaServer?.id],
    });

  const { invalidate: refreshPromptSessionsSummary } = useQueryFunc<
    typeof getPromptSessionSummary
  >({
    fName: "getPromptSessionSummary",
    args: { org_name: orgName, project_name: projectName },
  });

  // The summary query is used on the playgrounds page
  const refreshPromptSessions = useCallback(async () => {
    await Promise.all([
      refreshPromptSessionsMeta(),
      refreshPromptSessionsSummary(),
    ]);
  }, [refreshPromptSessionsMeta, refreshPromptSessionsSummary]);

  const promptSessionId = promptSessionMeta?.id;

  const {
    refreshed: promptSessionReady,
    scan: promptSessionScan,
    schema: promptSessionSchema,
    channel: promptSessionChannel,
    isDirty: isPromptSessionDirty,
    isLoadingInitial: isPromptSessionLoadingInitial,
  } = useParquetView({
    objectType: "prompt_session",
    search: promptSessionId,
    disableCache: true,
  });

  const [savingPromptSession, setSavingPromptSession] =
    useState<SavingState>("none");
  const [savingDataset, setSavingDataset] = useState<SavingState>("none");

  const savingState = useMemo(() => {
    return mergeStates([savingPromptSession, savingDataset]);
  }, [savingPromptSession, savingDataset]);

  const promptSessionDML = useMutableObject({
    scan: promptSessionScan,
    channel: promptSessionChannel,
    objectType: "prompt_session",
    setSavingState: setSavingPromptSession,
  });

  const hasFunctionData = useMemo(() => {
    return promptSessionSchema?.fields.some((f) => f.name === "function_data");
  }, [promptSessionSchema]);

  // This part gets the prompt session state
  const {
    data: promptSessionRecords,
    hasLoaded: promptSessionRecordsHaveLoaded,
  } = useDBQuery(
    promptSessionScan &&
      `
    SELECT
      id,
      prompt_data,
      ${hasFunctionData ? "function_data" : "NULL"} AS function_data,
      ${TransactionIdField}
    FROM (${promptSessionScan})
    WHERE json(prompt_data) IS NOT NULL
    `,
    [promptSessionReady],
  );

  const promptSessionDbState = useMemo(() => {
    if (!promptSessionRecords) {
      return null;
    }

    const records = Object.fromEntries(
      promptSessionRecords.toArray().map((r) => {
        const rData = r.toJSON();
        return [
          rData.id,
          {
            prompt_data: JSON.parse(rData.prompt_data),
            function_data: rData.function_data
              ? JSON.parse(rData.function_data)
              : undefined,
          },
        ];
      }),
    );

    const result = promptSessionStateSchema.safeParse(records);

    if (result.success) {
      return result.data;
    } else {
      console.error("Failed to parse", records);
      console.error(zodErrorToString(result.error, 0, true));
      toast.error("Error parsing prompt session state", {
        description: zodErrorToString(result.error, 0, true),
      });
      return null;
    }
  }, [promptSessionRecords]);

  const playgroundBlocksDbState: SyncedPlaygroundBlock[] = useMemo(() => {
    if (!promptSessionRecords) {
      return [];
    }

    const records = promptSessionRecords.toArray().map((r) => {
      const rData = r.toJSON();
      return {
        id: rData.id,
        [TransactionIdField]: rData[TransactionIdField],
        prompt_data: JSON.parse(rData.prompt_data),
        function_data: rData.function_data
          ? JSON.parse(rData.function_data)
          : undefined,
      };
    });

    const result = syncedPlaygroundBlockArraySchema.safeParse(records);

    if (result.success) {
      return result.data;
    } else {
      console.error("Failed to parse", records);
      console.error(zodErrorToString(result.error, 0, true));
      toast.error("Error parsing playgroundBlocksDbState", {
        description: zodErrorToString(result.error, 0, true),
      });
      return [];
    }
  }, [promptSessionRecords]);

  const promptSessionTransactionIds = useMemo(
    () =>
      promptSessionRecords
        ? z.record(z.string()).parse(
            promptSessionRecords.toArray().reduce((acc, r) => {
              if (r[TransactionIdField]) {
                acc[r.id] = r[TransactionIdField];
              }
              return acc;
            }, {}),
          )
        : undefined,
    [promptSessionRecords],
  );

  return useMemo(
    () => ({
      promptSessionMeta,
      refreshPromptSessions,
      promptSessionId,
      promptSessionDbState,
      playgroundBlocksDbState,
      promptSessionTransactionIds,
      promptSessionReady,
      promptSessionScan,
      promptSessionDML,
      savingDataset,
      savingState,
      setSavingDataset,
      setSavingPromptSession,
      isPromptSessionDirty,
      isPromptSessionLoadingInitial,
      promptSessionRecordsHaveLoaded,
    }),
    [
      isPromptSessionDirty,
      isPromptSessionLoadingInitial,
      playgroundBlocksDbState,
      promptSessionDML,
      promptSessionDbState,
      promptSessionId,
      promptSessionMeta,
      promptSessionReady,
      promptSessionRecordsHaveLoaded,
      promptSessionScan,
      promptSessionTransactionIds,
      refreshPromptSessions,
      savingDataset,
      savingState,
    ],
  );
};

const PromptTransactionIdContext = createContext<Record<string, string>>({});

export const PromptTransactionIdProvider = ({
  children,
  promptSessionTransactionIds = {},
  promptToGenerationId = {},
}: {
  children: React.ReactNode;
  promptSessionTransactionIds?: Record<string, string>;
  promptToGenerationId?: Record<string, string>;
}) => {
  const promptTransactionIds = useMemo(() => {
    return Object.fromEntries(
      Object.entries(promptSessionTransactionIds).map(
        ([promptId, transactionId]) => [
          promptToGenerationId[promptId],
          transactionId,
        ],
      ),
    );
  }, [promptSessionTransactionIds, promptToGenerationId]);

  return (
    <PromptTransactionIdContext.Provider value={promptTransactionIds}>
      {children}
    </PromptTransactionIdContext.Provider>
  );
};

export const usePromptTransactionIds = () => {
  return useContext(PromptTransactionIdContext);
};

import {
  type SSEConsoleEventData,
  callEventSchema,
  type SSEProgressEventData,
  type RemoteEvalData,
  type FunctionId,
} from "@braintrust/core/typespecs";
import { BraintrustStream, type BraintrustStreamChunk } from "braintrust";
import { applyBraintrustSSETransformStreams } from "#/app/app/[org]/prompt/[prompt]/completion-stream";
import {
  sseProgressEventDataSchema,
  sseProgressEventSchema,
  type RunEvalRequest,
} from "@braintrust/core/typespecs";

import {
  sessionFetchProps,
  type BtSessionToken,
} from "#/utils/auth/session-token";
import { toast } from "sonner";
import { _urljoin, isEmpty } from "@braintrust/core";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { produce } from "immer";
import { type AllCompletionsStreamingCell } from "#/ui/prompts/schema";
import { fastEventSourceParser } from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";

export const SINGLETON_DATASET_ID = "single-row";

export type PlayxEvalRequest =
  | {
      type: "playx";
      request: RunEvalRequest;
    }
  | {
      type: "remote";
      request: RemoteEvalData;
      data: RunEvalRequest["data"];
      scores: {
        function_id: FunctionId;
        name: string;
      }[];
      parent: RunEvalRequest["parent"];
      stream: boolean;
    };

// This is copy/pasted and modified from create-experiment-dialog.tsx.
// Eventually we should remove that one
export async function runEval({
  proxyUrl,
  runEvalRequest,
  sessionToken,
  orgName,
  onError,
  setCompletion,
  signal,
}: {
  proxyUrl: string;
  runEvalRequest: PlayxEvalRequest;
  sessionToken: BtSessionToken;
  orgName: string;
  onError: (msg: string) => void;
  setCompletion: (
    datasetRecordId: string,
    callback: (
      prev: AllCompletionsStreamingCell,
    ) => AllCompletionsStreamingCell | undefined,
  ) => void;
  signal?: AbortSignal;
}) {
  if (sessionToken === "loading") {
    toast.error("Session token still loading. Try again in a few seconds.");
    return;
  }

  let result: Response | undefined = undefined;
  let error: string | undefined = undefined;
  try {
    const { sessionHeaders, sessionExtraFetchProps } =
      sessionFetchProps(sessionToken);
    result = await (runEvalRequest.type === "playx"
      ? fetch(_urljoin(normalizeProxyUrlBase(proxyUrl), "function", "eval"), {
          method: "POST",
          body: JSON.stringify(runEvalRequest.request),
          mode: "cors",
          headers: {
            "content-type": "application/json",
            "x-bt-org-name": orgName,
            ...sessionHeaders,
          },
          ...sessionExtraFetchProps,
          signal,
        })
      : fetch(_urljoin(runEvalRequest.request.endpoint, "eval"), {
          method: "POST",
          body: JSON.stringify({
            name: runEvalRequest.request.eval_name,
            parameters: runEvalRequest.request.parameters,
            data: runEvalRequest.data,
            scores: runEvalRequest.scores,
            parent: runEvalRequest.parent,
            stream: runEvalRequest.stream,
          }),
          mode: "cors",
          headers: {
            "content-type": "application/json",
            "x-bt-org-name": orgName,
            ...sessionHeaders,
          },
          ...sessionExtraFetchProps,
          signal,
        }));
  } catch (e) {
    if (e instanceof DOMException && e.name === "AbortError") {
      // aborted before streaming began
      return;
    }
    error = `${e}`;
  }

  if (error || !result || !result.ok || !result.body) {
    toast.error("Failed to run playground", {
      description:
        error ??
        (result ? `${result.status}: ${await result.text()}` : "Unknown error"),
      closeButton: true,
    });
    return;
  }

  const resultStream = result.body
    .pipeThrough(new TextDecoderStream())
    .pipeThrough(fastEventSourceParser());
  const datasetStreams = new Map<string, CellStreamState>();

  const reader = resultStream.getReader();

  while (true) {
    const { done, value } = await reader.read();
    if (done) {
      break;
    }

    switch (value.event) {
      case "progress": {
        const progressEvent = sseProgressEventSchema.safeParse(value);
        if (!progressEvent.success) {
          console.warn("Failed to parse progress data", progressEvent.error);
          continue;
        }
        const progressDataParsed = sseProgressEventDataSchema.safeParse(
          JSON.parse(progressEvent.data.data),
        );
        if (!progressDataParsed.success) {
          console.warn(
            "Failed to parse progress data",
            progressDataParsed.error,
          );
          continue;
        }
        const progressData = progressDataParsed.data;

        const datasetRecordId = progressData.origin?.id ?? SINGLETON_DATASET_ID;

        if (!datasetStreams.has(datasetRecordId)) {
          datasetStreams.set(
            datasetRecordId,
            new CellStreamState({
              setIsJSON: (isJSON: boolean) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.isJSON = isJSON;
                  }),
                );
              },
              onError: (error: Error) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.error = error.message;
                  }),
                );
              },
              onTaskDone: () => {
                // Only set the task done if there is a completion cell
                setCompletion(datasetRecordId, (prev) =>
                  !isEmpty(prev)
                    ? produce(prev, (draft) => {
                        draft.isTaskDone = true;
                      })
                    : undefined,
                );
              },
              setCompletion: (value: string) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.completion = value ?? "";
                  }),
                );
              },
              setCurrentStatus: (status: string) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.streamingStatus = status;
                  }),
                );
              },
              addReasoning: (thought: string) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.reasoning = (draft.reasoning ?? "") + thought;
                  }),
                );
              },
              addConsoleMessages: (messages: SSEConsoleEventData[]) => {
                setCompletion(datasetRecordId, (prev) =>
                  produce(prev, (draft) => {
                    draft.consoleMessages = [
                      ...(draft.consoleMessages ?? []),
                      ...messages,
                    ];
                  }),
                );
              },
            }),
          );
        }
        const stream = datasetStreams.get(datasetRecordId)!;
        stream.writeEvent(progressData);

        break;
      }
      case "summary": {
        break;
      }
      case "meta": {
        break;
      }
      case "done": {
        break;
      }
      case "error": {
        // XXX
        // * Propagate errors to the UI (where? a toast?)
        // * Handle free tier (see how it's done in create-experiment-dialog.tsx)
        // * How do individual cell errors show up? Maybe test it?
        //console.warn("error event streaming");
        onError?.(value.data);
        break;
      }
      default: {
        console.warn("Unknown event", value.event, value.data);
      }
    }
  }
}

export async function stopEval({
  proxyUrl,
  stopToken,
  sessionToken,
  orgName,
}: {
  proxyUrl: string;
  stopToken: string;
  sessionToken: BtSessionToken;
  orgName: string;
}): Promise<boolean> {
  if (sessionToken === "loading") {
    toast.error("Session token still loading. Try again in a few seconds.");
    return false;
  }

  const { sessionHeaders, sessionExtraFetchProps } =
    sessionFetchProps(sessionToken);

  try {
    await fetch(_urljoin(normalizeProxyUrlBase(proxyUrl), "function", "stop"), {
      method: "POST",
      body: JSON.stringify({ stop_token: stopToken }),
      mode: "cors",
      headers: {
        "content-type": "application/json",
        "x-bt-org-name": orgName,
        ...sessionHeaders,
      },
      ...sessionExtraFetchProps,
    });
    return true;
  } catch (e) {
    console.error("Failed to stop eval on server", e);
    return false;
  }
}

class CellStreamState {
  private stream: TransformStream<BraintrustStreamChunk, BraintrustStreamChunk>;
  private finished: Promise<void>;
  private writer: WritableStreamDefaultWriter<BraintrustStreamChunk>;

  constructor(callbacks: {
    setIsJSON: (isJSON: boolean) => void;
    onError: (error: Error) => void;
    setCompletion: (value: string) => void;
    setCurrentStatus: (status: string) => void;
    addReasoning: (reasoning: string) => void;
    addConsoleMessages: (messages: SSEConsoleEventData[]) => void;
    onTaskDone: () => void;
  }) {
    this.stream = new TransformStream();
    this.writer = this.stream.writable.getWriter();
    const cellStream = applyBraintrustSSETransformStreams({
      readable: this.stream.readable,
      setIsJSON: callbacks.setIsJSON,
      onError: callbacks.onError,
      setCurrentStatus: callbacks.setCurrentStatus,
      addConsoleMessages: callbacks.addConsoleMessages,
      addReasoning: callbacks.addReasoning,
      onTaskDone: callbacks.onTaskDone,
    });

    this.finished = (async () => {
      const reader = cellStream.getReader();
      const decoder = new TextDecoder();
      const chunks = [];
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        chunks.push(decoder.decode(value));
        if (!document.hidden) {
          callbacks.setCompletion(chunks.join(""));
        }
      }
      // in case the tab was inactive the entire time, we want to set the completion when things are finished
      callbacks.setCompletion(chunks.join(""));
    })().catch(console.error);
  }

  public writeEvent(event: SSEProgressEventData) {
    const callEvent = callEventSchema.safeParse(event);
    if (!callEvent.success) {
      console.warn("Failed to parse event", callEvent.error);
      return;
    }
    this.writer.write(BraintrustStream.parseRawEvent(callEvent.data));
  }

  public async flush() {
    await this.finished;
  }
}

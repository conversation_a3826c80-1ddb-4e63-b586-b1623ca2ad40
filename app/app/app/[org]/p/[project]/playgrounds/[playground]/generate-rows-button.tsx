import { But<PERSON> } from "#/ui/button";
import { Blend } from "lucide-react";
import { useGlobalChat } from "#/ui/optimization/use-global-chat-context";

export const GenerateRowsButton = () => {
  const { setIsChatOpen, handleSendMessage } = useGlobalChat();

  return (
    <Button
      variant="ghost"
      size="xs"
      className="gap-2 font-normal text-foreground"
      onClick={() => {
        setIsChatOpen(true);
        handleSendMessage(
          {
            id: crypto.randomUUID(),
            type: "user_message",
            message: "Please generate 5 rows of data for the dataset",
          },
          {
            clearContextObjects: false,
            clearUserMessage: false,
          },
        );
      }}
    >
      <Blend className="size-3 text-foreground" />
      Generate rows
    </Button>
  );
};

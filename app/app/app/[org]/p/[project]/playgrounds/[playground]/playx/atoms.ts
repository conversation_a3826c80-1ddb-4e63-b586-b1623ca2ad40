import { type AllCompletionsStrings } from "#/ui/prompts/schema";
import { atom } from "jotai";
import { atomWithListeners } from "#/utils/atomWithListener";
import { useMemo } from "react";
import { type TransactionId } from "@braintrust/core";

export const isRunningAtom = atom(false);
export const latestXactIdAtom = atom<TransactionId | undefined>(undefined);
export const runningInfoAtom = atom<{
  generationIds?: string[];
  rowIds?: string[];
  stopToken?: string;
  totalDatasetRows?: number;
}>({});
export const hasWebsocketRejoinedAtom = atom(false);

export type StreamingState = "streaming" | undefined;
export const streamingStateAtom = atom<StreamingState>(undefined);
export const isPlaygroundRunningAtom = atom<boolean>((get) => {
  const isRunning = get(isRunningAtom);
  const streamingState = get(streamingStateAtom);
  return isRunning || streamingState === "streaming";
});

export const resetRunningStateAtom = atom(null, (get, set) => {
  set(isRunningAtom, false);
  set(streamingStateAtom, undefined);
  set(latestXactIdAtom, undefined);
  set(runningInfoAtom, {});
  set(streamingCompletionsAtom, {});
  set(completedTaskCountAtom, {});
  set(completedTasksAtom, new Set<string>());
});

export const [streamingCompletionsAtom, useStreamingCompletionsListener] =
  atomWithListeners<AllCompletionsStrings>({}, (get, set, newVal, prevVal) => {
    const completedByDatasetRow: Record<string, number> = {};
    const allCompletedTasks = new Set<string>(get(completedTasksAtom));

    // Find tasks that existed in prevVal but not in newVal (completed tasks)
    for (const datasetRowId in prevVal) {
      if (!completedByDatasetRow[datasetRowId]) {
        completedByDatasetRow[datasetRowId] = 0;
      }

      for (const generationId in prevVal[datasetRowId]) {
        const taskKey = `${datasetRowId}:${generationId}`;
        if (
          (!newVal[datasetRowId] || !newVal[datasetRowId][generationId]) &&
          !allCompletedTasks.has(taskKey)
        ) {
          completedByDatasetRow[datasetRowId]++;
          allCompletedTasks.add(taskKey);
        }
      }
    }

    if (
      Object.keys(completedByDatasetRow).some(
        (id) => completedByDatasetRow[id] > 0,
      )
    ) {
      set(completedTaskCountAtom, (prev) => {
        const result = { ...prev };
        for (const datasetRowId in completedByDatasetRow) {
          result[datasetRowId] =
            (result[datasetRowId] ?? 0) + completedByDatasetRow[datasetRowId];
        }
        return result;
      });
      set(completedTasksAtom, allCompletedTasks);
    }
  });
export const hasStreamingCompletionsAtom = atom(
  (get) => Object.keys(get(streamingCompletionsAtom)).length > 0,
);

export const streamingTaskDoneCountAtom = atom((get) =>
  Object.entries(get(streamingCompletionsAtom)).reduce<Record<string, number>>(
    (acc, [datasetRowId, generations]) => {
      acc[datasetRowId] = Object.values(generations).reduce(
        (count, curr) => count + (curr.isTaskDone ? 1 : 0),
        0,
      );
      acc["total"] += acc[datasetRowId];
      return acc;
    },
    { total: 0 },
  ),
);

export const completedTasksAtom = atom<Set<string>>(new Set<string>());
export const completedTaskCountAtom = atom<Record<string, number>>({});

/**
 * Fully completed rows are counted as 2 (main task done and entire eval done)
 * Partially completed rows are counted as 1 (main task done)
 */
const FULL_COMPLETION_MULTIPLIER = 2;

export const useCompletionPercentageAtom = (datasetRowId?: string) => {
  return useMemo(
    () =>
      atom((get) => {
        const completedTaskCount =
          datasetRowId == null
            ? get(completedTasksAtom).size
            : (get(completedTaskCountAtom)[datasetRowId] ?? 0);
        const runningInfo = get(runningInfoAtom);
        const streamingTaskDoneCounts = get(streamingTaskDoneCountAtom);
        const streamingTaskDoneCount =
          datasetRowId == null
            ? streamingTaskDoneCounts["total"]
            : (streamingTaskDoneCounts[datasetRowId] ?? 0);

        let totalTaskCount: number | undefined;
        if (runningInfo.generationIds) {
          // Initiated a full run and viewing the entire playground
          if (
            datasetRowId == null &&
            runningInfo.rowIds == null &&
            runningInfo.totalDatasetRows
          ) {
            totalTaskCount =
              runningInfo.generationIds.length * runningInfo.totalDatasetRows;
          } else if (
            // Initiated a full run or a row run from trace sheet and viewing the running row
            (datasetRowId &&
              (runningInfo.rowIds?.includes(datasetRowId) ||
                runningInfo.rowIds == null)) ||
            // Initiated a row run from trace sheet and viewing entire playground
            (datasetRowId == null && runningInfo.rowIds != null)
          ) {
            totalTaskCount = runningInfo.generationIds.length;
          }
        }

        if (!totalTaskCount) {
          return undefined;
        }

        // Cap at 100% as a (dumb) safeguard against overcounting completed tasks
        const completedPercentage = Math.min(
          ((completedTaskCount * FULL_COMPLETION_MULTIPLIER +
            streamingTaskDoneCount) /
            (FULL_COMPLETION_MULTIPLIER * totalTaskCount)) *
            100,
          100,
        );

        // We don't want individual rows stuck at 100% for a long time, which makes things feel "stuck"
        return completedPercentage === 100 && datasetRowId
          ? undefined
          : completedPercentage;
      }),
    [datasetRowId],
  );
};

export const useStreamingCompletionsAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.completion ?? null)
          : null,
      ),
    [datasetRowId, generationId],
  );
};

export const useHasStreamingCompletionAtom = (
  datasetRowId: string,
  generationId: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? !!get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
          : false,
      ),
    [datasetRowId, generationId],
  );
};

export const useStreamingCompletionErrorAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.error ?? null)
          : null,
      ),
    [datasetRowId, generationId],
  );
};

export const useStreamingCompletionReasoningAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.reasoning ?? "")
          : "",
      ),
    [datasetRowId, generationId],
  );
};

export const useStreamingCompletionConsoleMessagesAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.consoleMessages ?? [])
          : [],
      ),
    [datasetRowId, generationId],
  );
};

export const useStreamingCompletionTaskDoneAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.isTaskDone ?? false)
          : false,
      ),
    [datasetRowId, generationId],
  );
};

export const useStreamingStatusAtom = (
  datasetRowId: string,
  generationId?: string,
) => {
  return useMemo(
    () =>
      atom((get) =>
        generationId
          ? (get(streamingCompletionsAtom)[datasetRowId]?.[generationId]
              ?.streamingStatus ?? "")
          : "",
      ),
    [datasetRowId, generationId],
  );
};

export const stopRequestedAtom = atom(false);

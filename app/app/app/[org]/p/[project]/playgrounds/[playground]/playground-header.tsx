"use client";

import { type SavingState, SavingStatus } from "#/ui/saving";
import { objectCopyName } from "#/utils/metadata";
import { useOrg, useUser } from "#/utils/user";
import { type SetStateAction, type Dispatch, useCallback } from "react";
import { useEntityContextActions } from "../../../useEntityContextActions";
import {
  getPlaygroundLink,
  getPlaygroundsLink,
} from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { useCreatePlaygroundDialog } from "#/app/app/[org]/prompt/[prompt]/create-playground-dialog";
import { EntityContextMenu } from "#/ui/entity-context-menu";
import { makePromptSessionDefaultName } from "#/app/app/[org]/prompt/[prompt]/createPromptSession";
import { Button } from "#/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileSpreadsheet, Plus } from "lucide-react";
import { cn } from "#/utils/classnames";
import { useSessionToken } from "#/utils/auth/session-token";
import { type PromptSessionMeta } from "./playground-actions";
import { DiffModeSwitch, useDiffModeOptions } from "#/ui/diff-mode-switch";
import { type Schema } from "apache-arrow";
import { type UIFunction } from "#/ui/prompts/schema";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  type OtherObjectInserterInfo,
  useOtherObjectInserter,
} from "#/utils/other-object-inserter";
import { useRouter } from "next/navigation";
import { PromptSessionIdField } from "@braintrust/local/query";
import { useDuckDB } from "#/utils/duckdb";
import { parseObjectJSON } from "#/utils/schema";
import { type DiffModeState } from "#/ui/query-parameters";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { PlaygroundRunButton } from "./playground-run-button";
import {
  CreateExperimentDialog,
  type CreateExperimentTask,
} from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";
import { type SavedScorer } from "#/utils/scorers";
import { pluralize } from "#/utils/plurals";
import { OptimizationChat } from "#/ui/optimization/optimization-chat";

export function PlaygroundHeader({
  className,
  playgroundName,
  projectName,
  savingState,
  refreshPromptSessions,
  promptSessionMeta,
  promptSessionReady,
  promptSessionScan,
  downloadJSON,
  downloadCSV,
  setSavingPromptSession,
  isReadOnly,
  diffMode,
  setDiffMode,
  diffModeOptionParams,
  selectedDatasetId,
  getFunctions,
  numPrompts,
  runPrompts,
  stop,
  isRunning,
  savedScorers,
  scorerFunctions,
  maxConcurrency,
  strict,
  isDatasetEmpty,
  hasEverRun,
  extraMessages,
  lintError,
  isLoopEnabled,
}: {
  className?: string;
  playgroundName: string;
  projectName: string;
  savingState: SavingState;
  refreshPromptSessions: () => Promise<void>;
  promptSessionMeta: PromptSessionMeta;
  promptSessionReady: number;
  promptSessionScan: string | null;
  downloadJSON: () => void;
  downloadCSV: () => Promise<void>;
  setSavingPromptSession: (state: SavingState) => void;
  isReadOnly: boolean;
  diffModeOptionParams: {
    loading: boolean | undefined;
    comparisonExperimentsCount: number;
    experimentSchema: Schema | null;
  };
  diffMode: DiffModeState | null;
  setDiffMode: Dispatch<SetStateAction<DiffModeState>>;
  selectedDatasetId?: string;
  getFunctions: (args: {
    useInlinePrompts: boolean;
  }) => Promise<CreateExperimentTask[]>;
  numPrompts: number;
  runPrompts: VoidFunction;
  stop: VoidFunction;
  isRunning: boolean;
  savedScorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;
  maxConcurrency: number;
  strict: boolean | undefined;
  isDatasetEmpty?: boolean;
  hasEverRun?: boolean;
  extraMessages?: string;
  lintError?: string;
  isLoopEnabled: boolean;
}) {
  const org = useOrg();
  const duck = useDuckDB();
  const { user } = useUser();

  const apiUrl = org.api_url;
  const userId = user?.id;

  const { getOrRefreshToken } = useSessionToken();

  const { actions: promptActions, modals: promptActionModals } =
    useEntityContextActions({
      entityType: "prompt_session",
      onUpdate: refreshPromptSessions,
      reloadPageOnUpdateArgs: {
        getEditedEntityLink: (promptName) =>
          getPlaygroundLink({
            orgName: org.name,
            projectName,
            playgroundName: promptName,
          }),
        getDeletedEntityLink: () =>
          `${getPlaygroundsLink({ orgName: org.name, projectName })}`,
      },
    });

  const { setNewPromptSessionDefaultValue, makeNewPromptSessionDialog } =
    useCreatePlaygroundDialog();
  const {
    setNewPromptSessionDefaultValue: setCopyToNewPromptSessionDefaultValue,
    makeNewPromptSessionDialog: makeCopyToNewPromptSessionDialog,
  } = useCreatePlaygroundDialog();

  const setOtherPromptInserterInfo = useOtherObjectInserter({
    objectType: "prompt_session",
    setSavingState: setSavingPromptSession,
  });

  const getAllRowsFromPromptSession = useCallback(async () => {
    if (!duck || !promptSessionReady) {
      throw new Error(
        `Failed to query rows from ${JSON.stringify(
          playgroundName,
        )}: data not ready`,
      );
    }
    const conn = await duck?.connect();
    const result = await conn?.query(`SELECT * FROM (${promptSessionScan})`);
    return (result?.toArray() || []).map((row) =>
      parseObjectJSON("prompt_session", row),
    );
  }, [duck, playgroundName, promptSessionReady, promptSessionScan]);

  const makeOtherPromptSessionInserterInfoFromTableScan = (otherPromptSession: {
    id: string;
    name: string;
  }): OtherObjectInserterInfo => ({
    objectInfo: otherPromptSession,
    getRows: async () => {
      const rows = await getAllRowsFromPromptSession();
      return rows.map((row) => {
        row[PromptSessionIdField] = otherPromptSession.id;
        return row;
      });
    },
    onSuccess: () => {
      window.open(
        getPlaygroundLink({
          orgName: org.name,
          projectName,
          playgroundName: otherPromptSession.name,
        }),
        "_blank",
      );
      window.focus();
    },
  });

  const router = useRouter();
  const routeToNewPrompt = useCallback(
    ({ name: playgroundName }: { name: string }) => {
      router.push(
        getPlaygroundLink({
          orgName: org.name,
          projectName,
          playgroundName: playgroundName,
        }),
      );
    },
    [org.name, projectName, router],
  );

  const { diffModeOptions } = useDiffModeOptions({
    diffModeState: diffMode,
    setDiffMode,
    diffModeOptionParams,
  });

  const {
    flags: { functions: functionsEnabled },
  } = useFeatureFlags();

  return (
    <div
      className={cn(
        "flex flex-none items-center gap-2 bg-primary-50 px-3 pb-2 pt-1",
        className,
      )}
    >
      <h1 className="flex-none text-base font-semibold">{playgroundName}</h1>
      {!isReadOnly && (
        <EntityContextMenu
          objectName={playgroundName}
          objectType="prompt_session"
          objectId={promptSessionMeta.id}
          orgName={org.name}
          projectName={projectName}
          handleDelete={async () =>
            promptActions.deleteEntity({
              entityName: playgroundName,
              entityId: promptSessionMeta.id,
            })
          }
          handleEdit={() =>
            promptActions.editEntityName({
              entityName: playgroundName,
              entityId: promptSessionMeta.id,
            })
          }
          handleCopyId={() =>
            promptActions.copyEntityId({
              entityId: promptSessionMeta.id,
            })
          }
          extraOptions={
            org.id
              ? [
                  {
                    label: (
                      <>
                        <Plus className="size-3" /> New playground
                      </>
                    ),
                    onClick: () => {
                      setNewPromptSessionDefaultValue(
                        makePromptSessionDefaultName(1),
                      );
                    },
                  },
                  {
                    label: (
                      <>
                        <Copy className="size-3" /> Duplicate playground
                      </>
                    ),
                    onClick: () => {
                      setCopyToNewPromptSessionDefaultValue(
                        objectCopyName(playgroundName),
                      );
                    },
                  },
                  ...(downloadCSV
                    ? [
                        {
                          label: (
                            <>
                              <FileSpreadsheet className="size-3" /> Download
                              data as CSV
                            </>
                          ),
                          disabled: !hasEverRun || isRunning,
                          onClick: () => {
                            downloadCSV();
                          },
                        },
                      ]
                    : []),
                  {
                    label: (
                      <>
                        <FileJson className="size-3" /> Download data as JSON
                      </>
                    ),
                    disabled: !hasEverRun || isRunning,
                    onClick: () => {
                      downloadJSON();
                    },
                  },
                ]
              : undefined
          }
        />
      )}
      <div className="flex flex-none grow items-center justify-end gap-2">
        <SavingStatus
          state={savingState}
          className="mr-2 text-xs text-gray-500"
        />
        <DiffModeSwitch
          diffModeState={diffMode}
          diffModeOptions={diffModeOptions} // TODO: remove between_experiments and add support for between_prompts
          setDiffMode={setDiffMode}
        />

        {!isReadOnly && (
          <>
            <CreateExperimentDialog
              mode="playground"
              getFunctions={getFunctions}
              dataset={selectedDatasetId}
              scorers={savedScorers}
              scorerFunctions={scorerFunctions}
              maxConcurrency={maxConcurrency}
              strict={strict}
              extraMessages={extraMessages}
            >
              <Button
                size="xs"
                variant="ghost"
                disabled={numPrompts === 0 || !functionsEnabled}
                Icon={Plus}
              >
                {pluralize(numPrompts, "Experiment")}
              </Button>
            </CreateExperimentDialog>

            {isLoopEnabled && <OptimizationChat />}

            <Tooltip>
              <TooltipTrigger asChild>
                <span>
                  <PlaygroundRunButton
                    disabled={isDatasetEmpty || numPrompts === 0 || !!lintError}
                    onRun={runPrompts}
                    onStop={stop}
                    isRunning={isRunning}
                    variant="border"
                  />
                </span>
              </TooltipTrigger>
              {isDatasetEmpty && (
                <TooltipContent className="text-xs">
                  Add at least one row to the dataset
                </TooltipContent>
              )}
              {lintError && (
                <TooltipContent className="text-xs">{lintError}</TooltipContent>
              )}
            </Tooltip>
          </>
        )}
      </div>
      {promptActionModals}
      {makeNewPromptSessionDialog({
        orgName: org.name,
        projectName,
        refreshPromptSessions,
        initialPromptArgs:
          apiUrl && userId
            ? { apiUrl, getOrRefreshToken: getOrRefreshToken, userId }
            : undefined,
        onSuccess: routeToNewPrompt,
      })}
      {makeCopyToNewPromptSessionDialog({
        orgName: org.name,
        projectName,
        refreshPromptSessions,
        onSuccess: ({ id, name }: { id: string; name: string }) => {
          setOtherPromptInserterInfo(
            makeOtherPromptSessionInserterInfoFromTableScan({ id, name }),
          );
        },
      })}
    </div>
  );
}

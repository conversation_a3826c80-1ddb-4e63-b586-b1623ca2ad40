import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { cn } from "#/utils/classnames";
import { MessageCircle, Plus, Route, Unplug } from "lucide-react";
import { PromptsDropdown } from "../prompts-dropdown";
import { RemoteEvalsDropdown } from "../remote-evals-dropdown";
import { AgentsDropdown } from "../agents-dropdown";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useState, type PropsWithChildren } from "react";
import { usePlaygroundPromptSheetIndexState } from "#/ui/query-parameters";
import { useSyncedPrompts } from "../../prompts/synced/use-synced-prompts";
import { type UIFunctionData } from "#/ui/prompts/schema";
import { type PromptData } from "#/ui/prompts/schema";

export const PlaygroundAddTask = ({
  isReadOnly,
  numTasks,
  projectName,
  children,
  disabledOriginIds = [],
}: PropsWithChildren<{
  isReadOnly?: boolean;
  numTasks?: number;
  projectName: string;
  disabledOriginIds?: string[];
}>) => {
  const { setPromptSheetIndex } = usePlaygroundPromptSheetIndexState();
  const { appendNewEditor_ROOT } = useSyncedPrompts();

  const onAdd = (item: Parameters<typeof appendNewEditor_ROOT>[0]) => {
    appendNewEditor_ROOT(item);
    setPromptSheetIndex(numTasks ?? 0);
  };

  return (
    <AddTask
      projectName={projectName}
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      onAdd={(t) => onAdd(t as Parameters<typeof appendNewEditor_ROOT>[0])}
      disabledOriginIds={disabledOriginIds}
    >
      {children ?? (
        <Button
          size="xs"
          Icon={Plus}
          className={cn("flex-none", { hidden: isReadOnly })}
        >
          Task
          {!!numTasks && numTasks > 0 && (
            <span className="text-[10px] font-normal text-primary-500">
              {numTasks}
            </span>
          )}
        </Button>
      )}
    </AddTask>
  );
};

export const AddTask = ({
  projectName,
  children,
  hideRemoteEvals,
  disallowBlankTasks,
  disabledOriginIds,
  align = "end",
  onAdd,
}: PropsWithChildren<{
  projectName: string;
  hideRemoteEvals?: boolean;
  align?: "start" | "end";
  disallowBlankTasks?: boolean;
  disabledOriginIds?: string[];
  onAdd: (task: {
    origin?: PromptData["origin"];
    promptData?: PromptData;
    functionData?: UIFunctionData;
  }) => void;
}>) => {
  const [open, setOpen] = useState(false);
  const {
    flags: { agents, remoteEvals },
  } = useFeatureFlags();

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <MessageCircle className="size-3" />
            Prompt
          </DropdownMenuSubTrigger>
          <PromptsDropdown
            onAddPrompt={onAdd}
            isInSubMenu
            open={open}
            hideBlankOption={disallowBlankTasks}
            projectName={projectName}
            disabledOriginIds={disabledOriginIds}
            dropdownMenuContentProps={{
              align: "start",
            }}
          />
        </DropdownMenuSub>
        {agents && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Route className="size-3" />
              Agent
            </DropdownMenuSubTrigger>
            <AgentsDropdown
              isInSubMenu
              open={open}
              projectName={projectName}
              selectedAgentIds={[]}
              disabledOriginIds={disabledOriginIds}
              dropdownMenuContentProps={{
                align: "start",
              }}
              hideBlankOption={disallowBlankTasks}
              onAddAgent={onAdd}
            />
          </DropdownMenuSub>
        )}
        {remoteEvals && !hideRemoteEvals && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Unplug className="size-3" />
              Remote eval
            </DropdownMenuSubTrigger>
            <RemoteEvalsDropdown isInSubMenu open={open} onAddEval={onAdd} />
          </DropdownMenuSub>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

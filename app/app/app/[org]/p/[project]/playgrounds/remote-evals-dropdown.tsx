import { Button, type ButtonProps } from "#/ui/button";
import { Plus, RefreshCcw, Settings2 } from "lucide-react";
import {
  forwardRef,
  type HTMLAttributes,
  useCallback,
  useContext,
  useMemo,
} from "react";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { isEmpty } from "#/utils/object";
import { useRemoteEvals } from "./[playground]/playx/remote-evals-provider";
import { type EvaluatorDefinition } from "braintrust";
import { type PromptData } from "@braintrust/core/typespecs";
import { type UIFunctionData } from "#/ui/prompts/schema";
import { useQueryClient } from "@tanstack/react-query";
import { Spinner } from "#/ui/icons/spinner";
import { cn } from "#/utils/classnames";
import { useRouter } from "next/navigation";
import { useOrg } from "#/utils/user";
import { ProjectContext } from "../projectContext";
import { getProjectConfigurationLink } from "../getProjectLink";

type RemoteEvalItem = {
  name: string;
  evalDef: EvaluatorDefinition;
  endpointUrl: string;
};

export const DEFAULT_ENDPOINT_URL = "http://localhost:8300";

export const RemoteEvalsDropdown = ({
  buttonProps,
  dropdownMenuContentProps,
  children,
  onAddEval,
  open,
  isInSubMenu,
}: React.PropsWithChildren<{
  onAddEval: (item: {
    functionData?: UIFunctionData;
    origin?: PromptData["origin"];
  }) => void;
  buttonProps?: ButtonProps;
  dropdownMenuContentProps?: DropdownMenuContentProps;
  open?: boolean;
  isInSubMenu?: boolean;
}>) => {
  const { remoteEvals, isLoading } = useRemoteEvals({
    enabled: open ? true : undefined,
  });
  const queryClient = useQueryClient();
  const { projectName } = useContext(ProjectContext);

  const isRefreshingSources = queryClient.isFetching({
    queryKey: ["remote-evals", projectName],
  });

  const dropdownData = useMemo(() => {
    if (isEmpty(remoteEvals)) {
      return { items: undefined, subGroups: undefined };
    }

    return {
      subGroups: Object.entries(remoteEvals).map(
        ([endpoint, { evals, error }]) => ({
          groupLabel: endpoint,
          items: Object.entries(evals).map(([evalName, evalDef]) => ({
            name: evalName,
            evalDef,
            endpointUrl: endpoint,
          })),
        }),
      ),
    };
  }, [remoteEvals]);

  const RemoteEvalMenuItem = forwardRef<
    HTMLDivElement,
    { item: RemoteEvalItem } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: evalItem, ...rest }, ref) => {
        return (
          <DropdownMenuItem
            ref={ref}
            {...rest}
            className="flex gap-2"
            onSelect={() => {
              onAddEval({
                functionData: {
                  type: "remote_eval",
                  eval_name: evalItem.name,
                  endpoint: evalItem.endpointUrl,
                  parameters: {},
                },
              });
            }}
            title={evalItem.name}
          >
            <span className="flex-1">{evalItem.name}</span>
          </DropdownMenuItem>
        );
      },
      [onAddEval],
    ),
  );

  const router = useRouter();
  const org = useOrg();

  return (
    <NestedDropdown<RemoteEvalItem>
      open={open}
      isInSubMenu={isInSubMenu}
      objectType="remote eval"
      align={dropdownMenuContentProps?.align}
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name?.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      DropdownItemComponent={RemoteEvalMenuItem}
      additionalActions={[
        {
          label: (
            <span
              className={cn(
                "flex items-center gap-1",
                isRefreshingSources && "opacity-50 pointer-events-none",
              )}
            >
              {isRefreshingSources ? (
                <Spinner className="size-3" />
              ) : (
                <RefreshCcw className="size-3" />
              )}
              Refresh sources
            </span>
          ),
          onSelect: async (e) => {
            e.preventDefault();
            await queryClient.invalidateQueries({
              queryKey: ["remote-evals", projectName],
            });
          },
        },
        {
          label: (
            <span className="flex items-center gap-1">
              <Settings2 className="size-3" />
              Configure remote eval sources
            </span>
          ),
          onSelect: (e) => {
            e.preventDefault();
            router.push(
              `${getProjectConfigurationLink({
                orgName: org.name,
                projectName,
              })}/remote-evals`,
            );
          },
        },
      ]}
    >
      {isInSubMenu
        ? null
        : (children ?? (
            <Button
              size="xs"
              variant="ghost"
              className="gap-1"
              isLoading={isLoading}
              Icon={Plus}
              {...buttonProps}
            >
              Remote
            </Button>
          ))}
    </NestedDropdown>
  );
};

import { useContext, useMemo } from "react";
import { useCostQuery } from "../../monitor/costQuery";
import { ProjectContext } from "./projectContext";
import { Skeleton } from "#/ui/skeleton";
import { useLatencyQuery } from "../../monitor/latencyQuery";
import { formatDuration } from "#/ui/table/formatters/duration-formatter";
import { useTokenCountQuery } from "../../monitor/tokenCountQuery";
import { useTTFTQuery } from "../../monitor/ttftQuery";
import { formatPriceValue } from "#/ui/trace/trace-metrics";
import {
  TIME_RANGE_TO_MILLISECONDS,
  type ChartTimeFrame,
} from "../../monitor/time-controls/time-range";
import { getDataSeries, type MetricsData } from "../../monitor/groups";

import {
  useMultiLineChart,
  type XTimeRange,
} from "#/ui/charts/multi-line-chart";
import { isEmpty } from "#/utils/object";
import { scaleTime } from "#/ui/charts/scale-time";
import { useRequestCountQuery } from "../../monitor/requestCountQuery";
import { cn } from "#/utils/classnames";
import { useFeatureFlags } from "#/lib/feature-flags";

const experimentIds: string[] = [];

export const LogsSparklines = ({
  context,
}: {
  context: "logs-page" | "overview-page";
}) => {
  const {
    flags: { projectSummaryMetrics },
  } = useFeatureFlags();
  return projectSummaryMetrics ? (
    <LogsSparklinesInternal context={context} />
  ) : null;
};

const LogsSparklinesInternal = ({
  context,
}: {
  context: "logs-page" | "overview-page";
}) => {
  const { projectId: projectIdFromContext } = useContext(ProjectContext);
  const projectId = projectIdFromContext ?? "";

  const startTime = useMemo(() => {
    const now = new Date();
    const newStartTimeMs = new Date(
      now.getTime() - TIME_RANGE_TO_MILLISECONDS["7d"],
    );

    return new Date(newStartTimeMs).toISOString();
  }, []);

  const chartTimeFrame: ChartTimeFrame = useMemo(() => {
    const now = new Date();
    return {
      start: now.getTime() - TIME_RANGE_TO_MILLISECONDS["7d"],
      end: now.getTime(),
    };
  }, []);

  const projectIds = useMemo(() => {
    return [projectId];
  }, [projectId]);

  const { data: tracesData, loading: isFetchingTraces } = useRequestCountQuery(
    projectIds,
    {
      chartTimeFrame,
      timeBucket: "day",
      experimentIds,
      from: "project_logs",
    },
  );

  const { costData, loading: isFetchingCost } = useCostQuery(projectIds, {
    chartTimeFrame,
    timeBucket: "day",
    experimentIds,
    from: "project_logs",
  });

  const { data: latencyData, loading: isFetchingLatency } = useLatencyQuery(
    projectIds,
    {
      chartTimeFrame,
      timeBucket: "day",
      experimentIds,
      from: "project_logs",
    },
  );

  const { data: tokenCountData, loading: isFetchingTokenCount } =
    useTokenCountQuery(projectIds, {
      chartTimeFrame,
      timeBucket: "day",
      experimentIds,
      from: "project_logs",
    });

  const { data: ttftData, loading: isFetchingTTFT } = useTTFTQuery(projectIds, {
    chartTimeFrame,
    timeBucket: "day",
    experimentIds,
    from: "project_logs",
  });

  return (
    <div className="flex flex-wrap gap-4">
      <Sparkline
        data={tracesData}
        series="count"
        isLoading={isFetchingTraces}
        startTime={startTime}
        context={context}
        value={new Intl.NumberFormat("en-US", {
          notation: "compact",
        }).format(
          tracesData?.reduce((acc, curr) => acc + Number(curr.count), 0) ?? 0,
        )}
      >
        <div className="text-xs font-normal text-primary-600">Traces</div>
      </Sparkline>
      <Sparkline
        data={costData}
        series="totalCost"
        isLoading={isFetchingCost}
        startTime={startTime}
        context={context}
        value={formatPriceValue(
          costData?.reduce((acc, curr) => acc + curr.totalCost, 0) ?? 0,
        )}
      >
        <div className="text-xs font-normal text-primary-600">LLM cost</div>
      </Sparkline>
      <Sparkline
        data={latencyData}
        series="p50_duration"
        isLoading={isFetchingLatency}
        startTime={startTime}
        context={context}
        value={formatDuration(
          (latencyData?.reduce(
            (acc, curr) => acc + (curr.p50_duration ?? 0),
            0,
          ) ?? 0) / (latencyData?.length || 1),
        )}
      >
        <div className="text-xs font-normal text-primary-600">Avg. latency</div>
      </Sparkline>
      <Sparkline
        data={tokenCountData}
        series="sum_tokens"
        isLoading={isFetchingTokenCount}
        startTime={startTime}
        context={context}
        value={`${new Intl.NumberFormat("en-US", {
          notation: "compact",
        }).format(
          tokenCountData?.reduce(
            (acc, curr) => acc + Number(curr.sum_tokens ?? 0),
            0,
          ) ?? 0,
        )}
        tok`}
      >
        <div className="text-xs font-normal text-primary-600">Tokens</div>
      </Sparkline>
      <Sparkline
        data={ttftData}
        series="p50_time_to_first_token"
        isLoading={isFetchingTTFT}
        startTime={startTime}
        context={context}
        value={formatDuration(
          (ttftData?.reduce(
            (acc, curr) => acc + Number(curr.p50_time_to_first_token ?? 0),
            0,
          ) ?? 0) / (ttftData?.length || 1),
        )}
      >
        <div className="text-xs font-normal text-primary-600">
          Avg. time to first token
        </div>
      </Sparkline>
    </div>
  );
};

function getEmptyMetadata(x: number) {
  return {
    time: new Date(x).toISOString(),
    count: BigInt(0),
  };
}

const CHART_HEIGHT = 18;

const Sparkline = ({
  data,
  series,
  isLoading,
  children,
  startTime,
  context,
  value,
}: {
  data?: MetricsData[] | null;
  series: string;
  isLoading: boolean;
  children: React.ReactNode;
  startTime: string;
  context: "logs-page" | "overview-page";
  value?: string;
}) => {
  const chartHeight = context === "logs-page" ? 12 : CHART_HEIGHT;
  const noData = isEmpty(data) || data.length === 0;

  const dataSeries = getDataSeries({
    data: data ?? [],
    selectedSeries: [series],
  });

  const xTimeRange: XTimeRange<{ time: string; count: bigint }> =
    useMemo(() => {
      return {
        allXValues: scaleTime({
          startTime,
          totalDurationMS: TIME_RANGE_TO_MILLISECONDS["7d"],
          timeBucket: "day",
        }),
        getEmptyMetadata,
      };
    }, [startTime]);

  const { chartProps } = useMultiLineChart({
    height: chartHeight,
    hidePoints: true,
    data: dataSeries,
    seriesMetadata: [
      {
        value: series,
        type: "metric",
      },
    ],
    xTimeRange,
    pointSize: 0,
  });

  const chartContent = useMemo(
    () => chartProps.renderChartContent(null, [true]),
    [chartProps],
  );

  if (isLoading || data === null || data === undefined) {
    return (
      <Skeleton
        className={cn("h-12 w-24 rounded-md", {
          "h-8": context === "logs-page",
        })}
      />
    );
  }

  return (
    <div
      className={cn("mr-5 flex flex-col h-12 items-start", {
        "h-8": context === "logs-page",
      })}
    >
      <div className="flex gap-2">
        <div
          className={cn("text-2xl font-medium tabular-nums", {
            "text-base": context === "logs-page",
          })}
        >
          {value}
        </div>
        {!noData && (
          <div className="relative w-12" ref={chartProps.chartRef}>
            <svg
              width="100%"
              height={chartHeight}
              className="relative z-20 overflow-visible"
            >
              <g
                style={{
                  transform: `translateY(${chartHeight + 8}px) scaleY(-1)`,
                }}
              >
                {chartContent}
              </g>
            </svg>
          </div>
        )}
      </div>
      {children}
    </div>
  );
};

import { EntityContextMenu } from "#/ui/entity-context-menu";
import Link from "next/link";
import { type GetRowsForExportFn } from "../experiments/[experiment]/(queries)/table-queries";
import { ReviewButton } from "../review-button";
import { getProjectConfigurationLink } from "../getProjectLink";
import { cn } from "#/utils/classnames";
import { buttonVariants } from "#/ui/button";
import { Radio } from "lucide-react";

export const LogsHeader = ({
  projectId,
  projectName,
  orgName,
  getRowsForExport,
  hideReviewButton,
  columnVisibility,
}: {
  projectId: string;
  projectName: string;
  orgName: string;
  getRowsForExport: GetRowsForExportFn;
  hideReviewButton?: boolean;
  columnVisibility?: Record<string, boolean>;
}) => {
  return (
    <div className="flex items-center gap-2 px-3 pb-2 pt-1 bg-primary-50">
      <h1 className="text-base font-semibold">Logs</h1>
      <EntityContextMenu
        objectType="project_log"
        objectId={projectId ?? ""}
        objectName={projectName}
        orgName={orgName}
        projectName={projectName}
        buttonClassName="h-7 w-7"
        getRowsForExport={getRowsForExport}
        exportName={[projectName, "logs"].join(" ")}
        onlyExportingLoadedRows={true}
        columnVisibility={columnVisibility}
      />
      <div className="grow" />
      <Link
        href={`${getProjectConfigurationLink({ orgName, projectName })}/online-scoring`}
        className={cn(buttonVariants({ size: "xs", variant: "ghost" }))}
      >
        <Radio className="size-3" />
        Online scoring
      </Link>
      {!hideReviewButton && <ReviewButton />}
    </div>
  );
};

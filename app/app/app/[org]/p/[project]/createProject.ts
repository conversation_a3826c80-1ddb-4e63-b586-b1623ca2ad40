export interface Project {
  id: string;
  name: string;
  org_id: string;
}

export async function createProject({
  orgId,
  projectName,
}: {
  orgId: string;
  projectName: string;
}): Promise<{
  error: unknown;
  project?: Project;
}> {
  const resp = await fetch("/api/project/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      org_id: orgId,
      project_name: projectName,
    }),
  });

  let project = undefined;
  let error = undefined;
  if (resp.ok) {
    project = await resp.json();
  } else {
    error = new Error(`Failed to register project: ${await resp.text()}`);
  }
  return { project, error };
}

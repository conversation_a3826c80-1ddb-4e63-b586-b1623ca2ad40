"use client";

import { useMemo } from "react";
import {
  type ScoreSummary,
  SUMMARY_DEFAULT_GROUP_KEY,
} from "@braintrust/local/query";
import {
  GROUP_BY_NONE_VALUE,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import { type AggregationType } from "#/utils/queries/aggregations";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useOrg } from "#/utils/user";
import { z } from "zod";

export type SummaryBreakdownData = {
  summary: {
    experiments: SummaryBreakdown[];
  };
  hasGroups?: boolean;
  aggregationTypes: Record<string, AggregationType>;
};

type SummaryBreakdown = {
  scores: ScoreSummary;
  groupedSummary: { [key: string]: ScoreSummary };
  groupTotals: ScoreSummary;
  experiment: ScoreSummaryExperiment;
};

const _errorSummarySchema = z
  .object({
    rowCount: z.number(),
    errorRate: z.number(),
    errorCount: z.number(),
  })
  .default({
    rowCount: 0,
    errorRate: 0,
    errorCount: 0,
  });

export type ErrorSummary = z.infer<typeof _errorSummarySchema>;

export type ScoreSummaryExperiment = {
  id: string;
  name: string;
  index: number;
  type: "base" | "comparison";
};

type SummaryData = {
  summary: { [key: string]: ScoreSummary } | null;
  groupedSummary: { [key: string]: ScoreSummary } | null;
};

type BaseSummaryMetadata = {
  summaryMetrics: string[];
  tableGrouping: string;
};

export const useSummaryBreakdown = ({
  summaryData,
  comparisonSummaryData,
  experiments,
  ignoredMeasures,
}: {
  experiments: ScoreSummaryExperiment[];
  summaryData: SummaryData & BaseSummaryMetadata;
  comparisonSummaryData: SummaryData[];
  ignoredMeasures: SelectionType[];
}) => {
  const summaryBreakdownData = useMemo(() => {
    if (!summaryData) return { summary: { experiments: [] } };

    const summary = {
      experiments: experiments.map((experiment, index) => {
        if (experiment.type === "base") {
          const { data: validData } = filterAndCountTotals({
            summaryData: summaryData.summary ?? {},
            ignoredMeasures,
            scoresInOuterGroup: false,
            comparisonData: comparisonSummaryData.flatMap((d) =>
              d.summary != null ? [d.summary] : [],
            ),
          });

          const { data: validGroupedData, groupTotals } = filterAndCountTotals({
            summaryData: summaryData.groupedSummary ?? {},
            ignoredMeasures,
            scoresInOuterGroup: false,
            comparisonData: comparisonSummaryData.flatMap((d) =>
              d.groupedSummary != null ? [d.groupedSummary] : [],
            ),
          });

          return {
            scores: validData[SUMMARY_DEFAULT_GROUP_KEY],
            groupedSummary: validGroupedData,
            groupTotals,
            experiment,
          };
        }

        const comparison = comparisonSummaryData[index - 1];

        const { data: validGroupedData, groupTotals } = filterAndCountTotals({
          summaryData: comparison?.groupedSummary ?? {},
          ignoredMeasures,
          scoresInOuterGroup: false,
          comparisonData: [],
          filterNulls: "compareAvg",
        });

        return {
          scores: comparison?.summary?.[SUMMARY_DEFAULT_GROUP_KEY] ?? {},
          groupedSummary: validGroupedData,
          groupTotals,
          experiment,
        };
      }),
    };

    return {
      summary,
      hasGroups:
        summaryData.tableGrouping != null &&
        summaryData.tableGrouping != GROUP_BY_NONE_VALUE,
    };
  }, [summaryData, comparisonSummaryData, ignoredMeasures, experiments]);

  const org = useOrg();
  const [aggregationTypes] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: org.id ?? "",
    key: "aggregationTypes",
  });

  return useMemo(() => {
    return { ...summaryBreakdownData, aggregationTypes };
  }, [summaryBreakdownData, aggregationTypes]);
};

const regressionFields = [
  "improvements",
  "regressions",
  "compare_keys",
  "all_keys",
  "compare_all_keys",
] as const;

function filterAndCountTotals({
  summaryData,
  ignoredMeasures,
  scoresInOuterGroup,
  comparisonData,
  filterNulls = "avg" as const,
}: {
  summaryData: { [key: string]: ScoreSummary };
  ignoredMeasures: SelectionType[];
  scoresInOuterGroup: boolean;
  comparisonData: { [key: string]: ScoreSummary }[];
  filterNulls?: keyof ScoreSummary[keyof ScoreSummary];
}) {
  const result: { [key: string]: ScoreSummary } = {};
  const groupTotals: ScoreSummary = {};

  for (const [groupName, scores] of Object.entries(summaryData)) {
    if (
      scoresInOuterGroup &&
      ignoredMeasures.some((st) => st.value === groupName) &&
      groupName !== "error"
    )
      continue;

    const totalKeys = Object.fromEntries(
      regressionFields.map((f) => [f, new Set<string>()]),
    );

    for (const [comparisonKey, scoreData] of Object.entries(scores)) {
      if (
        comparisonKey !== "error" &&
        ((!scoresInOuterGroup &&
          ignoredMeasures.some((st) => st.value === comparisonKey)) ||
          (filterNulls &&
            scoreData[filterNulls] == null &&
            comparisonData.every(
              (d) => d[groupName]?.[comparisonKey]?.compareAvg == null,
            )))
      ) {
        continue;
      }
      const comparisonAggregatedKeys = Object.fromEntries(
        regressionFields.map((f) => [f, new Set<string>()]),
      );
      regressionFields.forEach((f) =>
        scoreData[f]?.forEach((v) => comparisonAggregatedKeys[f].add(v)),
      );

      comparisonData.forEach((d) => {
        const comparisonScoreData = d[groupName]?.[comparisonKey];
        if (comparisonScoreData == null) return;

        regressionFields.forEach((f) =>
          comparisonScoreData[f]?.forEach((v) =>
            comparisonAggregatedKeys[f].add(v),
          ),
        );
      });

      result[groupName] = result[groupName] ?? {};
      result[groupName][comparisonKey] = {
        ...scoreData,
        // for multi-experiment we want to calculate aggregated regression/improvement counts
        // across each comparison experiment for each score/metric.
        // Currently the easiest way to do that is to mutate the original data directly
        ...Object.fromEntries(
          Object.entries(comparisonAggregatedKeys).map(([k, v]) => [
            k,
            Array.from(v),
          ]),
        ),
      };

      regressionFields.forEach((f) =>
        scoreData[f]?.forEach((v) => totalKeys[f].add(v)),
      );
    }

    comparisonData.forEach((d) => {
      if (!d[groupName]) return;

      for (const [comparisonKey, scoreData] of Object.entries(d[groupName])) {
        if (
          !scoresInOuterGroup &&
          ignoredMeasures.some((st) => st.value === comparisonKey)
        )
          continue;

        regressionFields.forEach((f) =>
          scoreData[f]?.forEach((v) => totalKeys[f].add(v)),
        );
      }
    });

    groupTotals[groupName] = {
      _name: "totals",
      avg: null,
      min: null,
      max: null,
      sum: null,
      stddev: null,
      diffAvg: null,
      diffMin: null,
      diffMax: null,
      diffSum: null,
      compareAvg: null,
      compareMin: null,
      compareMax: null,
      compareSum: null,
      equal: null,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      ...(Object.fromEntries(
        Object.entries(totalKeys).map(([k, v]) => [k, Array.from(v)]),
      ) as Record<(typeof regressionFields)[number], string[]>),
    };
  }

  return { data: result, count: 0, groupTotals };
}

// https://stackoverflow.com/a/52369951
export const SUMMARY_COLLATOR = new Intl.Collator("en", {
  numeric: true,
  sensitivity: "base",
});

export function scoreNameForSummary(scoreName: string) {
  if (scoreName.startsWith("scores.")) {
    return scoreName.slice("scores.".length);
  }
  if (scoreName.startsWith("metrics.")) {
    return scoreName.slice("metrics.".length);
  }

  return scoreName;
}

export function getColumnType(path: string[]) {
  switch (path[0]) {
    case "scores":
      return "score";
    case "metrics":
      return "metric";
    default:
      return "none";
  }
}

export function summaryFieldsForAggregationType(agg: AggregationType) {
  switch (agg) {
    case "avg":
      return ["avg" as const, "diffAvg" as const, "compareAvg" as const];
    case "min":
      return ["min" as const, "diffMin" as const, "compareMin" as const];
    case "max":
      return ["max" as const, "diffMax" as const, "compareMax" as const];
    case "sum":
      return ["sum" as const, "diffSum" as const, "compareSum" as const];
  }
}

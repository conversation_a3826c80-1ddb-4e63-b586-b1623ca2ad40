import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { type SavingState } from "#/ui/saving";
import {
  CancelSelectionButton,
  SelectionBarButton,
} from "#/ui/table/selection-bar";
import { type TableSelection } from "#/ui/table/useTableSelection";
import type { DataObjectType } from "#/utils/btapi/btapi";
import {
  DatasetIdField,
  OriginField,
  ProjectIdField,
  TagsField,
} from "#/utils/duckdb";
import {
  type OtherObjectInserterInfo,
  useOtherObjectInserter,
} from "#/utils/other-object-inserter";
import { pluralizeWithCount } from "#/utils/plurals";
import { parseObjectJSON } from "#/utils/schema";
import { useOrg } from "#/utils/user";
import { useContext, useState } from "react";
import { getDatasetLink } from "../../datasets/[dataset]/getDatasetLink";
import { TagBulkEditor } from "#/ui/trace/tags";
import { type DML } from "#/utils/mutable-object";
import { toast } from "sonner";
import { Button, buttonVariants } from "#/ui/button";
import Link from "next/link";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { FileJson, FileSpreadsheet, Trash } from "lucide-react";
import { objectLookupSupportedType } from "@braintrust/local";
import { DatasetDropdown } from "#/ui/dataset-dropdown";
import { type GetRowsForExportFn } from "./(queries)/table-queries";
import { downloadAsCSV, downloadAsJSON } from "#/utils/download";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { type ParsedQuery } from "@braintrust/btql/parser";
import { BasicTooltip } from "#/ui/tooltip";
import OptimizeDatasetButton from "../optimize-dataset-button";
import { useIsLoopEnabled } from "#/ui/optimization/optimization-chat";

export const ExperimentSelectionSection = (props: {
  selectionProps: TableSelection;
  orgName: string;
  orgId?: string;
  objectType: DataObjectType;
  dml?: DML;
  experimentId?: string;
  isPlayground?: boolean;
  getRowsForExport?: GetRowsForExportFn;
  exportName: string;
  additionalDeleteConfirmationDescription?: string;
  deleteButtonLabel?: string;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
}) => {
  const {
    selectedRowsNumber,
    deselectAllTableRows,
    getSelectedRowsWithData,
    refetchSelectedRowsWithData,
  } = props.selectionProps;
  const {
    orgName,
    orgId,
    objectType,
    dml,
    experimentId,
    getRowsForExport,
    exportName,
    additionalDeleteConfirmationDescription,
    deleteButtonLabel,
    refetchDataQueryFn,
  } = props;

  const org = useOrg();

  const isLoopEnabled = useIsLoopEnabled();

  const [_savingState, setSavingState] = useState<SavingState>("none");
  const addRowsToDataset = useOtherObjectInserter({
    objectType: "dataset",
    setSavingState,
  });
  const { projectId, projectName, orgDatasets, mutateDatasets } =
    useContext(ProjectContext);

  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: ({ datasetId, datasetName, projectName }) => {
        mutateDatasets();
        performAddRowsToDataset({
          datasetName: datasetName,
          datasetId: datasetId,
          projectName,
        });
      },
      orgId,
      projectName,
    });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [rowsToDelete, setRowsToDelete] = useState<any[]>([]);

  if (!projectId) {
    return null;
  }

  const performAddRowsToDataset = async ({
    datasetId,
    datasetName,
    projectName,
  }: {
    datasetId: string;
    datasetName: string;
    projectName: string;
  }) => {
    const selectedRowsWithData = refetchSelectedRowsWithData
      ? await refetchSelectedRowsWithData()
      : getSelectedRowsWithData();
    const rows = prepareRowsForCopy({
      orgName,
      projectName,
      projectId,
      objectType,
      objectId: experimentId,
      selectedRowsWithData,
      targetDataset: {
        id: datasetId,
        name: datasetName,
      },
      expectedFieldSource: "auto",
      cleanup: () => deselectAllTableRows(),
    });
    addRowsToDataset(rows);
  };

  return (
    <>
      <CancelSelectionButton
        onCancelSelection={deselectAllTableRows}
        selectedRowsNumber={selectedRowsNumber}
      />
      {!org.resources?.forbid_insert_datasets && (
        <DatasetDropdown
          datasets={orgDatasets}
          onCreateNewDataset={openCreateDatasetDialog}
          onSelectDataset={(data) => {
            performAddRowsToDataset({
              datasetName: data.name,
              datasetId: data.id,
              projectName: data.project_name,
            });
          }}
        >
          <Button size="xs">Add to dataset</Button>
        </DatasetDropdown>
      )}
      {getRowsForExport && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SelectionBarButton isDropdown>Download</SelectionBarButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              onSelect={() => {
                toast.promise(
                  async () => {
                    try {
                      const rowIds = getSelectedRowsWithData().map((r) => r.id);
                      const refetchDataQuery = refetchDataQueryFn?.(rowIds);
                      const data = await getRowsForExport({
                        withComparisons: props.isPlayground,
                        rowIds,
                        refetchDataQuery,
                      });
                      if (!data) {
                        throw new Error("Unable to download csv");
                      }
                      downloadAsCSV(exportName, data);
                    } catch (error) {
                      console.error(error);
                      throw error;
                    }
                  },
                  {
                    loading: `Downloading ${exportName}`,
                    success: "Download complete",
                    error: `Failed to download ${objectType}`,
                  },
                );
              }}
            >
              <FileSpreadsheet className="size-3 flex-none" />
              Download as CSV
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => {
                toast.promise(
                  async () => {
                    try {
                      const rowIds = getSelectedRowsWithData().map((r) => r.id);
                      const refetchDataQuery = refetchDataQueryFn?.(rowIds);
                      const data = await getRowsForExport({
                        withComparisons: props.isPlayground,
                        rowIds,
                        refetchDataQuery,
                      });
                      if (!data) {
                        throw new Error("Unable to download json");
                      }
                      downloadAsJSON(exportName + ".json", data);
                    } catch (error) {
                      console.error(error);
                      throw error;
                    }
                  },
                  {
                    loading: `Downloading ${exportName}`,
                    success: "Download complete",
                    error: `Failed to download ${objectType}`,
                  },
                );
              }}
            >
              <FileJson className="size-3 flex-none" />
              Download as JSON
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {dml && !props.isPlayground && (
        <TagBulkEditor dml={dml} selectionProps={props.selectionProps} />
      )}
      {isLoopEnabled && props.isPlayground && (
        <OptimizeDatasetButton selectionProps={props.selectionProps} />
      )}
      {dml && (
        <BasicTooltip
          tooltipContent={`Delete selected rows from current ${objectType.replace("_", " ")}`}
        >
          <SelectionBarButton
            onClick={() => {
              const rows = getSelectedRowsWithData(
                objectType === "experiment"
                  ? {
                      experiment_id: experimentId,
                      project_id: projectId,
                    }
                  : undefined,
              );
              setRowsToDelete(rows);
            }}
            Icon={Trash}
          />
        </BasicTooltip>
      )}
      {createDatasetModal}
      {dml && rowsToDelete.length > 0 && (
        <ConfirmationDialog
          open={rowsToDelete.length > 0}
          onOpenChange={() => setRowsToDelete([])}
          title="Delete rows"
          description={`Are you sure you want to delete ${pluralizeWithCount(
            rowsToDelete.length,
            "row",
            "rows",
          )}?${
            additionalDeleteConfirmationDescription
              ? ` ${additionalDeleteConfirmationDescription}`
              : ""
          }`}
          confirmText={deleteButtonLabel ?? "Delete"}
          onConfirm={async () => {
            deselectAllTableRows();
            const preparedDeletes = await dml.prepareDeletes(rowsToDelete);
            await dml.upsert(preparedDeletes, {
              onOptimisticUpdate: () => {
                toast.success(
                  `Deleted ${pluralizeWithCount(rowsToDelete.length, "row", "rows")}`,
                );
              },
            });
          }}
        />
      )}
    </>
  );
};

export function prepareRowsForCopy({
  orgName,
  projectName,
  projectId,
  objectType,
  objectId,
  selectedRowsWithData,
  targetDataset,
  expectedFieldSource,
  cleanup,
}: {
  orgName: string;
  projectName: string;
  projectId: string;
  objectType: DataObjectType;
  objectId?: string;
  selectedRowsWithData: unknown[];
  targetDataset: {
    id: string;
    name: string;
  };
  expectedFieldSource: "output" | "auto";
  cleanup: () => void;
}): OtherObjectInserterInfo {
  return {
    objectInfo: targetDataset,
    getRows: async () =>
      selectedRowsWithData.map((row) => {
        const rowJson = parseObjectJSON(objectType, row);
        if (!objectLookupSupportedType.safeParse(objectType).success) {
          console.error(rowJson);
          throw new Error(`Unsupported origin object type ${objectType}`);
        }
        // for playgrounds, the underlying row could be a dataset row, so just use that
        const originObjectType = rowJson.dataset_id ? "dataset" : objectType;
        const originObjectId = rowJson.dataset_id ?? objectId;
        if (!originObjectId) {
          console.error(rowJson);
          throw new Error(`Source of type ${objectType} does not have ID`);
        }
        return {
          id: rowJson.id,
          input: rowJson.input,
          metadata: rowJson.metadata,
          expected:
            expectedFieldSource == "output"
              ? rowJson.output
              : (rowJson.expected ?? rowJson.output),
          [TagsField]: rowJson.tags,
          [ProjectIdField]: projectId,
          [DatasetIdField]: targetDataset.id,
          [OriginField]: {
            object_type: originObjectType,
            object_id: originObjectId,
            id: rowJson.id,
            _xact_id: rowJson._xact_id,
          },
        };
      }),
    onSuccess: (numRows: number) => {
      const datasetName = targetDataset.name;
      toast(`Copied ${pluralizeWithCount(numRows, "row", "rows")} to dataset`, {
        action: (
          <Link
            className={buttonVariants({ size: "xs" })}
            href={getDatasetLink({ orgName, projectName, datasetName })}
            target="_blank"
          >
            Go to dataset
          </Link>
        ),
        duration: 10000,
        dismissible: true,
      });
    },
    cleanup,
  };
}

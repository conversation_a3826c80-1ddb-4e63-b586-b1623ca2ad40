import { useDuckDB, type ChannelSpec } from "#/utils/duckdb";
import { useCallback, useMemo, useRef, useState } from "react";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { fetchBtqlBinaryParquet, rowWithIdsSchema } from "#/utils/btql/btql";
import { useRealtimeChannel } from "#/utils/simple-channel";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { type AliasExpr, type Expr } from "@braintrust/btql/parser";
import { type Schema, type TypeMap } from "apache-arrow";
import {
  doubleQuote,
  ObjectDeleteField,
  singleQuote,
} from "@braintrust/local/query";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import type { AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { auditLogBaseEventSchema } from "@braintrust/local/api-schema";
import useEvent from "react-use-event-hook";
import { throttle } from "throttle-debounce";

export function useSummaryRealtime({
  btObject,
  summaryTableName,
  summarySchema,
  projectedColumns,
}: {
  btObject: {
    id?: string;
    type: DataObjectType;
  };
  summaryTableName: string | null;
  summarySchema?: Schema<TypeMap> | null;
  projectedColumns?: AliasExpr[];
}) {
  const { flags } = useFeatureFlags();
  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const summaryFields = useMemo(
    () =>
      summarySchema?.fields
        .filter(({ name }) => !["_update_nonce"].includes(name))
        ?.map(({ name }) => name),
    [summarySchema],
  );

  const [ready, setReady] = useState(0);
  if (summarySchema && ready === 0) {
    // If you are curious about why this is done directly while rendering instead of in an effect,
    // see https://react.dev/reference/react/useState#storing-information-from-previous-renders, and
    // and ask @colsondonohue if you have questions :P
    setReady((r) => r + 1);
  }

  const duck = useDuckDB();
  const builder = useBtqlQueryBuilder({});
  const rootSpanIdsToRefetch = useRef<Set<string>>(new Set());
  const idsToRefetch = useRef<Set<string>>(new Set());

  const refetchObjectIds = useEvent(() => {
    (async () => {
      const rootSpanIds = Array.from(rootSpanIdsToRefetch.current);
      const ids = Array.from(idsToRefetch.current);

      if (
        !btObject.id ||
        (rootSpanIds.length === 0 && ids.length === 0) ||
        !summaryTableName ||
        !summaryFields ||
        !duck
      ) {
        return;
      }

      rootSpanIdsToRefetch.current = new Set();
      idsToRefetch.current = new Set();
      let conn: AsyncDuckDBConnection | null = null;
      let parquetFile: string | null = null;
      try {
        [conn, parquetFile] = await Promise.all([
          duck.connect(),
          fetchBtqlBinaryParquet({
            args: {
              query: {
                filter: builder.or(
                  ...rootSpanIds.map(
                    (id): Expr => ({
                      op: "eq" as const,
                      left: { btql: "root_span_id" },
                      right: { op: "literal", value: id },
                    }),
                  ),
                  ...ids.map(
                    (id): Expr => ({
                      op: "eq" as const,
                      left: { btql: "id" },
                      right: { op: "literal", value: id },
                    }),
                  ),
                ),
                from: builder.from(btObject.type, [btObject.id], "summary"),
                select: [{ op: "star" }],
                sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                preview_length: 1000,
                custom_columns: projectedColumns,
              },
              brainstoreRealtime: true,
              disableLimit: true,
            },
            flags,
            apiUrl: org.api_url,
            getOrRefreshToken,
            duck,
          }),
        ]);

        if (!parquetFile) {
          console.warn(
            `Empty parquet file found for object ${btObject.id} of type ${btObject.type}. Skipping real-time update.`,
          );
          return;
        }

        const emptyResult = await conn.query(
          `SELECT scores, metrics FROM parquet_scan(${singleQuote(parquetFile)}) LIMIT 0`,
        );
        const { scoreNames: newScoreNames, metricNames: newMetricNames } =
          parseSummarySchemaScores(emptyResult.schema);

        const readScan = makeStructToMapScan({
          scoreNames: newScoreNames,
          metricNames: newMetricNames,
          summaryScan: `SELECT * FROM parquet_scan(${singleQuote(parquetFile)})`,
        });

        // NOTE: Keep this logic in sync with app/ui/summary-paginated-object-viewer.tsx
        // update if the incoming row has a higher or equal _xact_id because comments
        // are appended to the row in the api and don't affect the _xact_id
        await conn.query(`INSERT INTO ${doubleQuote(summaryTableName)}
          ${readScan}
          ON CONFLICT(id) DO UPDATE
          SET
            ${summaryFields
              .filter((name) => name !== "id")
              .map(
                (name) =>
                  `${doubleQuote(name)} = EXCLUDED.${doubleQuote(name)}`,
              )
              .join(", ")}
          WHERE EXCLUDED._xact_id >= _xact_id OR _xact_id IS NULL`);
        setReady((r) => r + 1);
      } catch (e) {
        // Re-add the ids to the set of ids to refetch, and schedule a refetch in 1s
        for (const id of rootSpanIds) {
          rootSpanIdsToRefetch.current.add(id);
        }
        for (const id of ids) {
          idsToRefetch.current.add(id);
        }
        setTimeout(() => {
          refetchObjectIds();
        }, 1000);
        console.warn(`Error updating table ${summaryTableName}:`, e);
      } finally {
        if (conn) {
          conn.close();
        }
        if (parquetFile) {
          duck.dropFile(parquetFile);
        }
      }
    })().catch(console.error);
  });

  const idsToDelete = useRef<Set<string>>(new Set());
  const idsToDeleteXactIds = useRef<Map<string, string>>(new Map());
  const deleteRows = useCallback(() => {
    (async () => {
      const ids = Array.from(idsToDelete.current);

      if (ids.length === 0 || !summaryTableName || !duck) {
        return;
      }

      const conn = await duck.connect();
      try {
        for (const id of ids) {
          const xactId = idsToDeleteXactIds.current.get(id);
          await conn.query(
            `UPDATE ${doubleQuote(summaryTableName)} SET ${doubleQuote(ObjectDeleteField)} = TRUE${xactId ? `, _xact_id = ${singleQuote(xactId)}` : ""} WHERE id = ${singleQuote(id)}`,
          );
          idsToDeleteXactIds.current.delete(id);
        }
        setReady((r) => r + 1);
      } catch (e) {
        console.error(`Error deleting rows from table ${summaryTableName}:`, e);
      } finally {
        conn.close();
      }
    })().catch(console.error);
  }, [duck, summaryTableName]);

  const realtimeSpec: ChannelSpec | null = useMemo(
    () =>
      btObject.id && flags.fastExperimentSummary
        ? {
            objectType: btObject.type,
            id: btObject.id,
            audit_log: false,
            shape: "summary",
          }
        : null,
    [btObject.id, btObject.type, flags.fastExperimentSummary],
  );

  const auditLogRealtimeSpec: ChannelSpec | null = useMemo(
    () =>
      btObject.id && flags.fastExperimentSummary
        ? {
            objectType: btObject.type,
            id: btObject.id,
            audit_log: true,
            shape: "summary",
          }
        : null,
    [btObject.id, btObject.type, flags.fastExperimentSummary],
  );
  const throttledRefetchIds = useMemo(
    () => throttle(1000, refetchObjectIds),
    [refetchObjectIds],
  );
  const debouncedDeleteRows = useDebouncedCallback(deleteRows, 100);

  const onEvent = useCallback(
    (event: unknown) => {
      const row = rowWithIdsSchema.safeParse(event);
      if (row.success) {
        if (!row.data._object_delete) {
          rootSpanIdsToRefetch.current.add(row.data.root_span_id);
          throttledRefetchIds();
        } else {
          idsToDelete.current.add(row.data.id);
          idsToDeleteXactIds.current.set(row.data.id, row.data._xact_id);
          debouncedDeleteRows();
        }
      }
    },
    [throttledRefetchIds, debouncedDeleteRows],
  );

  const onAuditLogEvent = useCallback(
    (event: unknown) => {
      const row = auditLogBaseEventSchema.safeParse(event);
      if (row.success && row.data.comment?.text) {
        idsToRefetch.current.add(row.data.origin.id);
        throttledRefetchIds();
      }
    },
    [throttledRefetchIds],
  );

  useRealtimeChannel({
    spec: realtimeSpec,
    onEvent,
  });

  useRealtimeChannel({
    spec: auditLogRealtimeSpec,
    onEvent: onAuditLogEvent,
  });

  return { ready };
}

export function parseSummarySchemaScores(
  summarySchema: Schema<TypeMap> | null,
): {
  scoreNames: string[];
  metricNames: string[];
} {
  const scoreField = summarySchema?.fields.find(
    ({ name }) => name === "scores",
  );
  const metricField = summarySchema?.fields.find(
    ({ name }) => name === "metrics",
  );
  return {
    scoreNames: scoreField
      ? (scoreField.type.children ?? []).map(({ name }) => name)
      : [],
    metricNames: metricField
      ? (metricField.type.children ?? []).map(({ name }) => name)
      : [],
  };
}

export function makeStructToMapScan({
  scoreNames,
  metricNames,
  summaryScan,
}: {
  scoreNames: string[];
  metricNames: string[];
  summaryScan: string;
}) {
  return `SELECT
    * REPLACE (
      ${scoreNames.length > 0 ? `map_from_entries([${scoreNames.map((f) => `(${singleQuote(f)}, scores.${doubleQuote(f)})`).join(",")}])` : "map{}::MAP(TEXT, DOUBLE)"} AS scores,
      ${metricNames.length > 0 ? `map_from_entries([${metricNames.map((f) => `(${singleQuote(f)}, metrics.${doubleQuote(f)})`).join(",")}])` : "map{}::MAP(TEXT, DOUBLE)"} AS metrics,
    ),
    NULL as ${doubleQuote(ObjectDeleteField)} FROM (${summaryScan})`;
}

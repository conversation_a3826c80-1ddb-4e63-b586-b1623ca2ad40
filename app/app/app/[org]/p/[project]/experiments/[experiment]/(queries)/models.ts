import { modelArrowSchema, modelTableDefinition } from "#/ui/prompts/models";
import { useAvailableModels } from "#/ui/prompts/models";
import { useTempDuckDataTable } from "#/utils/queries/useTempDuckTable";
import { useOrg } from "#/utils/user";
import { doubleQuote } from "@braintrust/local/query";

export const useModelScan = ({ id }: { id: string }) => {
  const org = useOrg();
  const { modelInserts } = useAvailableModels({ orgName: org.name });
  const { tableName: modelSpecTableName } = useTempDuckDataTable({
    tableNameHashKey: id ? `model_specs_${id}` : null,
    tableDefinition: modelTableDefinition,
    schema: modelArrowSchema,
    data: modelInserts,
    insertionCategory: "model_costs",
  });
  const modelSpecScan = modelSpecTableName
    ? `(SELECT * FROM ${doubleQuote(modelSpecTableName)})`
    : null;

  return modelSpecScan;
};

import { type RefObject, useCallback, useContext, useState } from "react";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { type PlaygroundCopilotContext } from "#/ui/copilot/playground";
import { type UIFunction } from "#/ui/prompts/schema";
import { Percent } from "lucide-react";
import { Button } from "#/ui/button";
import {
  DistributionChartInsight,
  type DistributionChartInsightProps,
} from "./DistributionChartInsight";
import { BasicTooltip } from "#/ui/tooltip";
import { PromptPreviewTooltip } from "../../playgrounds/[playground]/prompt-preview-tooltip";
import { FunctionDialog } from "#/ui/prompts/function-dialog";
import { ProjectContext } from "../../projectContext";
import { useOrg } from "#/utils/user";

const OUTPUT_NAMES = ["output"];

export const ExperimentSidebarScorers = ({
  copilotContext,
  scoreFields,
  distributionChartProps: {
    baseComparisonQuery,
    setFilter,
    signals,
    primaryExperiment,
    comparisonExperimentData,
  },
  distributionChartRef,
}: {
  copilotContext: PlaygroundCopilotContext;
  scoreFields: string[];
  distributionChartProps: Omit<DistributionChartInsightProps, "scoreField">;
  distributionChartRef: RefObject<{
    [scoreName: string]: { removeBrush: () => void };
  } | null>;
}) => {
  const org = useOrg();
  const { projectName, projectId } = useContext(ProjectContext);

  const savedScorerObjects = useScorerFunctions({});
  const { functions } = savedScorerObjects;

  const getScorerFunction = useCallback(
    (scorer: string) => {
      return Object.values(functions).find(
        (func) => func.slug === scorer || func.name === scorer,
      );
    },
    [functions],
  );

  const [openedScorerId, setOpenedScorerId] = useState<string>();
  const scorer = openedScorerId ? functions[openedScorerId] : null;

  return (
    <>
      {scoreFields.length === 0 ? (
        <div className="text-xs text-primary-500">None</div>
      ) : (
        <div className="-mx-2 flex flex-col gap-2 pt-1">
          {scoreFields.map((scorer, idx) => (
            <div key={idx} className="flex flex-col gap-1">
              <ScorerItem
                scorer={scorer}
                scorerFunction={getScorerFunction(scorer)}
                setOpenedScorerId={setOpenedScorerId}
              />
              <div className="px-2">
                <DistributionChartInsight
                  baseComparisonQuery={baseComparisonQuery}
                  scoreField={scorer}
                  signals={signals}
                  setFilter={setFilter}
                  primaryExperiment={primaryExperiment}
                  comparisonExperimentData={comparisonExperimentData}
                  ref={distributionChartRef}
                />
              </div>
            </div>
          ))}
        </div>
      )}
      {projectId && (
        <FunctionDialog
          identifier={`scorer-${scorer?.id}`}
          type="scorer"
          context="try_prompt"
          objectType="project_functions"
          outputNames={OUTPUT_NAMES}
          projectName={projectName}
          projectId={projectId}
          orgName={org.name}
          opened={!!openedScorerId}
          setOpened={(open) => {
            if (!!openedScorerId && !open) {
              setOpenedScorerId(undefined);
            }
          }}
          copilotContext={copilotContext}
          status={savedScorerObjects.status}
          initialFunction={scorer}
          mode={{ type: "view_saved" }}
        />
      )}
    </>
  );
};

const ScorerItem = ({
  scorer,
  scorerFunction,
  setOpenedScorerId,
}: {
  scorer: string;
  scorerFunction?: UIFunction & { projectName?: string };
  setOpenedScorerId: (id: string) => void;
}) => {
  if (scorerFunction) {
    return (
      <BasicTooltip
        side="right"
        align="start"
        disableHoverableContent={false}
        className="flex w-full max-w-sm flex-col gap-2.5 py-3"
        tooltipContent={
          <PromptPreviewTooltip
            version={scorerFunction._xact_id}
            prompt={scorerFunction.prompt_data}
            promptMeta={{
              name: scorerFunction.name,
              description: scorerFunction.description,
              metadata: scorerFunction.metadata,
            }}
          />
        }
      >
        <Button
          size="xs"
          Icon={Percent}
          variant="ghost"
          className="flex-1 justify-start gap-2 py-1.5 text-left"
          onClick={() => setOpenedScorerId(scorerFunction.id)}
        >
          <span className="flex-1 truncate">{scorerFunction.name}</span>
        </Button>
      </BasicTooltip>
    );
  }

  return (
    <div className="flex w-full items-center gap-2 px-2 py-1 text-xs">
      <Percent className="size-3 flex-none" />
      <span className="flex-1 truncate text-primary-700">{scorer}</span>
    </div>
  );
};

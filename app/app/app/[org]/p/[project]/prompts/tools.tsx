import { <PERSON><PERSON> } from "#/ui/button";
import { Bolt, X } from "lucide-react";

import { type TransactionId } from "@braintrust/core";
import { useMemo, useRef, useState } from "react";

import { pluralizeWithCount } from "#/utils/plurals";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { type SelectedTools, ToolsForm } from "./tools-form";
import { cn } from "#/utils/classnames";
import {
  type FunctionObjectType,
  type OpenAIModelParams,
} from "@braintrust/core/typespecs";

export const Tools = ({
  selectedTools: selectedToolsProp,
  onSave,
  copilotContext,
  rawToolsValue,
  toolChoice,
  type = "prompt",
  isReadOnly,
}: {
  selectedTools: SelectedTools;
  onSave: (
    tools: SelectedTools,
    rawTools: Record<string, unknown>[] | null,
    toolChoice: OpenAIModelParams["tool_choice"],
  ) => Promise<TransactionId | null>;
  copilotContext?: CopilotContextBuilder;
  rawToolsValue: Record<string, unknown>[] | null;
  toolChoice: OpenAIModelParams["tool_choice"];
  type?: FunctionObjectType;
  isReadOnly?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const totalNumTools = useMemo(() => {
    const toolFns =
      selectedToolsProp?.filter(
        (t): t is { type: "function"; id: string } => t.type === "function",
      ) ?? [];
    const numToolFns = toolFns.length;
    const numRawTools = rawToolsValue?.length ?? 0;
    return numToolFns + numRawTools;
  }, [selectedToolsProp, rawToolsValue]);

  const buttonLabel =
    totalNumTools === 0 ? "Tools" : pluralizeWithCount(totalNumTools, "tool");

  if (isReadOnly && totalNumTools === 0) {
    return null;
  }

  return (
    <div className="flex">
      <Dialog open={open} onOpenChange={setOpen} modal>
        <DialogTrigger asChild>
          <Button
            ref={buttonRef}
            size="xs"
            className={cn(totalNumTools > 0 && !isReadOnly && "rounded-r-none")}
            Icon={Bolt}
            onClick={() => {
              setOpen((o) => !o);
            }}
          >
            {buttonLabel}
          </Button>
        </DialogTrigger>
        <DialogContent
          onEscapeKeyDown={(e) => {
            e.stopPropagation();
          }}
          onPointerDownOutside={(e) => {
            e.preventDefault();
          }}
          className="flex flex-col gap-0 overflow-hidden p-0"
        >
          <DialogHeader className="m-0 flex-none border-b p-4 border-primary-100">
            <DialogTitle className="mb-1">Tools</DialogTitle>
            <DialogDescription>
              {isReadOnly
                ? `Tools from the library and/or raw tools used by this ${type}`
                : `Add tools from the library and/or raw tools to this ${type}`}
            </DialogDescription>
          </DialogHeader>

          {open && (
            <ToolsForm
              selectedTools={selectedToolsProp}
              onSave={onSave}
              copilotContext={copilotContext}
              initialRawToolsValue={rawToolsValue}
              onClose={() => setOpen(false)}
              toolChoice={toolChoice}
              isReadOnly={isReadOnly}
            />
          )}
        </DialogContent>
      </Dialog>
      {totalNumTools > 0 && !isReadOnly && (
        <Button
          size="xs"
          className="rounded-l-none border-l-0"
          Icon={X}
          onClick={() => onSave(null, null, toolChoice)}
        />
      )}
    </div>
  );
};

import { <PERSON><PERSON> } from "#/ui/button";
import PlainDropdown from "#/ui/plain-dropdown";
import { type TextEditorProps } from "#/ui/text-editor";
import {
  ChatPlaceholders,
  CompletionPlaceholders,
} from "#/utils/ai/placeholders";
import {
  type OpenAIModelParams,
  type MessageRole,
  type SavedFunctionId,
  type FunctionObjectType,
} from "@braintrust/core/typespecs";
import {
  type ModelSpec,
  modelProviderHasTools,
} from "@braintrust/proxy/schema";
import { memo, useCallback, useContext, useMemo } from "react";
import { cn } from "#/utils/classnames";
import {
  Clipboard,
  File,
  MessageSquare,
  Minus,
  Plus,
  PocketKnife,
  XIcon,
} from "lucide-react";
import { type TransactionId } from "#/utils/duckdb";
import { type PromptData } from "#/ui/prompts/schema";
import { PlainInput } from "#/ui/plain-input";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { MessageActionButton } from "../message-action-button";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { Tools } from "../tools";
import { ProjectContext } from "../../projectContext";
import React from "react";
import { OutputFormat } from "../output-format";
import { type structuredOutputSchema } from "../structured-output";
import { toast } from "sonner";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { deserializePlainStringAsJSON } from "braintrust";
import { InfoBanner } from "#/ui/info-banner";
import MessageBlockSynced from "./message-block-synced";
import { MESSAGE_ROLES, useSyncedPrompts } from "./use-synced-prompts";
import { type z } from "zod";
import ToolCallBlockSynced from "./tool-call-block-synced";
import { FocusScope } from "@react-aria/focus";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import Link from "next/link";

export type OnSaveToolFunctionsFn = (
  tools: SavedFunctionId[],
) => Promise<TransactionId | null>;

export type OnSaveResponseTypeFn = (
  type: "json_schema" | "json_object" | "text",
) => Promise<void>;

export type AgentPosition = "first" | "later";

export interface PromptBlockProps {
  isReadOnly?: boolean;
  agentPosition?: AgentPosition;
  data?: PromptData;
  model: string;
  allAvailableModels: { [name: string]: ModelSpec };
  triggerSave: () => Promise<TransactionId | null>;
  runPrompts: () => void;
  idx: number;
  extensions: TextEditorProps["extensions"];
  autoFocus?: boolean;
  disableTools?: boolean;
  copilotContext?: CopilotContextBuilder;
  isInPlayground?: boolean;
  datasetId?: string;
  promptId: string;
  type?: FunctionObjectType;
}

const PromptBlockSynced = memo(
  ({
    isReadOnly,
    agentPosition,
    data,
    model,
    allAvailableModels,
    triggerSave,
    runPrompts,
    extensions,
    disableTools,
    copilotContext,
    isInPlayground,
    datasetId,
    promptId,
    type,
  }: PromptBlockProps) => {
    const { orgName } = useContext(ProjectContext);

    const {
      updateStructuredOutput,
      updateResponseType,
      updateTools,
      addMessage,
      removeMessage,
      updateMessageRole,
      addMessagePart,
      updateMessageToolId_NO_SAVE,
      toggleMessageToolCalls,
      focusedEditor,
    } = useSyncedPrompts();

    const onSaveStructuredOutput = useCallback(
      async (data: z.infer<typeof structuredOutputSchema>) => {
        await updateStructuredOutput({ id: promptId, data });
      },
      [promptId, updateStructuredOutput],
    );

    const onSaveResponseType = useCallback(
      async (type: "json_schema" | "json_object" | "text") => {
        await updateResponseType({ id: promptId, type });
      },
      [promptId, updateResponseType],
    );

    const {
      format,
      flavor,
      multimodal: isModelMultimodal,
    } = allAvailableModels[model] || {};

    const isMultimodalActive = useCallback(
      (role?: MessageRole) => !!isModelMultimodal && role === "user",
      [isModelMultimodal],
    );

    const canCallTools = useCallback(
      (role?: MessageRole) =>
        modelProviderHasTools[format] && role === "assistant",
      [format],
    );

    const validRoles = MESSAGE_ROLES[format];
    const messages =
      data?.prompt?.type === "chat"
        ? data.prompt.messages
        : [{ ...data?.prompt, role: undefined }];

    const debouncedSave = useDebouncedCallback(triggerSave, 500);

    const isChat = flavor === "chat";
    const supportsTools =
      isChat && modelProviderHasTools[format] && !data?.parser && !disableTools;
    const supportsOutputFormat = isChat && !data?.parser;
    const supportsStructuredOutput = supportsTools;

    // TODO: bring back variable detection
    const hasVariable = false;

    const [promptVariableTipDismissed, setPromptVariableTipDismissed] =
      useEntityStorage({
        entityType: "dismissableMessages",
        key: "promptVariableTipDismissed",
        entityIdentifier: orgName,
      });

    const rawToolsString =
      data?.prompt?.type === "chat" ? data?.prompt?.tools : null;
    const rawTools: Record<string, unknown>[] | null = useMemo(() => {
      if (!rawToolsString) return null;
      return deserializePlainStringAsJSON(rawToolsString).value ?? [];
    }, [rawToolsString]);

    const variableTipText = (
      <>
        Try inserting dataset variables from{" "}
        <code className="font-semibold text-comparison-700">{`{{input}}`}</code>
        ,{" "}
        <code className="font-semibold text-comparison-700">{`{{expected}}`}</code>
        , or{" "}
        <code className="font-semibold text-comparison-700">{`{{metadata}}`}</code>
      </>
    );

    return (
      <FocusScope>
        <div className="flex flex-auto grow flex-col">
          {messages.map((message, index) => (
            <div
              className={cn(
                `group/prompt flex flex-col mb-2 pb-3 pt-1 bg-transparent border border-primary-200/50 focus-within:border-primary-200 hover:bg-primary-200/20 focus-within:hover:bg-primary-200/60 focus-within:bg-primary-200/60 transition-colors rounded-md px-3 min-h-[2.5lh] cursor-text`,
                {
                  "bg-transparent": isReadOnly,
                  grow: flavor === "completion",
                },
              )}
              key={`${index}${focusedEditor.current?.messageIndex === index && focusedEditor.current?.focusKey ? `-${focusedEditor.current?.focusKey}` : ""}`}
            >
              {isChat && (
                <div className="mb-1 flex h-7 items-center">
                  <div className="flex-auto text-sm font-medium capitalize">
                    <PlainDropdown
                      selectedOption={message?.role}
                      setSelectedOption={(role) =>
                        updateMessageRole({
                          id: promptId,
                          index,
                          newRole: role,
                          newIsMultimodal: isMultimodalActive(role),
                        })
                      }
                      className="-ml-1.5 rounded-md px-1.5 py-1 text-xs transition-all text-primary-700 hover:bg-primary-200"
                      disabled={isReadOnly}
                      options={validRoles}
                      itemClassName="capitalize"
                    />
                  </div>
                  {!isReadOnly && (
                    <div className="-mr-1 flex gap-1 opacity-0 transition-opacity group-hover/prompt:opacity-100">
                      <MessageActionButton
                        onClick={() => {
                          if (!message?.content) return;
                          if (typeof message.content === "string") {
                            navigator.clipboard.writeText(message.content);
                            toast.success("Message copied to clipboard");
                            return;
                          }
                          if (Array.isArray(message.content)) {
                            const textPart = message.content.find(
                              (part) => part.type === "text",
                            );
                            if (textPart && "text" in textPart) {
                              navigator.clipboard.writeText(textPart.text);
                              toast.success("Message copied to clipboard");
                            }
                          }
                        }}
                        tooltip="Copy message to clipboard"
                        Icon={Clipboard}
                      />
                      {canCallTools(message.role) && (
                        <MessageActionButton
                          onClick={() =>
                            toggleMessageToolCalls({
                              id: promptId,
                              index,
                            })
                          }
                          tooltip="Toggle tool calls"
                          Icon={PocketKnife}
                        />
                      )}

                      <MessageActionButton
                        onClick={() => {
                          addMessage({ id: promptId, afterIndex: index });
                        }}
                        tooltip="Add message"
                        Icon={Plus}
                      />
                      {messages.length > 1 && (
                        <MessageActionButton
                          onClick={() => {
                            removeMessage({ id: promptId, index });
                          }}
                          tooltip="Remove message"
                          Icon={Minus}
                        />
                      )}
                    </div>
                  )}
                </div>
              )}
              {message?.role === "tool" && (
                <div className="mb-2">
                  <PlainInput
                    value={message.tool_call_id}
                    onChange={(e) => {
                      updateMessageToolId_NO_SAVE({
                        id: promptId,
                        index,
                        toolId: e.target.value,
                      });
                      debouncedSave();
                    }}
                    disabled={isReadOnly}
                    required
                    placeholder="Enter tool call ID"
                    className="-ml-2 block h-8 w-full max-w-xs px-2 font-mono text-xs placeholder:font-inter"
                  />
                </div>
              )}
              <MessageBlockSynced
                isReadOnly={isReadOnly}
                data={message?.content}
                isMultimodal={isMultimodalActive(message.role)}
                triggerSave={triggerSave}
                runPrompts={runPrompts}
                extensions={extensions}
                textPlaceholder={
                  flavor === "completion"
                    ? CompletionPlaceholders[format]
                    : message?.role &&
                      ChatPlaceholders[format] &&
                      ChatPlaceholders[format][message.role]
                }
                copilotContext={copilotContext}
                promptId={promptId}
                messageIdx={index}
              />
              {!isReadOnly && isChat && isMultimodalActive(message.role) && (
                <div className="flex gap-1 pt-3">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        className="text-primary-700"
                        Icon={Plus}
                        size="xs"
                        isDropdown
                      >
                        Message part
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem
                        onSelect={() =>
                          // This is a hack to ensure the new message part is added and auto-focused _after_ radix refocuses the dropdown trigger
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "text",
                              }),
                            50,
                          )
                        }
                      >
                        <MessageSquare className="size-3" /> Text
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() =>
                          setTimeout(
                            () =>
                              addMessagePart({
                                id: promptId,
                                index,
                                type: "image_url",
                              }),
                            50,
                          )
                        }
                      >
                        <File className="size-3" /> File
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
              {message?.role === "assistant" && message?.tool_calls && (
                <ToolCallBlockSynced
                  value={message.tool_calls}
                  triggerSave={triggerSave}
                  runPrompts={runPrompts}
                  promptId={promptId}
                  messageIdx={index}
                  // TODO: bring back focus management
                  // autoFocus={autoFocus || focusedEditor === i}
                />
              )}
            </div>
          ))}
          {isChat && (
            <>
              {!promptVariableTipDismissed && isInPlayground && !isReadOnly && (
                <div className="mb-2 flex items-center gap-2 rounded-md border pl-3 pr-0 text-xs bg-primary-100/80 border-primary-200/50 text-primary-500">
                  <div className="flex-1 py-2 leading-normal">
                    {variableTipText}
                  </div>
                  <Button
                    size="icon"
                    transparent
                    className="size-8 flex-none text-primary-400"
                    Icon={XIcon}
                    onClick={() => {
                      setPromptVariableTipDismissed(true);
                    }}
                  />
                </div>
              )}
              {agentPosition === "first" && !isReadOnly && (
                <InfoBanner>{variableTipText}</InfoBanner>
              )}
              {agentPosition === "later" && !isReadOnly && (
                <InfoBanner>
                  Learn about{" "}
                  <Link
                    href="/docs/guides/functions/agents#variables"
                    target="_blank"
                    className="font-medium hover:underline"
                  >
                    variable interpolation
                  </Link>{" "}
                  in agents.
                </InfoBanner>
              )}
              {hasVariable &&
                !datasetId &&
                agentPosition !== "later" &&
                !isReadOnly && (
                  <div className="mb-2 rounded-md border p-3 text-xs bg-primary-100/80 border-primary-200/50 text-primary-500">
                    This prompt includes variables, but there is no dataset
                    selected.
                  </div>
                )}
              <div className="mb-2 flex flex-wrap gap-2">
                {!isReadOnly && (
                  <Button
                    size="xs"
                    onClick={(e) => {
                      e.preventDefault();
                      addMessage({ id: promptId });
                    }}
                    Icon={Plus}
                  >
                    Message
                  </Button>
                )}
                {supportsTools && (
                  <Tools
                    selectedTools={data?.tool_functions}
                    onSave={async (tools, rawTools, toolChoice) =>
                      await updateTools({
                        id: promptId,
                        toolFunctions: tools ?? [],
                        rawTools,
                        toolChoice,
                      })
                    }
                    copilotContext={copilotContext}
                    rawToolsValue={rawTools}
                    toolChoice={
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                      data?.options?.params
                        ?.tool_choice as OpenAIModelParams["tool_choice"]
                    }
                    isReadOnly={isReadOnly}
                    type={type}
                  />
                )}
                {supportsOutputFormat && (
                  <OutputFormat
                    onSaveStructuredOutput={onSaveStructuredOutput}
                    currentInitData={data}
                    supportsStructuredOutput={supportsStructuredOutput}
                    onSaveResponseType={onSaveResponseType}
                    isReadOnly={isReadOnly}
                  />
                )}
              </div>
            </>
          )}
        </div>
      </FocusScope>
    );
  },
);

PromptBlockSynced.displayName = "PromptBlockSynced";

export default PromptBlockSynced;

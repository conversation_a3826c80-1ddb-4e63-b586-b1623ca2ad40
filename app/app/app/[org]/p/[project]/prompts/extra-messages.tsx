import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "#/ui/dialog";
import { Input } from "#/ui/input";
import { type PropsWithChildren, useEffect, useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "#/ui/form";

export const extraMessagesSchema = z.object({
  expression: z.string(),
});

export type ExtraMessagesFormValues = z.infer<typeof extraMessagesSchema>;

export type OnSaveExtraMessagesFn = (expression: string) => void;

type ExtraMessagesProps = PropsWithChildren<{
  setExtraMessages: OnSaveExtraMessagesFn;
  currentExpression?: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}>;

export const ExtraMessagesDialog = ({
  currentExpression = "",
  setExtraMessages,
  children,
  isOpen,
  setIsOpen,
}: ExtraMessagesProps) => {
  const form = useForm<ExtraMessagesFormValues>({
    resolver: zodResolver(extraMessagesSchema),
    defaultValues: {
      expression: currentExpression,
    },
  });

  useEffect(() => {
    form.reset({ expression: currentExpression });
  }, [currentExpression, form]);

  const onSubmit = async (data: ExtraMessagesFormValues) => {
    setExtraMessages(data.expression);
    setIsOpen(false);
  };

  const hasValue = form.watch("expression");
  const clearValue = () => {
    form.setValue("expression", "", {
      shouldDirty: true,
      shouldValidate: true,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} modal={true}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="mb-2">
            Append messages from dataset
          </DialogTitle>
          <DialogDescription>
            Define a path from dataset rows to messages. If an array of messages
            is found at the specified path, the messages will be appended to the
            end of the prompt.
          </DialogDescription>
          <DialogDescription>
            An error will be thrown if the path does not include a valid array
            of messages.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col"
          >
            <FormField
              control={form.control}
              name="expression"
              render={({ field }) => (
                <FormItem className="mb-4">
                  <FormControl>
                    <Input
                      autoFocus
                      placeholder="Enter metadata path to messages"
                      className="h-9"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription className="flex justify-between">
                    If no path is provided, this feature will be disabled
                    {hasValue && (
                      <Button
                        transparent
                        size="inline"
                        className="text-xs text-accent-600"
                        onClick={clearValue}
                        type="button"
                      >
                        Clear
                      </Button>
                    )}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="submit"
                isLoading={form.formState.isSubmitting}
                disabled={!form.formState.isValid || !form.formState.isDirty}
                size="sm"
                variant="primary"
              >
                Save
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export const ExtraMessages = ({
  currentExpression,
  setExtraMessages,
  children,
}: {
  currentExpression?: string;
  setExtraMessages: OnSaveExtraMessagesFn;
  children: React.ReactNode;
}) => {
  const [isExtraMessagesOpen, setIsExtraMessagesOpen] = useState(false);

  return (
    <ExtraMessagesDialog
      currentExpression={currentExpression}
      setExtraMessages={setExtraMessages}
      isOpen={isExtraMessagesOpen}
      setIsOpen={setIsExtraMessagesOpen}
    >
      {children}
    </ExtraMessagesDialog>
  );
};

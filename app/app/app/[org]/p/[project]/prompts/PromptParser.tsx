import { Button, buttonVariants } from "#/ui/button";
import { Input } from "#/ui/input";
import { type PromptData } from "#/ui/prompts/schema";
import { Switch } from "#/ui/switch";
import { cn } from "#/utils/classnames";
import { type TransactionId } from "#/utils/duckdb";
import { produce } from "immer";
import { Trash2 } from "lucide-react";
import { useState, useRef, useEffect } from "react";

export function PromptParser({
  parser,
  saveParser,
  isReadOnly,
}: {
  parser: NonNullable<PromptData["parser"]>;
  saveParser: (parser: PromptData["parser"]) => Promise<TransactionId | null>;
  isReadOnly?: boolean;
}) {
  const choiceEntries = Object.entries(parser.choice_scores);
  const [entryIds, setEntryIds] = useState<string[]>([]);
  const nextIdRef = useRef(0);

  // Initialize or update entry IDs when choice entries change.
  // We need to do this so that entries have unique keys.
  useEffect(() => {
    if (entryIds.length !== choiceEntries.length) {
      setEntryIds(
        choiceEntries.map((_, i) => {
          if (entryIds[i]) return entryIds[i];
          return `entry-${nextIdRef.current++}`;
        }),
      );
    }
  }, [choiceEntries, entryIds]);

  return (
    <>
      {!isReadOnly && (
        <div className="mb-6 mt-4">
          <a
            className={cn(
              buttonVariants({
                size: "xs",
                variant: "border",
              }),
              "inline-flex gap-1.5 cursor-pointer",
            )}
            onClick={() => {
              saveParser({
                ...parser,
                use_cot: !parser?.use_cot,
              });
            }}
          >
            <Switch className="scale-90" checked={parser?.use_cot} />
            Use chain of thought (CoT)
          </a>
        </div>
      )}
      <div className="mb-4 flex flex-col gap-1.5">
        <div className="text-xs font-medium">Choice scores</div>
        <div className="mb-1 max-w-sm text-xs text-primary-500">
          Choice scores are required when using LLM-as-a-judge scorers. The
          model will be forced to choose one of the choices using a tool schema.
          All choices and scores must be unique.
        </div>
        {choiceEntries.length === 0 ? (
          <div className="mb-1 max-w-sm text-xs text-bad-700">
            No choice scores defined
          </div>
        ) : (
          <div className="flex max-w-sm gap-1 pr-8 text-xs text-primary-500">
            <div className="flex-1">Choice</div>
            <div className="w-24 text-right">Score (0 to 1)</div>
          </div>
        )}

        {choiceEntries.map(([label, value], idx) => (
          <ChoiceEntry
            key={entryIds[idx] || idx}
            index={idx}
            isReadOnly={isReadOnly}
            label={label}
            value={value}
            isLast={idx === 0 && choiceEntries.length === 1}
            isValidLabel={(label) => {
              const isUnique = choiceEntries.every(([l]) => l !== label);
              const isEmpty = label === "";
              return isUnique && !isEmpty;
            }}
            isValidValue={(value, index) => {
              return true; // Values do not need to be unique
            }}
            onChangeLabel={(newLabel) => {
              saveParser(
                produce(parser, (draft) => {
                  draft.choice_scores = Object.fromEntries(
                    Object.entries(draft.choice_scores).map(([k, v]) =>
                      k === label ? [newLabel, v] : [k, v],
                    ),
                  );
                }),
              );
            }}
            onChangeValue={(newValue) => {
              saveParser(
                produce(parser, (draft) => {
                  draft.choice_scores[label] = newValue;
                }),
              );
            }}
            onRemove={() => {
              const newEntryIds = [...entryIds];
              newEntryIds.splice(idx, 1);
              setEntryIds(newEntryIds);

              saveParser(
                produce(parser, (draft) => {
                  const entries = Object.entries(draft.choice_scores);
                  const newEntries = entries.filter((_, i) => i !== idx);
                  draft.choice_scores = Object.fromEntries(newEntries);
                }),
              );
            }}
          />
        ))}
        {!isReadOnly && (
          <div>
            <Button
              size="xs"
              disabled={choiceEntries.some(([label]) => label === "")}
              onClick={() => {
                const newId = `entry-${nextIdRef.current++}`;
                setEntryIds([...entryIds, newId]);

                saveParser(
                  produce(parser, (draft) => {
                    draft.choice_scores[""] = 0;
                  }),
                );
              }}
            >
              Add choice score
            </Button>
          </div>
        )}
      </div>
    </>
  );
}

function ChoiceEntry({
  isReadOnly,
  label,
  value,
  onChangeLabel,
  onChangeValue,
  onRemove,
  isValidLabel,
  isValidValue,
  index,
  isLast,
}: {
  isReadOnly?: boolean;
  label: string;
  value: number;
  onChangeLabel: (label: string) => void;
  onChangeValue: (value: number) => void;
  onRemove: () => void;
  isValidLabel: (label: string) => boolean;
  isValidValue: (value: number, index: number) => boolean;
  index: number;
  isLast: boolean;
}) {
  const [isLabelValid, setIsLabelValid] = useState(label !== "");
  const [isValueValid, setIsValueValid] = useState(true);

  return (
    <div className="flex max-w-sm gap-1 text-xs">
      <Input
        className={cn("h-7 flex-1 text-xs", {
          "border-bad-700 bg-bad-50 focus-visible:border-bad-700 focus-visible:ring-bad-700":
            !isLabelValid,
        })}
        required
        disabled={isReadOnly}
        defaultValue={label}
        onChange={(e) => {
          const newLabel = e.target.value;
          const newLabelValid = isValidLabel(newLabel);
          setIsLabelValid(newLabelValid);

          if (newLabel && newLabelValid) {
            onChangeLabel(newLabel);
          }
        }}
        placeholder="Enter choice label"
      />
      <Input
        className={cn("h-7 w-24 flex-none tabular-nums text-right text-xs", {
          "border-bad-700 bg-bad-50 focus-visible:border-bad-700 focus-visible:ring-bad-700":
            !isValueValid,
        })}
        disabled={isReadOnly}
        defaultValue={value}
        onChange={(e) => {
          const newValue = e.target.value;
          const numValue = parseFloat(newValue);
          const isValueValid =
            !isNaN(numValue) &&
            numValue >= 0 &&
            numValue <= 1 &&
            isValidValue(numValue, index);
          setIsValueValid(isValueValid);
          if (isValueValid) {
            onChangeValue(numValue);
          }
        }}
        min={0}
        required
        type="number"
        step={0.1}
        max={1}
        placeholder={value.toString()}
      />
      {!isReadOnly && (
        <Button
          Icon={Trash2}
          size="xs"
          className="flex-none"
          onClick={onRemove}
          disabled={isLast}
        />
      )}
    </div>
  );
}

"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import {
  type BackfillableObjectType,
  backfillableObjectTypeSchema,
  projectBackfillStatusSchema,
} from "@braintrust/local/app-schema";
import { zodErrorToString } from "#/utils/validation";
import { smartTimeFormat } from "#/ui/date";
import { useSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";
import { useOrg } from "#/utils/user";
import {
  runDeleteBackfilling,
  runEnableBackfilling,
} from "../../brainstore/ops";
import { Skeleton } from "#/ui/skeleton";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { Switch } from "#/ui/switch";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeysContext } from "react-hotkeys-hook";
import { Check<PERSON><PERSON>cle2, <PERSON>f<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { Button } from "#/ui/button";
import { capitalize } from "#/utils/string";
import { useGetRequest } from "#/utils/btapi/get";
import pluralize from "pluralize";
import { BasicTooltip } from "#/ui/tooltip";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

export const BrainstoreProjectConfiguration = ({
  orgId,
  projectId,
  apiUrl,
}: {
  orgId: string;
  projectId: string;
  apiUrl?: string;
}) => {
  const { data, error, requestErrorCode, refreshStatus } =
    useBrainstoreProjectStatus({ projectId, apiUrl });

  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();

  const enableBackfilling = useCallback(
    async (objectType: BackfillableObjectType | null) => {
      if (!projectId) {
        toast.error(`No project ID`);
        return;
      }
      if (!org.api_url) {
        toast.error(`Not logged in (yet)`);
        return;
      }

      try {
        await runEnableBackfilling({
          apiUrl: org.api_url,
          projectId,
          objectTypes: objectType ? [objectType] : undefined,
          sessionToken: await getOrRefreshToken(),
        });
      } catch (e) {
        toast.error(`${e}`);
        return;
      }

      refreshStatus();
    },
    [org.api_url, projectId, refreshStatus, getOrRefreshToken],
  );

  const deleteBackfilling = useCallback(async () => {
    if (!projectId) {
      toast.error(`No project ID`);
      return;
    }
    if (!org.api_url) {
      toast.error(`Not logged in (yet)`);
      return;
    }

    try {
      await runDeleteBackfilling({
        apiUrl: org.api_url,
        projectId,
        sessionToken: await getOrRefreshToken(),
      });
    } catch (e) {
      toast.error(`${e}`);
      return;
    }

    refreshStatus();
  }, [org.api_url, projectId, refreshStatus, getOrRefreshToken]);
  const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

  const anyEnabled = backfillableObjectTypeSchema.options.some(
    (objectType) =>
      !!data?.object_statuses.find(
        (r) => r.object_type === objectType && r.enabled,
      ),
  );
  const allEnabled = backfillableObjectTypeSchema.options.every(
    (objectType) =>
      !!data?.object_statuses.find(
        (r) => r.object_type === objectType && r.enabled,
      ),
  );
  const { enableScope, disableScope } = useHotkeysContext();

  if (requestErrorCode === 403) {
    return (
      <ConfigurationSectionLayout
        title="Brainstore"
        description="Brainstore is the next-gen backend designed for scalability and speed. Use this section to enable Brainstore for this project and manage data backfilling."
      >
        <TableEmptyState
          Icon={ShieldAlert}
          label={`You do not have permissions to manage Brainstore for this project. Please contact your organization administrator for assistance.`}
        />
      </ConfigurationSectionLayout>
    );
  }

  return (
    <ConfigurationSectionLayout
      title="Brainstore"
      description="Brainstore is the next-gen backend designed for scalability and speed. Use this section to enable Brainstore for this project and manage data backfilling."
    >
      {error && (
        <div className="mb-4 flex items-center gap-1.5 rounded-md border p-2 font-mono text-xs font-semibold bg-rose-500/5 border-rose-500/10 text-bad-700">
          {`${error}`}
        </div>
      )}

      {projectId && data ? (
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-3">
            {data.global_status.backfill_mode === "per_project" ? (
              <div className="flex items-baseline gap-3">
                <Switch
                  checked={allEnabled}
                  onClick={() => {
                    if (allEnabled) {
                      enableScope(HotkeyScope.ConfirmationModal);
                      setIsConfirmingDelete(true);
                    } else {
                      enableBackfilling(null);
                    }
                  }}
                  className="translate-y-0.5"
                />
                <div className="text-sm font-medium">
                  {allEnabled
                    ? "Enabled"
                    : "Enable" + (anyEnabled ? " for all object types" : "")}
                </div>
              </div>
            ) : (
              <div className="flex items-baseline gap-3">
                <div className="text-sm font-medium">
                  Running global backfill
                  {data.global_status.historical_backfill_progress
                    ? ` (${
                        Math.round(
                          data.global_status.historical_backfill_progress *
                            10000,
                        ) / 100
                      }% completed)`
                    : ""}
                  {data.global_status.comments_backfill_progress
                    ? `, comments backfill (${
                        Math.round(
                          data.global_status.comments_backfill_progress * 10000,
                        ) / 100
                      }% completed)`
                    : ""}
                </div>
              </div>
            )}
            <Button
              size="xs"
              className="flex-none"
              onClick={() => refreshStatus()}
            >
              <RefreshCw className="size-3" />
              Refresh
            </Button>
          </div>

          {backfillableObjectTypeSchema.options.map((objectType) => {
            const row = data.object_statuses.find(
              (r) => r.object_type === objectType,
            );
            if (!row) return null;
            return (
              <div className="flex flex-col gap-1" key={objectType}>
                <span className="text-sm">
                  {objectType === "project_logs"
                    ? "Logs"
                    : objectType === "playground_logs"
                      ? "Playground logs"
                      : capitalize(pluralize(objectType))}
                  {row.completed_initial_backfill_ts && (
                    <BasicTooltip
                      tooltipContent={`Initial backfill completed ${smartTimeFormat(
                        new Date(row.completed_initial_backfill_ts).getTime(),
                      )}.`}
                      side="right"
                    >
                      <span className="ml-1 inline-flex translate-y-[2px] items-center gap-1 rounded-full px-1.5 text-xs font-medium text-good-600">
                        <CheckCircle2 className="size-3" />
                        Live
                      </span>
                    </BasicTooltip>
                  )}
                </span>
                <span className="text-xs text-primary-600">
                  {row?.estimated_progress
                    ? `Backfill ${Math.round((row?.estimated_progress ?? 0) * 10000) / 100}% completed ` +
                      (row?.completed_initial_backfill_ts
                        ? `${smartTimeFormat(
                            new Date(
                              row.completed_initial_backfill_ts,
                            ).getTime(),
                          )}`
                        : "")
                    : row.backfill_target_sequence_id &&
                        row.backfill_target_sequence_id > 0
                      ? "Backfilling..."
                      : "No known records to backfill"}
                  {row?.last_backfilled_ts
                    ? ` (last updated ${smartTimeFormat(
                        new Date(row.last_backfilled_ts).getTime(),
                      )})`
                    : null}
                </span>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>
      )}
      {isConfirmingDelete && (
        <ConfirmationDialog
          open={isConfirmingDelete}
          onOpenChange={(open) => {
            setIsConfirmingDelete(open);
            if (!open) {
              disableScope(HotkeyScope.ConfirmationModal);
            }
          }}
          title="Are you sure you want to disable Brainstore for this project?"
          confirmText="Disable"
          onConfirm={() => {
            deleteBackfilling();
          }}
        >
          All backfilled data will be removed from Brainstore, and queries will
          access the previous generation datastore. You will not lose any data.
          If you re-enable Brainstore, the backfilled data will be reloaded,
          which may take a few minutes or several hours, depending on the size
          of the project.
        </ConfirmationDialog>
      )}
    </ConfigurationSectionLayout>
  );
};

export function useBrainstoreProjectStatus({
  projectId,
  apiUrl,
}: {
  projectId: string;
  apiUrl?: string;
}) {
  const {
    data,
    error: requestError,
    errorCode: requestErrorCode,
    refresh: refreshStatus,
  } = useGetRequest(
    projectId
      ? `/brainstore/backfill/status/project/${encodeURIComponent(projectId)}`
      : null,
    useMemo(() => ({}), []),
    {
      apiUrl,
    },
  );

  const [parsedData, parseError] = useMemo(() => {
    if (!data) {
      return [null, null];
    }
    const status = projectBackfillStatusSchema.safeParse(data);
    if (status.success) {
      return [status.data, null];
    } else {
      return [null, zodErrorToString(status.error, 2, false)];
    }
  }, [data]);

  const error = requestError ?? parseError;

  return {
    data: parsedData,
    error,
    requestErrorCode,
    refreshStatus,
  };
}

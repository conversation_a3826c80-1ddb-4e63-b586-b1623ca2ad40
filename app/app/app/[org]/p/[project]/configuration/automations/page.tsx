"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { BellIcon, History, PencilLine, Plus, Trash2 } from "lucide-react";
import { Input, inputClassName } from "#/ui/input";
import {
  type UpsertResponse,
  type UpsertProjectAutomation,
  performDelete,
  performUpsert,
} from "../configuration-client-actions";
import { toast } from "sonner";
import { useForm, type UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import ReactTextareaAutosize from "react-textarea-autosize";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { cn } from "#/utils/classnames";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useOrg } from "#/utils/user";
import {
  type LoadedBtSessionToken,
  sessionFetchProps,
  useSessionToken,
} from "#/utils/auth/session-token";
import {
  automationConfigSchema,
  automationEventTypeEnum,
  exportDefinitionTypeEnum,
  type ExportDefinition,
  type ProjectAutomation,
} from "@braintrust/core/typespecs";
import { parseQuery } from "@braintrust/btql/parser";
import { formatDuration, intervalToDuration } from "date-fns";
import { DataTextEditor } from "#/ui/data-text-editor";
import {
  useAutomationIdState,
  useAutomationIdStatusState,
} from "#/ui/query-parameters";
import { ErrorBanner } from "#/ui/error-banner";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { AwsInstructions } from "./aws-instructions";
import { newId } from "#/utils/btapi/btapi";
import { registerCronWithServiceToken } from "./api-requests";
import { btqlExportJobSchema } from "@braintrust/local/api-schema";
import { createApiKey } from "#/app/app/[org]/settings/api-keys/api-key";
import { useFeatureFlags } from "#/lib/feature-flags";
import { AutomationRuns } from "./automation-runs";

type AutomationTestResponse =
  | { kind: "success"; payload: Record<string, unknown> }
  | { kind: "error"; message: string };

async function performAutomationTest({
  apiUrl,
  sessionToken,
  projectId,
  row,
}: {
  apiUrl: string;
  sessionToken: LoadedBtSessionToken;
  projectId: string;
  row: UpsertProjectAutomation;
}): Promise<AutomationTestResponse> {
  const resp = await (() => {
    const { sessionHeaders, sessionExtraFetchProps } =
      sessionFetchProps(sessionToken);
    return fetch(`${apiUrl}/test-automation`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...sessionHeaders,
      },
      body: JSON.stringify({
        project_id: projectId,
        ...row,
      }),
      ...sessionExtraFetchProps,
    });
  })();

  if (resp.ok) {
    return await resp.json();
  } else {
    return {
      kind: "error",
      message: `Error while testing automation: ${await resp.text()}`,
    };
  }
}

const Automations = () => {
  // "new" means we're creating a new automation
  const [selectedAutomationId, setSelectedAutomationId] =
    useAutomationIdState();

  const [selectedStatus, setSelectedStatus] = useAutomationIdStatusState();

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
  } = useContext(ProjectContext);
  const { automations } = config;

  const [confirmDiscard, setConfirmDiscard] = useState(false);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);

  const selectedAutomation = automations.find(
    (a) => a.id === selectedAutomationId,
  );

  const selectedAutomationStatus = automations.find(
    (a) => a.id === selectedStatus,
  );

  const form = useForm<z.infer<typeof projectAutomationFormSchema>>({
    resolver: async (values, context, options) => {
      const result = await zodResolver(projectAutomationFormSchema)(
        values,
        context,
        options,
      );

      if (!values.name) {
        result.errors = {
          ...result.errors,
          name: { message: "Automation name is required" },
        };
      }

      switch (values.config.event_type) {
        case "logs":
          if (values.config.btql_filter) {
            try {
              // TODO: should we swap with `useClauseChecker("project_logs", true)`?
              const query = parseQuery(
                `select: * | from: project_logs('fake_project') | filter: ${values.config.btql_filter}`,
              );
              if (!query.filter) {
                result.errors = {
                  ...result.errors,
                  btql_filter: { message: "Invalid BTQL filter" },
                };
              }
            } catch {
              result.errors = {
                ...result.errors,
                btql_filter: { message: "Invalid BTQL filter" },
              };
            }
          }

          if (!values.config.action.url) {
            result.errors = {
              ...result.errors,
              action: { message: "Webhook URL is required" },
            };
          } else {
            try {
              new URL(values.config.action.url);
            } catch (e) {
              result.errors = {
                ...result.errors,
                action: { message: "Invalid webhook URL" },
              };
            }
          }
          break;
        case "btql_export":
          if (
            values.config.export_definition &&
            values.config.export_definition.type === "btql_query"
          ) {
            try {
              const query = parseQuery(
                values.config.export_definition.btql_query,
              );
              if (query.sort) {
                result.errors = {
                  ...result.errors,
                  "config.export_definition.btql_query": {
                    message: "Cannot specify a custom sort.",
                  },
                };
              }
              if (query.limit) {
                result.errors = {
                  ...result.errors,
                  "config.export_definition.btql_query": {
                    message: "Cannot specify a limit.",
                  },
                };
              }
              if (query.cursor) {
                result.errors = {
                  ...result.errors,
                  "config.export_definition.btql_query": {
                    message: "Cannot specify a cursor.",
                  },
                };
              }
            } catch {
              result.errors = {
                ...result.errors,
                "config.export_definition.btql_query": {
                  message: "Invalid BTQL query",
                },
              };
            }
          }

          if (values.config.export_path) {
            try {
              const url = new URL(values.config.export_path);
              // remove the trailing colon
              if (!supportedProtocols.includes(url.protocol.slice(0, -1))) {
                result.errors = {
                  ...result.errors,
                  "config.export_path": {
                    message: `Invalid export path. Protocol "${url.protocol}" is not supported. Must be one of: ${supportedProtocols.join(", ")}`,
                  },
                };
              }
            } catch (e) {
              result.errors = {
                ...result.errors,
                "config.export_path": { message: "Invalid export path" },
              };
            }
          } else {
            result.errors = {
              ...result.errors,
              "config.export_path": { message: "Export path is required" },
            };
          }
          break;
        default:
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- i just want an exhaustive switch statement man
          const _exhaustiveCheck: never = values as never;
          throw new Error(`Unhandled event type: ${_exhaustiveCheck}`);
      }

      return result;
    },
    defaultValues: getReset(
      selectedAutomation,
      selectedAutomation?.config.event_type ?? "logs",
    ),
  });

  const reset = useCallback(() => {
    setSelectedAutomationId(null);
    setConfirmDiscard(false);
    form.reset();
  }, [form, setSelectedAutomationId]);

  const watchedEventType = form.watch("config.event_type");
  useEffect(() => {
    form.reset(
      getReset(
        selectedAutomation,
        selectedAutomation?.config.event_type ?? watchedEventType,
      ),
    );
  }, [watchedEventType, selectedAutomation, form]);

  if (!projectId) {
    throw new Error("Cannot instantiate Automations outside project");
  }

  return (
    <ConfigurationSectionLayout
      title="Automations"
      description="Configure automations to trigger actions based on events"
      action={
        automations.length > 0 ? (
          <Button
            size="sm"
            Icon={Plus}
            onClick={(_e) => {
              setSelectedAutomationId("new");
            }}
          >
            Automation
          </Button>
        ) : undefined
      }
    >
      {automations.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow>
              <TableHead className="w-36 truncate">Automation name</TableHead>
              <TableHead className="w-60 truncate">Event type</TableHead>
              <TableHead className="w-60 truncate">Action type</TableHead>
              <TableHead className="flex-1 truncate">Description</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {automations.map((automation, index) => (
              <TableRow key={index} className="py-2">
                <TableCell className="w-36 truncate">
                  {automation.name}
                </TableCell>
                <TableCell className="w-60 truncate">
                  {formatEventType(automation.config.event_type)}
                </TableCell>
                <TableCell className="w-60 truncate">
                  {formatActionType(automation.config)}
                </TableCell>
                <TableCell className="flex-1 truncate">
                  {automation.description ?? ""}
                </TableCell>
                <TableCell className="w-28 justify-end gap-1 pr-0">
                  {automation.config.event_type === "btql_export" && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="size-8"
                      onClick={() => {
                        setSelectedStatus(automation.id);
                      }}
                      Icon={History}
                      iconClassName="size-4"
                    />
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={() => {
                      setSelectedAutomationId(automation.id);
                    }}
                    Icon={PencilLine}
                    iconClassName="size-4"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setConfirmDeleteId(automation.id);
                    }}
                    Icon={Trash2}
                    iconClassName="size-4"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <BellIcon className="text-primary-500" />
              <p className="text-base text-primary-500">
                No automations defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedAutomationId("new");
                }}
                Icon={Plus}
              >
                Create automation
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <div className="mt-4">
        <Button
          size="sm"
          onClick={(_e) => {
            setSelectedAutomationId("new");
          }}
        >
          Add automation
        </Button>
      </div>

      <Dialog
        open={!!selectedAutomationStatus}
        onOpenChange={(o) => {
          if (!o) {
            setSelectedStatus(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg md:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Automation runs</DialogTitle>
            <DialogDescription>
              View previous runs and manually trigger a new run
            </DialogDescription>
          </DialogHeader>
          <AutomationRuns automation={selectedAutomationStatus ?? null} />
        </DialogContent>
      </Dialog>

      <Dialog
        open={selectedAutomationId === "new" || !!selectedAutomation}
        onOpenChange={(o) => {
          if (!o) {
            if (form.formState.isDirty) {
              setConfirmDiscard(true);
            } else {
              reset();
            }
          }
        }}
      >
        <DialogContent className="sm:max-w-lg md:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Configure automation</DialogTitle>
            <DialogDescription>
              Configure an automation to trigger actions based on events
            </DialogDescription>
          </DialogHeader>
          <AutomationForm
            form={form}
            apiUrl={apiUrl}
            row={selectedAutomation ?? null}
            onClose={() => {
              reset();
            }}
            test={async (row) => {
              if (!apiUrl) {
                return { kind: "error", message: "Not logged in (yet)" };
              }
              const sessionToken = await getOrRefreshToken();
              return await performAutomationTest({
                apiUrl,
                sessionToken,
                projectId,
                row,
              });
            }}
            save={async (row) => {
              if (!apiUrl) {
                return { kind: "error", message: "Not logged in (yet)" };
              }
              const sessionToken = await getOrRefreshToken();
              return await performUpsert({
                apiUrl,
                sessionToken,
                objectType: "project_automation",
                row,
                projectId,
                mutate,
              });
            }}
            deleteAutomation={async (id) => {
              if (!apiUrl) {
                throw new Error("Not logged in (yet)");
              }
              const sessionToken = await getOrRefreshToken();
              await performDelete({
                apiUrl,
                sessionToken,
                objectType: "project_automation",
                rowId: id,
                mutate,
              });
            }}
          />
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        open={confirmDiscard}
        onOpenChange={setConfirmDiscard}
        title="Discard changes"
        description="Are you sure you want to discard changes?"
        confirmText="Discard"
        onConfirm={() => {
          reset();
        }}
      />

      <ConfirmationDialog
        open={!!confirmDeleteId}
        onOpenChange={(o) => {
          if (!o) {
            setConfirmDeleteId(null);
          }
        }}
        title="Delete automation"
        description="Are you sure you want to delete this automation? This action cannot be undone."
        confirmText="Delete"
        onConfirm={async () => {
          if (!apiUrl) {
            toast.error("Not logged in (yet)");
            return;
          }
          const sessionToken = await getOrRefreshToken();

          if (confirmDeleteId) {
            return toast.promise(
              performDelete({
                apiUrl,
                sessionToken,
                objectType: "project_automation",
                rowId: confirmDeleteId,
                mutate,
              }),
              {
                loading: "Deleting automation...",
                success: "Automation deleted",
                error: "Failed to delete automation",
              },
            );
          }
        }}
      />
    </ConfigurationSectionLayout>
  );
};

const projectAutomationFormSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  config: automationConfigSchema,
});

type ProjectAutomationForm = z.infer<typeof projectAutomationFormSchema>;

const getReset = (
  row: ProjectAutomation | null | undefined,
  eventType: ProjectAutomation["config"]["event_type"],
): ProjectAutomationForm => {
  if (eventType === "logs") {
    const config =
      row?.config.event_type === "logs"
        ? row.config
        : ({
            event_type: "logs",
            btql_filter: "",
            interval_seconds: 60 * 60, // 1 hour
            action: {
              type: "webhook",
              url: "",
            },
          } as const);
    return {
      name: row?.name ?? "",
      description: row?.description ?? undefined,
      config,
    };
  } else if (eventType === "btql_export") {
    const config =
      row?.config.event_type === "btql_export"
        ? row.config
        : ({
            event_type: "btql_export",
            export_definition: { type: "log_traces" },
            export_path: "",
            format: "jsonl",
            interval_seconds: 60 * 60, // 1 hour
            credentials: {
              type: "aws_iam",
              role_arn: "",
              external_id: `${newId()}`,
            },
            batch_size: undefined,
          } as const);
    return {
      name: row?.name ?? "",
      description: row?.description ?? undefined,
      config,
    };
  } else {
    const _exhaustiveCheck: never = eventType;
    throw new Error(`Unhandled event type: ${_exhaustiveCheck}`);
  }
};

function AutomationForm({
  form,
  row,
  test,
  save,
  deleteAutomation,
  onClose,
}: {
  form: UseFormReturn<ProjectAutomationForm>;
  apiUrl: string;
  row: ProjectAutomation | null;
  test: (row: UpsertProjectAutomation) => Promise<AutomationTestResponse>;
  save: (row: UpsertProjectAutomation) => Promise<UpsertResponse>;
  deleteAutomation: (id: string) => Promise<void>;
  onClose: VoidFunction;
}) {
  const action = row ? "Update" : "Create";

  const { id: orgId, name: orgName } = useOrg();
  const project = useContext(ProjectContext);
  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: orgName },
  });

  const projectSummary = useMemo(() => {
    return projects?.find((p) => p.project_id === project?.projectId);
  }, [projects, project]);

  const watchedEventType = form.watch("config.event_type");
  const watchedInterval = form.watch("config.interval_seconds");
  const selectedInterval = intervalOptions.find(
    (o) => o.value === watchedInterval,
  );

  const watchedFormat = form.watch("config.format");
  const selectedFormat = formatOptions.find((o) => o.value === watchedFormat);

  const watchedExportPath = form.watch("config.export_path");
  const watchedExportDefinition = form.watch("config.export_definition");
  const watchedExternalId = form.watch("config.credentials.external_id");

  const [isRunningTest, setIsRunningTest] = useState(false);
  const [testResult, setTestResult] = useState<AutomationTestResponse | null>(
    null,
  );

  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const { flags } = useFeatureFlags();

  const handleSubmit = form.handleSubmit(async (data) => {
    const isCreate = !row?.id;
    const result = await save({
      id: row?.id,
      name: data.name,
      description: data.description,
      config: data.config,
    });

    if (result.kind === "duplicate") {
      form.setError(
        "name",
        {
          type: "value",
          message:
            "Name already exists. Please choose a unique automation name.",
        },
        {
          shouldFocus: true,
        },
      );
      return;
    } else if (result.kind === "error") {
      toast.error(`Failed to create or update automation`, {
        description: result.message,
      });
      return;
    }

    if (isCreate && data.config.event_type === "btql_export") {
      const toastId = toast.loading("Creating a service token...");
      try {
        // TODO: This should create a service token rather than an API key owned by this user.
        const apiKey = await createApiKey({
          name: `automation-${result.id}`,
          orgId: org.id!,
        });

        toast.loading("Registering the automation...", { id: toastId });

        const sessionToken = await getOrRefreshToken();
        await registerCronWithServiceToken({
          automationId: result.id,
          apiUrl: org.api_url,
          sessionToken,
          cronJob: btqlExportJobSchema.parse({
            type: "btql_export",
          }),
          serviceToken: apiKey,
        });

        toast.success("Automation created successfully", { id: toastId });
      } catch (error) {
        toast.error(`Failed to initialize cron job`, {
          description: error instanceof Error ? error.message : `${error}`,
          id: toastId,
        });
        await deleteAutomation(result.id);
      }
    }

    onClose();
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Automation name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter automation name"
                  {...field}
                  value={field.value ?? ""}
                  autoComplete="none"
                  data-1p-ignore="true"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <div>
              <CollapsibleSection
                title="Description (optional)"
                defaultCollapsed={!row?.description}
              >
                <FormItem>
                  <FormControl>
                    <ReactTextareaAutosize
                      {...field}
                      className={inputClassName}
                      rows={2}
                      placeholder="Enter description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </CollapsibleSection>
            </div>
          )}
        />
        <FormField
          control={form.control}
          name="config.event_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Event type</FormLabel>
              <FormControl>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button isDropdown className="w-full">
                      <span className="flex-1 truncate text-left">
                        {formatEventType(field.value)}
                      </span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-[462px]">
                    {automationEventTypeEnum.options
                      .filter(
                        (o) =>
                          o === "logs" ||
                          (flags.automationsExport && o === "btql_export"),
                      )
                      .map((o, idx) => (
                        <DropdownMenuCheckboxItem
                          key={idx}
                          checked={field.value === o}
                          onSelect={() => field.onChange(o)}
                        >
                          {formatEventType(o)}
                        </DropdownMenuCheckboxItem>
                      ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </FormControl>
              <FormDescription>
                More event types will be available soon.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Conditional fields based on event_type */}
        {watchedEventType === "logs" && (
          <>
            <FormField
              control={form.control}
              name="config.btql_filter"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BTQL filter</FormLabel>
                  <FormControl>
                    <BtqlEditor
                      mode="expr"
                      value={field.value}
                      onValueChange={(v) =>
                        field.onChange(v, {
                          shouldDirty: true,
                          shouldTouch: true,
                        })
                      }
                      onMetaEnter={() => {
                        form.setValue("config.btql_filter", field.value, {
                          shouldDirty: true,
                          shouldTouch: true,
                        });
                      }}
                      projectData={projectSummary ? [projectSummary] : []}
                      className={cn(
                        inputClassName,
                        "font-mono placeholder:font-inter",
                      )}
                      placeholder="Enter BTQL filter"
                    />
                  </FormControl>
                  <FormDescription>
                    Filter logs using BTQL. If no filter is provided, the
                    automation will apply to all logs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.interval_seconds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Interval</FormLabel>
                  <FormControl>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button isDropdown className="w-full">
                          <span className="flex-1 truncate text-left">
                            {selectedInterval?.label ?? "Select interval"}
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[462px]">
                        {intervalOptions.map((o) => (
                          <DropdownMenuCheckboxItem
                            key={o.value}
                            checked={field.value === o.value}
                            onSelect={() => field.onChange(o.value)}
                          >
                            {o.label}
                          </DropdownMenuCheckboxItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </FormControl>
                  <FormDescription>
                    Trigger the webhook once in this time interval
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.action.url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Webhook URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter webhook URL"
                      {...field}
                      value={field.value ?? ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "") {
                          field.onChange(null, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        } else {
                          field.onChange(value, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {watchedEventType === "btql_export" && (
          <>
            <FormField
              control={form.control}
              name="config.export_definition.type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data to export</FormLabel>
                  <FormControl>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button isDropdown className="w-full">
                          <span className="flex-1 truncate text-left">
                            {formatExportType(field.value)}
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[462px]">
                        {exportDefinitionTypeEnum.options.map((o, idx) => (
                          <DropdownMenuCheckboxItem
                            key={idx}
                            checked={field.value === o}
                            onSelect={() => field.onChange(o)}
                          >
                            {formatExportType(o)}
                          </DropdownMenuCheckboxItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </FormControl>
                  <FormDescription>
                    Select the type of data to export.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedExportDefinition &&
              watchedExportDefinition.type === "btql_query" && (
                <FormField
                  control={form.control}
                  name="config.export_definition.btql_query"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>BTQL query</FormLabel>
                      <FormControl>
                        <BtqlEditor
                          mode="query"
                          value={field.value}
                          onValueChange={(v) =>
                            field.onChange(v, {
                              shouldDirty: true,
                              shouldTouch: true,
                            })
                          }
                          onMetaEnter={() => {
                            form.setValue(
                              "config.export_definition.btql_query",
                              field.value,
                              {
                                shouldDirty: true,
                                shouldTouch: true,
                              },
                            );
                          }}
                          projectData={projectSummary ? [projectSummary] : []}
                          className={cn(
                            inputClassName,
                            "font-mono placeholder:font-inter min-h-24",
                          )}
                          placeholder="Enter BTQL query"
                        />
                      </FormControl>
                      <FormDescription>
                        Define your export as a BTQL query.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

            <FormField
              control={form.control}
              name="config.export_path"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>s3 export path</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter export path"
                      {...field}
                      value={field.value ?? ""}
                      readOnly={!!row?.id}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "") {
                          field.onChange(null, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        } else {
                          field.onChange(value, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        }
                      }}
                      spellCheck="false"
                    />
                  </FormControl>
                  <FormDescription>
                    The s3 path to export the results to. It should include the
                    storage protocol and prefix, e.g.{" "}
                    <code>s3://bucket-name/prefix/to/export</code>. Once the
                    automation is created, the export path cannot be changed.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CollapsibleSection
              title="Role creation instructions"
              defaultCollapsed={!!row?.id}
            >
              {watchedExportPath?.startsWith("s3://") &&
              orgId &&
              project?.projectId ? (
                <AwsInstructions
                  orgId={orgId}
                  projectId={project.projectId}
                  externalId={watchedExternalId}
                  exportPath={form.getValues("config.export_path")}
                />
              ) : (
                <p className="text-sm text-gray-500">
                  Enter an export path to view role configuration instructions.
                </p>
              )}
            </CollapsibleSection>

            {/* When there are multiple options, we can add a dropdown or infer this from the path */}
            <FormField
              control={form.control}
              name="config.credentials.role_arn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role ARN</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter role ARN"
                      {...field}
                      value={field.value ?? ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "") {
                          field.onChange(null, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        } else {
                          field.onChange(value, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        }
                      }}
                      spellCheck="false"
                    />
                  </FormControl>
                  <FormDescription>
                    A custom role that Braintrust will assume to export data.
                    This role will be assumed with a unique, auto-generated
                    external ID.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.format"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Format</FormLabel>
                  <FormControl>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button isDropdown className="w-full">
                          <span className="flex-1 truncate text-left">
                            {selectedFormat?.label ?? "Select format"}
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[462px]">
                        {formatOptions.map((o) => (
                          <DropdownMenuCheckboxItem
                            key={o.value}
                            checked={field.value === o.value}
                            onSelect={() => field.onChange(o.value)}
                          >
                            {o.label}
                          </DropdownMenuCheckboxItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </FormControl>
                  <FormDescription>The exported file format</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="config.interval_seconds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Interval</FormLabel>
                  <FormControl>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button isDropdown className="w-full">
                          <span className="flex-1 truncate text-left">
                            {selectedInterval?.label ?? "Select interval"}
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[462px]">
                        {intervalOptions.map((o) => (
                          <DropdownMenuCheckboxItem
                            key={o.value}
                            checked={field.value === o.value}
                            onSelect={() => field.onChange(o.value)}
                          >
                            {o.label}
                          </DropdownMenuCheckboxItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </FormControl>
                  <FormDescription>
                    Trigger the export once in this time interval
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CollapsibleSection title="Advanced" defaultCollapsed={true}>
              <FormField
                control={form.control}
                name="config.batch_size"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Batch size</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter batch size"
                        {...field}
                        value={field.value ?? ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === "") {
                            field.onChange(null, {
                              shouldDirty: true,
                              shouldValidate: true,
                            });
                          } else {
                            field.onChange(parseInt(value), {
                              shouldDirty: true,
                              shouldValidate: true,
                            });
                          }
                        }}
                        spellCheck="false"
                      />
                    </FormControl>
                    <FormDescription>
                      The number of items to export in each batch. Defaults to
                      1,000 if not set.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CollapsibleSection>
          </>
        )}

        {testResult?.kind === "error" && (
          <ErrorBanner skipErrorReporting>
            <div className="mb-1 font-medium">Test failed</div>
            {testResult.message}
          </ErrorBanner>
        )}
        {testResult?.kind === "success" && (
          <div className="mt-4 text-xs font-medium">
            <div
              className={cn(
                "px-2 py-1 rounded mb-1 inline-block bg-good-100 text-good-700",
              )}
            >
              Test successful
            </div>
            <DataTextEditor
              className="mt-2"
              value={testResult.payload}
              allowedRenderOptions={["json"]}
              readOnly
            />
          </div>
        )}

        {/* {form.formState.errors[""] && (
          <p className="text-xs font-medium text-bad-700">
            {form.formState.errors[""].message}
          </p>
        )} */}
        <DialogFooter className="mt-4">
          <Button
            variant="ghost"
            disabled={!form.formState.isValid}
            isLoading={isRunningTest}
            onClick={async (e) => {
              e.preventDefault();
              if (!form.formState.isValid) {
                return;
              }
              const data = form.getValues();

              setTestResult(null);
              setIsRunningTest(true);
              const result = await test({
                id: row?.id,
                name: data.name,
                description: data.description,
                config: data.config,
              });

              setTestResult(result);
              setIsRunningTest(false);
            }}
          >
            Test automation
          </Button>
          <Button
            variant="primary"
            type="submit"
            disabled={!form.formState.isDirty}
            isLoading={form.formState.isLoading || form.formState.isSubmitting}
          >
            {action} automation
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

const formatInterval = (interval: number) => {
  return formatDuration(
    intervalToDuration({ start: 0, end: interval * 1000 }),
    { format: ["days", "hours", "minutes"] },
  );
};

const formatEventType = (
  eventType: z.infer<typeof automationEventTypeEnum>,
) => {
  switch (eventType) {
    case "logs":
      return "Log event";
    case "btql_export":
      return "BTQL export";
    default:
      const _exhaustiveCheck: never = eventType;
      return _exhaustiveCheck;
  }
};

const formatActionType = (config: ProjectAutomation["config"]) => {
  switch (config.event_type) {
    case "logs":
      return "Webhook";
    case "btql_export":
      return "Export";
    default:
      const _exhaustiveCheck: never = config;
      throw new Error(`Unhandled event type: ${_exhaustiveCheck}`);
  }
};

const intervalOptions = [
  60 * 5,
  60 * 30,
  60 * 60,
  60 * 60 * 4,
  60 * 60 * 12,
  60 * 60 * 24,
].map((o) => ({
  label: formatInterval(o),
  value: o,
}));

const formatOptions = [
  { label: "JSON Lines", value: "jsonl" },
  { label: "Parquet", value: "parquet" },
];

const supportedProtocols = ["s3" /*, "gcs", "azure"*/];

function formatExportType(exportType: ExportDefinition["type"] | undefined) {
  const _exportType = exportType ?? "log_traces";
  switch (_exportType) {
    case "log_traces":
      return "Logs (traces)";
    case "log_spans":
      return "Logs (spans)";
    case "btql_query":
      return "Custom BTQL query";
    default:
      const _exhaustiveCheck: never = _exportType;
      throw new Error(`Unhandled export type: ${_exhaustiveCheck}`);
  }
}

export default Automations;

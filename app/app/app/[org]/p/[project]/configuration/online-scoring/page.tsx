"use client";
import {
  <PERSON>,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { But<PERSON> } from "#/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import { useContext, useMemo, useState } from "react";
import { PencilLine, Plus, Radio, Trash2 } from "lucide-react";
import { Input, inputClassName } from "#/ui/input";
import {
  type UpsertProjectScore,
  type UpsertResponse,
  performUpsert,
  performDelete,
} from "../configuration-client-actions";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Slider } from "#/ui/slider";
import { ScorersDropdownWithCreateDialog } from "#/app/app/[org]/prompt/[prompt]/scorers/scorers-dropdown";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "#/ui/form";
import { ConfigurationSectionLayout } from "../configuration-section-layout";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import ReactTextareaAutosize from "react-textarea-autosize";
import {
  onlineScoreConfigSchema,
  type ProjectScore,
} from "@braintrust/core/typespecs";
import { isOnlineScore } from "@braintrust/local/query";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { pluralizeWithCount } from "#/utils/plurals";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { Switch } from "#/ui/switch";
import { TableSkeleton } from "#/ui/table/table-skeleton";

const OnlineScoreConfig = () => {
  // null means we're creating a new rule
  const [selectedRule, setSelectedRule] = useState<
    ProjectScore | null | undefined
  >(undefined);

  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const {
    config,
    mutateConfig: mutate,
    projectId,
    isConfigLoading,
  } = useContext(ProjectContext);
  const { scores } = config;

  if (!projectId) {
    throw new Error("Cannot instantiate OnlineScoreConfig outside project");
  }

  const rules = useMemo(
    () =>
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      scores.filter((row) => isOnlineScore(row.score_type)) as ProjectScore[],
    [scores],
  );

  return (
    <ConfigurationSectionLayout
      title="Online scoring"
      description="Configure rules for continuous evaluation from real-time logs."
      action={
        rules.length > 0 ? (
          <Button
            size="sm"
            onClick={(_e) => {
              setSelectedRule(null);
            }}
            Icon={Plus}
          >
            Rule
          </Button>
        ) : undefined
      }
    >
      {isConfigLoading ? (
        <TableSkeleton />
      ) : rules.length > 0 ? (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow className="hover:bg-background">
              <TableHead className="w-48">Rule name</TableHead>
              <TableHead className="w-36">Sampling rate</TableHead>
              <TableHead className="w-36">Scorers</TableHead>
              <TableHead className="flex-1">Description</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rules.map((rule) => (
              <TableRow key={rule.id} className="py-2">
                <TableCell className="w-48 truncate">{rule.name}</TableCell>
                <TableCell className="w-36 truncate">
                  {((rate) => {
                    const percentage = (rate * 100).toFixed(2);
                    return percentage.endsWith(".00")
                      ? Math.round(rate * 100) + "%"
                      : percentage + "%";
                  })(rule.config?.online?.sampling_rate ?? 0)}
                </TableCell>
                <TableCell className="w-36 truncate">
                  {pluralizeWithCount(
                    rule.config?.online?.scorers.length ?? 0,
                    "scorer",
                    "scorers",
                  )}
                </TableCell>
                <TableCell className="flex-1 truncate">
                  {rule.description ?? ""}
                </TableCell>
                <TableCell className="w-28 justify-end gap-2 pr-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={(_e) => {
                      setSelectedRule(rule);
                    }}
                  >
                    <PencilLine className="size-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8"
                    onClick={async (_e) => {
                      if (!apiUrl) {
                        toast.error("Not logged in (yet)");
                        return;
                      }
                      return toast.promise(
                        performDelete({
                          apiUrl,
                          sessionToken: await getOrRefreshToken(),
                          objectType: "project_score",
                          rowId: rule.id,
                          mutate,
                        }),
                      );
                    }}
                  >
                    <Trash2 className="size-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <TableEmptyState
          label={
            <div className="flex flex-col items-center gap-4">
              <Radio className="text-primary-500" />
              <p className="text-base text-primary-500">
                No online scoring rules defined yet
              </p>
              <Button
                size="sm"
                onClick={(_e) => {
                  setSelectedRule(null);
                }}
                Icon={Plus}
              >
                Create rule
              </Button>
            </div>
          }
          labelClassName="text-sm"
        />
      )}
      <Dialog
        open={selectedRule !== undefined}
        onOpenChange={(o) => {
          if (!o) {
            setSelectedRule(undefined);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Configure online scoring rule</DialogTitle>
            <DialogDescription>
              Continuous evaluation from real-time logs
            </DialogDescription>
          </DialogHeader>
          <OnlineScoreConfigForm
            row={selectedRule ?? null}
            onClose={() => setSelectedRule(undefined)}
            save={async (row) => {
              if (!apiUrl) {
                return { kind: "error", message: "Not logged in (yet)" };
              }
              const sessionToken = await getOrRefreshToken();
              return await performUpsert({
                apiUrl,
                sessionToken,
                objectType: "project_score", // todo
                row,
                projectId,
                mutate,
              });
            }}
          />
        </DialogContent>
      </Dialog>
    </ConfigurationSectionLayout>
  );
};

const onlineScoreConfigFormSchema = z
  .object({
    name: z.string(),
    description: z.string().optional(),
  })
  .and(onlineScoreConfigSchema);

const getReset = (row: ProjectScore | null) => {
  if (!row) return undefined;
  return {
    name: row.name,
    description: row.description ?? undefined,
    sampling_rate: row.config?.online?.sampling_rate,
    scorers: row.config?.online?.scorers,
    apply_to_root_span: row.config?.online?.apply_to_root_span,
    apply_to_span_names: formatSpanNames(
      row.config?.online?.apply_to_span_names,
    ),
    skip_logging: row.config?.online?.skip_logging,
  };
};

const formatSpanNames = (names?: string[] | null) =>
  names?.flatMap((n) => n.split(",").map((n) => n.trim())).filter(Boolean);

function OnlineScoreConfigForm({
  row,
  save,
  onClose,
}: {
  row: ProjectScore | null;
  save: (row: UpsertProjectScore) => Promise<UpsertResponse>;
  onClose: VoidFunction;
}) {
  const { projectId, projectName } = useContext(ProjectContext);
  const [isCreatingScorer, setIsCreatingScorer] = useState(false);
  const action = row ? "Update" : "Create";

  const form = useForm<
    z.infer<typeof onlineScoreConfigFormSchema> & { "": string }
  >({
    resolver: zodResolver(onlineScoreConfigFormSchema),
    defaultValues: row
      ? getReset(row)
      : {
          sampling_rate: 0.1,
          scorers: [],
          apply_to_root_span: true,
          apply_to_span_names: [],
          skip_logging: false,
        },
  });

  const scorersCount = form.watch("scorers").length;
  const hasScorers = scorersCount > 0;

  const applyToRootSpan = form.watch("apply_to_root_span");
  const applyToSpanNames = form.watch("apply_to_span_names");

  const validSpans =
    applyToRootSpan || (applyToSpanNames && applyToSpanNames.length > 0);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(async (data) => {
          const { name, description, ...config } = data;
          const result = await save({
            id: row?.id,
            name,
            description,
            score_type: "online",
            config: {
              online: {
                ...config,
                apply_to_span_names: formatSpanNames(
                  config.apply_to_span_names,
                ),
              },
            },
          });
          if (result.kind === "duplicate") {
            form.setError(
              "name",
              {
                type: "value",
                message:
                  "Name already exists. Please choose a unique rule name.",
              },
              {
                shouldFocus: true,
              },
            );
            return;
          } else if (result.kind === "error") {
            toast.error(`Failed to create or update rule`, {
              description: result.message,
            });
            return;
          }
          onClose();
        })}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rule name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter online scoring rule name"
                  {...field}
                  value={field.value ?? ""}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/*<FormField
              control={form.control}
              name="filter"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>BTQL filter</FormLabel>
                  <FormControl>
                    <ReactTextareaAutosize
                      {...field}
                      className={cn(
                        inputClassName,
                        "font-mono placeholder:font-inter",
                      )}
                      rows={2}
                      placeholder="Enter BTQL filter"
                    />
                  </FormControl>
                  <FormDescription>
                    Filter logs using BTQL. If no filter is provided, the rule
                    will apply to all logs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />*/}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <div>
              <CollapsibleSection
                title="Description (optional)"
                defaultCollapsed={!row?.description}
              >
                <FormItem>
                  <FormControl>
                    <ReactTextareaAutosize
                      {...field}
                      className={inputClassName}
                      rows={2}
                      placeholder="Enter description"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </CollapsibleSection>
            </div>
          )}
        />
        <FormField
          control={form.control}
          name="scorers"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex justify-between">
                Scorers
                {hasScorers && (
                  <Button
                    size="inline"
                    transparent
                    className="text-xs text-primary-500"
                    onClick={(e) => {
                      e.preventDefault();
                      form.setValue("scorers", [], { shouldDirty: true });
                    }}
                  >
                    Clear
                  </Button>
                )}
              </FormLabel>
              <FormControl>
                {projectId && (
                  <ScorersDropdownWithCreateDialog
                    projectId={projectId}
                    projectName={projectName}
                    savedScorers={field.value}
                    updateScorers={async (scorers) => {
                      field.onChange(scorers);
                      return Promise.resolve(null);
                    }}
                    disableScorersThatRequireConfiguration
                    isCreatingScorer={isCreatingScorer}
                    setIsCreatingScorer={setIsCreatingScorer}
                    hideSelected={false}
                  />
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="sampling_rate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Sampling rate</FormLabel>
              <FormControl>
                <div className="flex max-w-xs items-center gap-2">
                  <div className="relative flex w-18 flex-none items-center">
                    <Input
                      ref={field.ref}
                      name={field.name}
                      onBlur={field.onBlur}
                      disabled={field.disabled}
                      value={Number((field.value * 100).toFixed(2))}
                      onChange={(e) => {
                        const value = Math.max(
                          0,
                          Math.min(100, Number(e.target.value)),
                        );
                        field.onChange(Number((value / 100).toFixed(4)));
                      }}
                      type="number"
                      placeholder="100"
                      className="h-7 w-full text-sm"
                      min={0}
                      max={100}
                      step="any"
                    />
                    <span className="pointer-events-none absolute right-2 text-xs text-primary-400">
                      %
                    </span>
                  </div>
                  <Slider
                    value={[Math.round(field.value * 100)]}
                    onValueChange={(v) => {
                      field.onChange(
                        Number((Math.round(v[0]) / 100).toFixed(2)),
                      );
                    }}
                    min={0}
                    max={100}
                    step={1}
                  />
                </div>
              </FormControl>
              <FormDescription>
                Percentage of filtered logs to score
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex flex-col gap-2">
          <div className="text-sm">Apply to spans</div>
          <FormField
            control={form.control}
            name="apply_to_root_span"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex h-8 items-center gap-2 text-sm">
                    <Switch
                      className="flex-none data-[state=checked]:bg-primary-700"
                      checked={!!field.value}
                      onCheckedChange={(value) =>
                        field.onChange(value, {
                          shouldDirty: true,
                        })
                      }
                    />
                    Root spans
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="apply_to_span_names"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="Enter span names (comma separated)"
                    {...field}
                    value={field.value?.join(", ") ?? ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === "") {
                        field.onChange(null, {
                          shouldDirty: true,
                          shouldValidate: true,
                        });
                      } else {
                        field.onChange(
                          value.split(",").map((n) => n.trim()),
                          {
                            shouldDirty: true,
                            shouldValidate: true,
                          },
                        );
                      }
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Enter the span names this rule should apply to, separated by
                  comma. Span names are case-sensitive.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        {hasScorers && validSpans && (
          <div className="rounded-md px-3 py-2 text-xs bg-accent-50 text-accent-700">
            {pluralizeWithCount(scorersCount, "scorer", "scorers")} will be
            added to {(form.watch("sampling_rate") * 100).toFixed(2)}% of new
            logs, and applied to{" "}
            {applyToRootSpan && applyToSpanNames && applyToSpanNames.length > 0
              ? "root spans and selected spans"
              : applyToRootSpan
                ? "root spans"
                : applyToSpanNames && applyToSpanNames.length > 0
                  ? "selected spans"
                  : ""}
          </div>
        )}
        <div>
          <CollapsibleSection
            title="Advanced"
            className="mb-2"
            defaultCollapsed
          >
            <FormField
              control={form.control}
              name="skip_logging"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="flex h-8 items-center gap-2 text-sm">
                      <Switch
                        checked={!!field.value}
                        onCheckedChange={(value) => field.onChange(value)}
                      />
                      Disable logging
                    </div>
                  </FormControl>
                  <FormDescription>
                    Disable logging for this rule. When disabled, logging will
                    be disabled on the target spans regardless of whether there
                    are other online scoring rules for those spans.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CollapsibleSection>
        </div>

        {form.formState.errors[""] && (
          <p className="text-xs font-medium text-bad-700">
            {form.formState.errors[""].message}
          </p>
        )}
        <DialogFooter className="mt-4">
          <Button
            variant="primary"
            type="submit"
            disabled={!form.formState.isDirty || !validSpans}
            isLoading={form.formState.isLoading || form.formState.isSubmitting}
          >
            {action} rule
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}

export default OnlineScoreConfig;

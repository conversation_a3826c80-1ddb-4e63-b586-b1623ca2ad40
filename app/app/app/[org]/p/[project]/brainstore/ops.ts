import { type LoadedBtSessionToken } from "#/utils/auth/session-token";
import { apiPost } from "#/utils/btapi/fetch";
import { type BackfillableObjectType } from "@braintrust/local/app-schema";

export async function runEnableBackfilling({
  apiUrl,
  projectId,
  objectTypes,
  sessionToken,
}: {
  apiUrl: string;
  projectId: string | string[];
  objectTypes: BackfillableObjectType[] | undefined;
  sessionToken: LoadedBtSessionToken;
}) {
  try {
    await apiPost({
      url: `${apiUrl}/brainstore/backfill/enable`,
      sessionToken,
      payload: {
        project_id: projectId,
        object_types: objectTypes,
      },
      alreadySerialized: false,
    });
  } catch (e) {
    throw e;
  }
}

export async function runDeleteBackfilling({
  apiUrl,
  projectId,
  sessionToken,
}: {
  apiUrl: string;
  projectId: string;
  sessionToken: LoadedBtSessionToken;
}) {
  try {
    await apiPost({
      url: `${apiUrl}/brainstore/backfill/delete`,
      sessionToken,
      payload: {
        project_id: projectId,
        should_reset_historical_backfill: true,
      },
      alreadySerialized: false,
    });
  } catch (e) {
    throw e;
  }
}

import { type ModelSpec } from "@braintrust/proxy/schema";
import { useCallback } from "react";
import ModelParameters from "./model-parameters";
import { type PromptData } from "#/ui/prompts/schema";
import {
  type FunctionObjectType,
  type ModelParams,
} from "@braintrust/core/typespecs";
import { ModelDropdown } from "./ModelDropdown";
import { type ModelDetails } from "#/ui/prompts/models";
import { PromptParser } from "../../p/[project]/prompts/PromptParser";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useSyncedPrompts } from "../../p/[project]/prompts/synced/use-synced-prompts";
import PromptBlockSynced, {
  type AgentPosition,
} from "../../p/[project]/prompts/synced/prompt-block-synced";
import { type TextEditorProps } from "#/ui/text-editor";

interface PromptEditorSyncedProps {
  orgName: string;
  promptId: string;
  modelOptionsByProvider: Record<string, ModelDetails[]>;
  extensions?: TextEditorProps["extensions"];
  allAvailableModels: { [name: string]: ModelSpec };
  onRun: VoidFunction;
  promptData?: PromptData;
  disableTools?: boolean;
  copilotContext?: CopilotContextBuilder;
  isInPlayground?: boolean;
  isReadOnly?: boolean;
  agentPosition?: AgentPosition;
  type?: FunctionObjectType;
}

export const PromptEditorSynced = ({
  orgName,
  promptId,
  agentPosition,
  modelOptionsByProvider,
  extensions,
  allAvailableModels,
  onRun,
  promptData,
  disableTools,
  copilotContext,
  isInPlayground,
  isReadOnly,
  type,
}: PromptEditorSyncedProps) => {
  const currentModel = promptData?.options?.model;
  const modelSpec = currentModel ? allAvailableModels[currentModel] : undefined;

  const { updateModel, updateModelParams, updateParser, saveSyncedPrompt } =
    useSyncedPrompts();

  const onChangeModel = useCallback(
    (model?: string) => {
      if (model) {
        updateModel({ id: promptId, model });
      }
    },
    [promptId, updateModel],
  );

  const handleSavePrompt = useCallback(
    async () => await saveSyncedPrompt(promptId),
    [promptId, saveSyncedPrompt],
  );

  const onModelParamsChange = useCallback(
    (params: ModelParams) => {
      updateModelParams({ id: promptId, params });
    },
    [promptId, updateModelParams],
  );

  return (
    <div className="flex flex-col">
      <div className="flex flex-none items-center pb-2">
        <div className="flex w-full items-center">
          <ModelDropdown
            orgName={orgName}
            currentModel={currentModel}
            onChange={onChangeModel}
            modelOptionsByProvider={modelOptionsByProvider}
            currentModelNotFound={
              !!currentModel && !allAvailableModels[currentModel]
            }
            isReadOnly={isReadOnly}
          />
          {modelSpec && (
            <ModelParameters
              modelSpec={modelSpec}
              params={promptData?.options?.params}
              saveParams={onModelParamsChange}
              isReadOnly={isReadOnly}
            />
          )}
        </div>
      </div>
      {currentModel && allAvailableModels[currentModel] && (
        <div className="group/promptblock flex-auto overflow-auto">
          <PromptBlockSynced
            key={promptId}
            idx={0}
            model={currentModel}
            allAvailableModels={allAvailableModels}
            triggerSave={handleSavePrompt}
            isInPlayground={isInPlayground}
            runPrompts={onRun}
            data={promptData}
            extensions={extensions}
            disableTools={disableTools}
            copilotContext={copilotContext}
            isReadOnly={isReadOnly}
            agentPosition={agentPosition}
            promptId={promptId}
            datasetId={undefined}
            type={type}
          />
        </div>
      )}

      {promptData?.parser && (
        <PromptParser
          isReadOnly={isReadOnly}
          parser={promptData?.parser}
          saveParser={(parser: PromptData["parser"]) =>
            updateParser({ id: promptId, parser })
          }
        />
      )}
    </div>
  );
};

import { useCallback, useState } from "react";
import { OneLineTextPrompt } from "#/ui/dialogs/one-line-text-prompt";
import {
  createPromptSession,
  type InitialPromptArgs,
} from "./createPromptSession";
import { toast } from "sonner";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";

export interface MakeNewPromptSessionDialogArgs {
  orgName: string;
  projectName: string;
  refreshPromptSessions: () => void;
  initialPromptArgs?: InitialPromptArgs;
  onSuccess?: (promptSession: { id: string; name: string }) => void;
}

export function useCreatePlaygroundDialog() {
  // If set to |undefined|, we don't show the new prompt dialog.
  const [newPromptSessionDefaultValue, setNewPromptSessionDefaultValue] =
    useState<string | undefined>(undefined);

  const { firstAvailableModel, params, prompt } = useDefaultPromptData();

  const makeNewPromptSessionDialog = useCallback(
    ({
      orgName,
      projectName,
      refreshPromptSessions,
      initialPromptArgs,
      onSuccess,
    }: MakeNewPromptSessionDialogArgs) => {
      if (!newPromptSessionDefaultValue) {
        return undefined;
      }
      return (
        <OneLineTextPrompt
          title="Create playground"
          fieldName="Name"
          onSubmit={async (name: string) => {
            const resp = await createPromptSession({
              orgName,
              projectName,
              sessionName: name,
              initialPromptArgs,
              initialModel: firstAvailableModel,
              initialParams: params,
              initialPrompt: prompt,
            });
            setNewPromptSessionDefaultValue(undefined);
            const promptSession = {
              id: resp?.data?.id,
              name: resp?.data?.name,
            };
            if (!promptSession.id || !promptSession.name || resp?.error) {
              toast.error(
                `Failed to create playground ${JSON.stringify(name)}`,
                {
                  description: JSON.stringify(resp?.error, null, 2),
                },
              );
            } else {
              refreshPromptSessions?.();
              onSuccess?.(promptSession);
            }
          }}
          onOpenChange={() => setNewPromptSessionDefaultValue(undefined)}
          open={newPromptSessionDefaultValue !== undefined}
          defaultValue={newPromptSessionDefaultValue}
          submitLabel="Create"
        />
      );
    },
    [newPromptSessionDefaultValue, firstAvailableModel, params, prompt],
  );
  return { setNewPromptSessionDefaultValue, makeNewPromptSessionDialog };
}

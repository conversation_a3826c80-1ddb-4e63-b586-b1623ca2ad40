"use server";

import { z } from "zod";

import { type AuthLookup, HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import {
  getExperimentLink,
  getExperimentsLink,
} from "../p/[project]/experiments/[experiment]/getExperimentLink";
import { getDatasetLink } from "../p/[project]/datasets/[dataset]/getDatasetLink";
import { getProjectLogsLink } from "../p/[project]/logs/getProjectLogsLink";
import { aclType, type ObjectLookupSupportedType } from "@braintrust/local";
import { getPlaygroundLink } from "../prompt/[prompt]/getPromptLink";
import { type ObjectRedirectLinkParams } from "#/utils/object-link";

async function getNames(
  authLookup: AuthLookup,
  object_id: string,
  object_type: ObjectLookupSupportedType,
) {
  const { query, queryParams, notFoundErrorMessage } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: aclType(object_type),
      aclPermission: "read",
    },
    filters: {
      id: [object_id],
    },
    fullResultSetAdditionalProjections: ["projects.name AS project_name"],
  });

  const supabase = getServiceRoleSupabase();
  const wrappedQuery = `
    SELECT
      project_name,
      name AS object_name
    FROM (${query}) AS object`;
  const { rows } = await supabase.query(wrappedQuery, queryParams.params);

  const row = extractSingularRow({ rows, notFoundErrorMessage });
  const rowParsed = z
    .object({
      project_name: z.string(),
      object_name: z.string(),
    })
    .parse(row);
  return {
    projectName: rowParsed.project_name,
    objectName: rowParsed.object_name,
  };
}

export async function getObjectRedirectLink({
  orgName,
  object_type,
  object_id,
  id,
}: ObjectRedirectLinkParams): Promise<string> {
  const authLookup = await getServerSessionAuthLookup();

  const { projectName, objectName } = await getNames(
    authLookup,
    object_id,
    object_type,
  );

  let basePath = "";
  switch (object_type) {
    case "experiment":
      basePath = getExperimentLink({
        orgName,
        projectName,
        experimentName: objectName,
      });
      break;
    case "dataset":
      basePath = getDatasetLink({
        orgName,
        projectName,
        datasetName: objectName,
      });
      break;
    case "project_logs":
      basePath = getProjectLogsLink({ orgName, projectName });
      break;
    case "prompt_session":
      basePath = getPlaygroundLink({
        orgName,
        projectName,
        playgroundName: objectName,
      });
      break;
    default:
      const x: never = object_type;
      throw new HTTPError(
        500,
        `Unsupported object type ${x} for reference lookup.`,
      );
  }

  const queryParams = id && new URLSearchParams({ oid: id }).toString();
  return `${basePath}${queryParams ? `?${queryParams}` : ""}`;
}

export async function getExperimentsPageRedirectLink({
  orgName,
  projectId,
}: {
  orgName: string;
  projectId: string;
}) {
  const authLookup = await getServerSessionAuthLookup();
  const { projectName } = await getNames(authLookup, projectId, "project_logs");
  return getExperimentsLink({ orgName, projectName });
}

import { createSearchAPI } from "fumadocs-core/search/server";
import { getPages as getDocsPages } from "#/app/docs/source";
import { getPages as getBlogPages } from "#/app/(landing)/blog/source";

export const { GET } = createSearchAPI("advanced", {
  indexes: [
    ...getDocsPages().map((page) => ({
      id: page.url,
      url: page.url,
      title: page.data.title,
      description: page.data.description,
      structuredData: page.data.structuredData,
    })),
    ...getBlogPages()
      .filter(page => !page.data.draft)
      .map((page) => ({
        id: page.url,
        url: page.url,
        title: page.data.title,
        description: page.data.description,
        structuredData: page.data.structuredData,
      })),
  ],
});

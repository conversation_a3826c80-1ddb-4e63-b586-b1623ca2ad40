import { Expr } from "@braintrust/btql/parser";
import { isEmpty, mapAt, objectNullish } from "@braintrust/core";
import {
  DEFAULT_IF_EXISTS,
  IfExists,
  extendedSavedFunctionIdSchema,
  ifExistsEnum,
} from "@braintrust/core/typespecs";
import { z } from "zod";
import { runBtql } from "./btql";
import { CustomFetchResponse, customFetchRequest } from "./custom_fetch";
import { LogToPublisherFn } from "./event_publisher";
import {
  constructPrefixSetAndCacheKeys,
  promptCacheKeyPrefix,
} from "./prompt_cache_invalidation";
import { getRedis } from "./redis";
import { runLogData } from "./run_log_data";
import {
  BadRequestError,
  InternalServerError,
  normalizeXact,
  postDefaultHeaders,
  wrapZodError,
} from "./util";

export type PromptFunctionObjectType = "project_prompts" | "project_functions";

export type InsertFunctionsInput = {
  rows: { functions: unknown[] };
  appOrigin: string;
  token: string | undefined;
  logToPublisher: LogToPublisherFn;
};

export type InsertFunctionsOutput =
  | { status: "error"; resp: CustomFetchResponse | string }
  | { status: "success" };

const projectIdSlugSchema = z.object({
  project_id: z.string(),
  slug: z.string(),
});

export async function runInsertFunctions({
  rows: rowsU,
  appOrigin,
  token,
  logToPublisher,
}: InsertFunctionsInput): Promise<InsertFunctionsOutput> {
  const rows = wrapZodError(() =>
    z.object({ functions: z.record(z.unknown()).array() }).parse(rowsU),
  ).functions;

  // Grab control fields.
  const rowControlFields = wrapZodError(() =>
    projectIdSlugSchema.array().parse(rows),
  );

  // Extract the if_exists field
  const ifExists: IfExists[] = [];
  for (const row of rows) {
    ifExists.push(
      ifExistsEnum.nullish().parse(row.if_exists) ?? DEFAULT_IF_EXISTS,
    );
    delete row.if_exists;
  }

  const resp = await customFetchRequest(`${appOrigin}/api/prompt/register`, {
    method: "POST",
    headers: postDefaultHeaders({ token }),
    body: JSON.stringify(rowControlFields),
  });
  if (!resp.ok) {
    return { status: "error", resp };
  }

  const registerResult = z
    .object({ prompt: z.record(z.unknown()), found_existing: z.boolean() })
    .array()
    .parse(await resp.body.json());

  if (registerResult.length !== rows.length) {
    throw new InternalServerError("registerResult has wrong length");
  }

  const failedExisting = [];
  for (let i = 0; i < registerResult.length; i++) {
    const result = registerResult[i];
    if (result.found_existing && ifExists[i] === "error") {
      failedExisting.push(rowControlFields[i].slug);
    }
  }
  if (failedExisting.length > 0) {
    return {
      status: "error",
      resp: `Slugs already exist: ${failedExisting.join(", ")}`,
    };
  }

  const projectIdSlugToRegisterResult = new Map<
    string,
    (typeof registerResult)[0]
  >(
    registerResult.map((x) => {
      const { project_id, slug } = projectIdSlugSchema.parse(x.prompt);
      return [JSON.stringify([project_id, slug]), x];
    }),
  );
  const logData = rows
    .filter(
      (_, idx) =>
        !registerResult[idx].found_existing || ifExists[idx] === "replace",
    )
    .map((row, idx): Record<string, unknown> => {
      const { project_id, slug } = rowControlFields[idx];
      const metadata = mapAt(
        projectIdSlugToRegisterResult,
        JSON.stringify([project_id, slug]),
      );

      const rowToolFunctions = z
        .object({
          prompt_data: z
            .object({
              tool_functions: z.array(extendedSavedFunctionIdSchema).nullish(),
            })
            .nullish(),
        })
        .safeParse(row);
      if (
        rowToolFunctions.success &&
        rowToolFunctions.data.prompt_data?.tool_functions
      ) {
        // There may be "dangling" references to tool functions that we should resolve to
        // function ids.
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- We verified this via the zod check above
        (row as any).prompt_data.tool_functions =
          rowToolFunctions.data.prompt_data.tool_functions.map((x) => {
            if (x.type === "slug") {
              const metadata = projectIdSlugToRegisterResult.get(
                JSON.stringify([x.project_id, x.slug]),
              );
              if (!metadata) {
                throw new BadRequestError(
                  `Unresolved slug reference ${x.project_id}:${x.slug}`,
                );
              }
              return {
                type: "function",
                id: metadata.prompt.id,
              };
            } else {
              return x;
            }
          });
      }

      return {
        log_id: "p" as const,
        ...stripPromptMetadata(metadata.prompt),
        ...row,
      };
    });

  await runLogData({ rows: logData, appOrigin, token, logToPublisher });
  return { status: "success" };
}

const retrievePromptsParamsSchemaBase = objectNullish(
  z.object({
    project_id: z.string(),
    project_name: z.string(),
    slug: z.string(),
    version: z.string().transform((x) => normalizeXact(x)),
    // Pagination params, forwarded to the app.
    limit: z.unknown(),
    starting_after: z.unknown(),
    ending_before: z.unknown(),
  }),
);

const retrievePromptsParamsSchema = retrievePromptsParamsSchemaBase.merge(
  objectNullish(
    z.object({
      prompt_id: z.string(),
      prompt_name: z.string(),
    }),
  ),
);

const retrieveFunctionsParamsSchema = retrievePromptsParamsSchemaBase.merge(
  objectNullish(
    z.object({
      function_id: z.string(),
      function_name: z.string(),
    }),
  ),
);

const getPromptsExtraParamsSchema = z.object({
  org_name: z.unknown(),
  org_id: z.unknown(),
  ids: z.unknown(),
});

export type RetrievePromptsInput = {
  params: unknown;
  objectType: PromptFunctionObjectType;
  appOrigin: string;
  token: string | undefined;
  wasCachedToken?: string | undefined;
};

export type RetrievePromptsOutput =
  | { status: "success"; rows: Record<string, unknown>[] }
  | { status: "error"; resp: CustomFetchResponse };

const promptMetadataSchema = z.object({
  project_id: z.string(),
  id: z.string(),
});
type PromptMetadata = z.infer<typeof promptMetadataSchema>;

export async function retrievePrompts({
  params: paramsU,
  objectType,
  appOrigin,
  token,
  wasCachedToken,
}: RetrievePromptsInput): Promise<RetrievePromptsOutput> {
  const params = wrapZodError(() => {
    if (objectType === "project_prompts") {
      const { prompt_name, ...rest } =
        retrievePromptsParamsSchema.parse(paramsU);
      return { name: prompt_name, ...rest };
    } else if (objectType === "project_functions") {
      const { function_id, function_name, ...rest } =
        retrieveFunctionsParamsSchema.parse(paramsU);
      return { prompt_id: function_id, name: function_name, ...rest };
    } else {
      const x: never = objectType;
      throw new Error(`Unexpected objectType: ${x}`);
    }
  });

  const redisClient = await getRedis();

  const { cacheKeys, promptStr } = await (async () => {
    const prefix = ((): string | undefined => {
      if (params.prompt_id) {
        return promptCacheKeyPrefix({ promptId: params.prompt_id });
      } else if (params.project_id && params.slug) {
        return promptCacheKeyPrefix({
          projectId: params.project_id,
          slug: params.slug,
        });
      } else if (params.project_name && params.slug) {
        return promptCacheKeyPrefix({
          projectName: params.project_name,
          slug: params.slug,
        });
      } else {
        return undefined;
      }
    })();
    if (!prefix) {
      return { cacheKeys: undefined, promptStr: undefined };
    }
    const cacheKeys = constructPrefixSetAndCacheKeys({
      prefix,
      version: params.version ?? undefined,
      token,
    });
    const promptStr = (await redisClient.get(cacheKeys.cacheKey)) ?? undefined;
    return { cacheKeys, promptStr };
  })();

  if (!isEmpty(promptStr)) {
    if (wasCachedToken) {
      await redisClient.set(wasCachedToken, "true", { EX: 3600 });
    }
    return { status: "success", rows: JSON.parse(promptStr) };
  }

  let promptMetadatas: PromptMetadata[] | undefined = undefined;
  if (params.prompt_id) {
    const resp = await customFetchRequest(`${appOrigin}/api/prompt/get`, {
      method: "POST",
      headers: postDefaultHeaders({ token }),
      body: JSON.stringify({ ids: [params.prompt_id] }),
    });
    if (!resp.ok) {
      return { status: "error", resp };
    }
    promptMetadatas = promptMetadataSchema
      .array()
      .parse(await resp.body.json());
  } else {
    const { org_name, org_id, ids } =
      getPromptsExtraParamsSchema.parse(paramsU);
    const {
      slug,
      project_name,
      project_id,
      limit,
      starting_after,
      ending_before,
    } = params;
    const resp = await customFetchRequest(`${appOrigin}/api/prompt/get`, {
      method: "POST",
      headers: postDefaultHeaders({ token }),
      body: JSON.stringify({
        slug,
        project_name,
        project_id,
        org_name,
        org_id,
        ids,
        limit,
        starting_after,
        ending_before,
      }),
    });
    if (!resp.ok) {
      return { status: "error", resp };
    }
    promptMetadatas = promptMetadataSchema
      .array()
      .parse(await resp.body.json());
  }

  if (promptMetadatas === undefined) {
    throw new Error("Impossible");
  }
  if (promptMetadatas.length === 0) {
    // The caller can handle this
    return { status: "success", rows: [] };
  }

  // Next, we can look up the prompt as an "object".
  const promptProjectIds = [
    ...new Set(promptMetadatas.map((x) => x.project_id)),
  ];
  const promptIdFilterExprs = promptMetadatas.map(
    (x): Expr => ({
      op: "and",
      left: {
        op: "eq",
        left: { op: "ident", name: ["project_id"] },
        right: { op: "literal", value: x.project_id },
      },
      right: {
        op: "eq",
        left: { op: "ident", name: ["id"] },
        right: { op: "literal", value: x.id },
      },
    }),
  );
  const promptIdFilterDisjunction = promptIdFilterExprs.reduce(
    (acc, x): Expr => ({
      op: "or",
      left: acc,
      right: x,
    }),
  );

  const promptFilters: Expr[] = [promptIdFilterDisjunction];
  if (params.name) {
    promptFilters.push({
      op: "eq",
      left: { op: "ident", name: ["name"] },
      right: { op: "literal", value: params.name },
    });
  }

  const promptFilter: Expr = promptFilters.reduce(
    (acc, x): Expr => ({
      op: "and",
      left: acc,
      right: x,
    }),
  );

  const btqlResult = await runBtql({
    body: {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: [objectType],
          },
          // TODO: There is a bug in BTQL when searching for prompts across
          // projects: the 'log_id=p' value does not get applied to each
          // project, so the filter only ends up looking in the first project.
          args: promptProjectIds.map((x) => ({
            op: "literal" as const,
            value: x,
          })),
        },
        select: [{ op: "star" }],
        filter: promptFilter,
      },
      version: params.version ?? undefined,
    },
    appOrigin,
    ctxToken: token,
    skipAclCheck: true,
  });
  if ("explain" in btqlResult) {
    throw new InternalServerError("Unexpected explain result");
  }
  const promptRows = btqlResult.rows;

  // Dynamically backfill missing function_data
  for (const row of promptRows) {
    if (typeof row !== "object" || row === null) {
      continue;
    }
    if ("function_data" in row && row.function_data) {
      continue;
    }

    row.function_data = { type: "prompt" };
  }

  if (!isEmpty(cacheKeys) && promptRows.length === 1) {
    await Promise.all([
      redisClient.set(cacheKeys.cacheKey, JSON.stringify(promptRows), {
        EX: 3600,
      }),
      ...(cacheKeys.prefixSetKey
        ? [
            // Also store this cache key in the set of keys with this prefix, so that
            // we can quickly retrieve the set of cache keys which match particular
            // prefixes for invalidation.
            redisClient.sAdd(cacheKeys.prefixSetKey, cacheKeys.cacheKey),
            redisClient.expire(cacheKeys.prefixSetKey, 3600),
          ]
        : []),
    ]);
  }

  // Retain the original ordering of rows from promptMetadatas, since they may
  // be sorted by recency.
  const promptKeyToRow = new Map(
    promptRows.map((x) => {
      const { project_id, id } = x;
      return [JSON.stringify([project_id, id]), x];
    }),
  );
  const orderedPrompts = promptMetadatas.reduce((acc, x) => {
    const key = JSON.stringify([x.project_id, x.id]);
    const row = promptKeyToRow.get(key);
    if (row) {
      return [...acc, row];
    } else {
      return acc;
    }
  }, new Array<Record<string, unknown>>());

  return { status: "success", rows: orderedPrompts };
}

export function sanitizeInsertedPromptRows(
  rows: Record<string, unknown>[],
): Record<string, unknown>[] {
  return rows.map((row) => {
    const { span_id: _0, root_span_id: _1, ...rest } = row;
    return rest;
  });
}

export function stripPromptMetadata<
  T extends { created?: unknown; user_id?: unknown; deleted_at?: unknown },
>(x: T): Omit<T, "created" | "user_id" | "deleted_at"> {
  const { created: _0, user_id: _1, deleted_at: _2, ...rest } = x;
  return rest;
}

import {
  DEFAULT_FUNCTION_CACHE_TTL,
  InvocableLambdaFunction,
  InvokeResponse,
} from "@braintrust/local/functions";
import { RuntimeContext } from "@braintrust/core/typespecs/dist";
import { InternalServerError } from "../util";
import {
  lambdaVersionedArn,
  nextSleepTime,
  RunnableLambda,
  waitForLambdaFunctionToBeReady,
} from "./pool";
import { getStsClient } from "../lambda";
import {
  CODE_FUNCTION_EXECUTION_TIMEOUT_S,
  QUARANTINE_INVOKE_ROLE,
  QUARANTINE_REGION,
} from "../env";
import {
  InvokeWithResponseStreamResponse,
  Lambda,
} from "@aws-sdk/client-lambda";

import { finishCodeEventStream } from "../proxy/invoke-code";
import { getLogger } from "../instrumentation/logger";

const LAMBDA_RETRIES = 100;

export async function useInvocableLambdaRuntime({
  runtime,
  lambda,
}: {
  runtime: RuntimeContext;
  lambda: RunnableLambda;
}): Promise<InvocableLambdaFunction> {
  const sts = await getStsClient();
  if (!QUARANTINE_INVOKE_ROLE) {
    throw new InternalServerError("No quarantine invoke role configured");
  }
  const functionArn = lambdaVersionedArn(lambda);

  const roleResponse = await retryLambdaOp({
    functionArn,
    op: () =>
      sts.assumeRole({
        RoleArn: QUARANTINE_INVOKE_ROLE,
        RoleSessionName: "QuarantineInvokeRoleSession",
        DurationSeconds: DEFAULT_FUNCTION_CACHE_TTL + 60,
        Policy: JSON.stringify({
          Version: "2012-10-17",
          Statement: [
            {
              Effect: "Allow",
              Action: "lambda:InvokeFunction",
              Resource: functionArn,
            },
          ],
        }),
      }),
    name: "assume role",
  });
  if (!roleResponse.Credentials) {
    throw new InternalServerError("Failed to assume role");
  }
  const credentials = roleResponse.Credentials;
  if (
    !credentials.AccessKeyId ||
    !credentials.SecretAccessKey ||
    !credentials.SessionToken
  ) {
    throw new InternalServerError("Failed to assume role (no credentials)");
  }
  if (!credentials.Expiration) {
    throw new InternalServerError("Failed to assume role (no expiration)");
  }

  return {
    type: "lambda",
    credentials: {
      AccessKeyId: credentials.AccessKeyId,
      SecretAccessKey: credentials.SecretAccessKey,
      SessionToken: credentials.SessionToken,
    },
    expiresAt: credentials.Expiration.getTime() / 1000,
    functionArn,
    responseStream: runtime.runtime === "node",
  };
}

const LAMBDA_INVOKE_TIMEOUT_MS = CODE_FUNCTION_EXECUTION_TIMEOUT_S * 1000;

export async function invokeCodeLambda({
  func,
  payload,
  timeoutMs,
  setStatusCode,
  writable,
  onFinal,
}: {
  func: InvocableLambdaFunction;
  payload: unknown;
  timeoutMs?: number;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  writable: WritableStream<Uint8Array>;
  onFinal?: (result: InvokeResponse) => void;
}) {
  const pino = getLogger().child({
    task: "invokeCodeLambda",
  });
  timeoutMs = timeoutMs ?? LAMBDA_INVOKE_TIMEOUT_MS;

  const invokeLambda = new Lambda({
    credentials: {
      accessKeyId: func.credentials.AccessKeyId,
      secretAccessKey: func.credentials.SecretAccessKey,
      sessionToken: func.credentials.SessionToken,
    },
    region: QUARANTINE_REGION,
  });

  let response: InvokeWithResponseStreamResponse | undefined;
  let timeoutId: NodeJS.Timeout | undefined = undefined;
  try {
    await Promise.race([
      (async () => {
        response = await retryLambdaOp({
          functionArn: func.functionArn,
          op: () =>
            invokeLambda.invokeWithResponseStream({
              FunctionName: func.functionArn,
              Payload: JSON.stringify(payload),
            }),
          name: "invoke function",
        });
      })(),
      new Promise<void>((resolve, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error(`Function timed out after ${timeoutMs}ms`));
        }, timeoutMs);
      }),
    ]);
  } finally {
    clearTimeout(timeoutId);
  }

  const eventStream = response?.EventStream;
  if (!eventStream) {
    throw new InternalServerError("No event stream");
  }

  const chunkIterator = (async function* () {
    try {
      const decoder = new TextDecoder();
      const buffer = [];
      for await (const event of eventStream) {
        if (event.PayloadChunk) {
          const payload = decoder.decode(event.PayloadChunk.Payload);
          if (func.responseStream) {
            yield payload;
          } else {
            // Lambda will still return this payload in chunks, so we can't be sure that it's a whole event. Since we also have to
            // JSON deserialize it, concatenate pieces into a buffer and deserialize it all at once at the end.
            buffer.push(payload);
          }
        } else if (event.InvokeComplete && event.InvokeComplete.ErrorCode) {
          throw new Error(
            `${event.InvokeComplete.ErrorCode}: ${event.InvokeComplete.ErrorDetails}`,
          );
        }
      }

      if (!func.responseStream) {
        try {
          yield JSON.parse(buffer.join(""));
        } catch (e) {
          pino.warn(
            { error: e },
            "Failed to process non-streaming lambda payload",
          );
          throw e;
        }
      }
    } finally {
      // See the docs for `.destroy()`:
      // > Destroy underlying resources, like sockets. It's usually not necessary to do this.
      // > However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
      // > Otherwise, sockets might stay open for quite a long time before the server terminates them.
      invokeLambda.destroy();
    }
  })();

  return await finishCodeEventStream({
    chunkIterator,
    setStatusCode,
    writable,
    onFinal,
    pino,
  });
}

export async function retryLambdaOp<T>({
  functionArn,
  op,
  name,
}: {
  functionArn: string;
  op: () => Promise<T>;
  name: string;
}): Promise<T> {
  const pino = getLogger().child({
    task: "retryLambdaOp",
  });
  let delayMs = 100;
  const start = Date.now();
  for (let i = 0; i < LAMBDA_RETRIES; i++) {
    try {
      return await op();
    } catch (e) {
      if (i === LAMBDA_RETRIES - 1) {
        throw e;
      }
      pino.warn(
        { error: e, attempt: i + 1, totalAttempts: LAMBDA_RETRIES, name },
        `Failed to ${name}. Trying again...`,
      );
      for (let j = 0; j < LAMBDA_RETRIES; j++) {
        try {
          await waitForLambdaFunctionToBeReady(functionArn);
          break;
        } catch (e) {
          if (j === LAMBDA_RETRIES - 1) {
            throw e;
          }
          pino.warn(
            { error: e, attempt: j + 1, totalAttempts: LAMBDA_RETRIES },
            "Failed to waitForLambdaFunctionToBeReady. Trying again...",
          );
          await new Promise((resolve) => setTimeout(resolve, delayMs));
          delayMs = nextSleepTime(delayMs, Date.now() - start);
        }
      }

      if (Date.now() - start < delayMs) {
        // If the lambda function is ready, but we didn't sleep, then sleep
        await new Promise((resolve) => setTimeout(resolve, delayMs));
        delayMs = nextSleepTime(delayMs, Date.now() - start);
      }
    }
  }
  // This should never happen.
  throw new InternalServerError(
    `Failed to ${name} after ${LAMBDA_RETRIES} retries`,
  );
}

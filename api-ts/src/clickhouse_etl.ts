import { ident, join, sql } from "@braintrust/btql/planner";
import {
  CLICKHOUSE_LOGS_TABLE,
  CLICKHOUSE_COMMENTS_TABLE,
  CLICKHOUSE_CONNECT_URL,
  CLICKHOUSE_PG_URL,
  CLICKHOUSE_ETL_BATCH_SIZE,
  CLICKHOUSE_ETL_PERIOD_S,
  PG_LOGS_TABLE,
  PG_COMMENTS_TABLE,
  PG_URL,
} from "./env";
import { getPG, LOG_PG_TRANSACTION_QUERY_IDENTIFIER } from "./db/pg";
import { getClickhouse, getClickhousePG } from "./db/clickhouse";
import { z } from "zod";
import { Pool, PoolClient } from "pg";
import { advisoryLockInfo, AdvisoryLocks } from "./advisory_locks";
import { blockBackfillObjects } from "./block_backfill_objects";
import { getLogger } from "./instrumentation/logger";

const CLICKHOUSE_MAX_BATCH_SIZE = 1000000;

const PG_REMOTE_LOGS_TABLE = "pg_" + PG_LOGS_TABLE;
const PG_REMOTE_COMMENTS_TABLE = "pg_" + PG_COMMENTS_TABLE;
export const ETL_BATCH_SIZE: number = (() => {
  const ret = Number(CLICKHOUSE_ETL_BATCH_SIZE);
  if (isNaN(ret)) {
    throw new Error(
      `Invalid CLICKHOUSE_ETL_BATCH_SIZE: ${CLICKHOUSE_ETL_BATCH_SIZE}`,
    );
  }
  return ret;
})();
export const ETL_PERIOD_S: number = (() => {
  const ret = Number(CLICKHOUSE_ETL_PERIOD_S);
  if (isNaN(ret)) {
    throw new Error(
      `Invalid CLICKHOUSE_ETL_PERIOD_S: ${CLICKHOUSE_ETL_PERIOD_S}`,
    );
  }
  return ret;
})();

export type RunEtlLoopInput = {
  timeoutS: number;
  batchSize: number;
};

export type EtlLoopBatch = {
  maxSequenceId: number;
  elapsedTimeS: number;
};

export type RunEtlLoopOutput = {
  locked?: boolean;
  tableResults?: Record<string, EtlLoopBatch[] | null>;
};

// To stress test this loop, the best way is to:
// 1. Run a log statement
// 2. Stop clickhouse (e.g. docker stop services-clickhouse-1)
// 3. Run a few more log statements
// 4. Check that clickhouse falls behind:
//       curl -H "Authorization: Bearer $BRAINTRUST_API_KEY" http://localhost:8000/clickhouse/etl-status | jq
//    should indicate that `pg_max_logs` is ahead of `ch_max_logs` and `caught_up_logs` is less than 1.0.
// 5. Turn clickhouse on.
// 6. Log some more data, or wait for 10 minutes (for the loop to run on its own), and run the same command. Now,
//    `caught_up_logs` should be 1.0.
export function startClickhouseEtlLoop(): void {
  if (!CLICKHOUSE_PG_URL) {
    return;
  }
  const pino = getLogger();
  (async () => {
    while (true) {
      const startTime = new Date();
      pino.info("Starting Clickhouse ETL");
      try {
        await runEtlLoop({
          timeoutS: ETL_PERIOD_S - 10,
          batchSize: ETL_BATCH_SIZE,
        });
      } catch (e) {
        pino.error({ error: e }, "Failed to run Clickhouse ETL loop");
      }
      const endTime = new Date();
      const elapsedS = (endTime.getTime() - startTime.getTime()) / 1000;
      pino.debug({ elapsedS }, `Clickhouse ETL loop took ${elapsedS} seconds`);
      const sleepS = ETL_PERIOD_S - elapsedS;
      if (sleepS > 0) {
        await new Promise((resolve) => setTimeout(resolve, sleepS * 1000));
      }
    }
  })().catch((e) => {
    pino.error({ error: e }, "Failed to start Clickhouse ETL loop");
  });
}

export async function runEtlLoop({
  timeoutS,
  batchSize,
}: RunEtlLoopInput): Promise<RunEtlLoopOutput> {
  if (!PG_URL || !CLICKHOUSE_PG_URL || !CLICKHOUSE_CONNECT_URL) {
    return {};
  }

  const pgConn = getPG();
  const pgClient = await pgConn.connect();
  await pgClient.query("begin");
  try {
    if (!(await tryAcquireEtlLock(pgClient))) {
      // pino.info("Failed to acquire lock. That's ok! Another loop is running.");
      return { locked: false };
    }
    const loopStart = new Date();
    const tableResults: Record<string, EtlLoopBatch[] | null> = {};
    // This naturally prioritize logs over comments.
    for (const { sourceDbTable, fromTable, toTable } of [
      {
        sourceDbTable: PG_LOGS_TABLE,
        fromTable: PG_REMOTE_LOGS_TABLE,
        toTable: CLICKHOUSE_LOGS_TABLE,
      },
      {
        sourceDbTable: PG_COMMENTS_TABLE,
        fromTable: PG_REMOTE_COMMENTS_TABLE,
        toTable: CLICKHOUSE_COMMENTS_TABLE,
      },
    ]) {
      const fromTableResults = await runEtlLoopTable({
        sourceDbTable,
        fromTable,
        toTable,
        loopStart,
        timeoutS,
        batchSize,
      });
      tableResults[fromTable] = fromTableResults;
      // pino.info({ fromTableResults }, `${fromTable} results:`);
    }
    return { locked: true, tableResults };
  } finally {
    await pgClient.query("rollback");
    pgClient.release();
  }
}

async function runEtlLoopTable({
  sourceDbTable,
  fromTable,
  toTable,
  loopStart,
  timeoutS,
  batchSize,
}: {
  sourceDbTable: string;
  fromTable: string;
  toTable: string;
  loopStart: Date;
  timeoutS: number;
  batchSize: number;
}): Promise<EtlLoopBatch[] | null> {
  const clickhousePGClient = getClickhousePG();
  const pino = getLogger().child({ task: "clickhouse_etl", table: toTable });
  try {
    await clickhousePGClient.query("select 1");
  } catch (e) {
    pino.error({ error: e }, "Failed to connect to ClickHouse. Is it running?");
    return null;
  }

  const pgConn = getPG();

  let lastSequenceId: number = -1;
  let remoteMaxSequenceId: number | null = null;
  const batches: EtlLoopBatch[] = [];
  let currBatchSize: number = batchSize;
  while (true) {
    const currTime = new Date();
    if (currTime.getTime() - loopStart.getTime() > timeoutS * 1000) {
      // pino.info(`${toTable}: time's up`);
      break;
    }
    const maxSequenceId = await selectMaxSequenceId(
      clickhousePGClient,
      toTable,
    );
    if (maxSequenceId === lastSequenceId) {
      // We didn't make any progress. There may be gaps in the data, or we may have caught up.
      if (remoteMaxSequenceId === null) {
        // We can use the less expensive (does not wait for pending transactions) selectMaxSequenceId
        // here directly, vs. getSourceMaxSequenceId, which waits for pending transactions.
        remoteMaxSequenceId = await selectMaxSequenceId(pgConn, sourceDbTable);
      }
      if (maxSequenceId >= remoteMaxSequenceId) {
        // pino.info(`${toTable}: caught up`);
        break;
      } else {
        if (currBatchSize + batchSize > CLICKHOUSE_MAX_BATCH_SIZE) {
          pino.warn(
            {
              toTable,
              maxSequenceId,
              currBatchSize,
            },
            `${toTable}: failed to backfill beyond sequence ID ${maxSequenceId} and batch size ${currBatchSize}. Aborting.`,
          );
          break;
        }
        currBatchSize += batchSize;
        // pino.info({ toTable, currBatchSize }, `${toTable}: increasing batch size to ${currBatchSize}`);
      }
    } else {
      currBatchSize = batchSize;
    }

    lastSequenceId = maxSequenceId;
    const startTime = new Date();

    // Notes:
    // * For some reason, in the latest version of Clickhouse, this query fails to run via the Postgres protocol.
    const sourceMaxSequenceId = await getSourceMaxSequenceId(sourceDbTable);
    // Exclude blocked objects from the backfill.
    //
    // IMPORTANT NOTE: If the set of objects in this batch are all blocked, this
    // loop iteration will not make any progress, and keep trying again with
    // larger batch sizes. So until we actually insert something which isn't
    // blocked, the backfill loop may keep running indefinitely.
    const blockBackfillObjectIdsExpr =
      blockBackfillObjects.length > 0
        ? sql`and object_id not in (${join(
            blockBackfillObjects.map((obj) => sql`${obj.traditionalObjectId}`),
            ",",
          )})`
        : sql``;
    const queryText = sql`
        insert into ${ident(toTable)}
        select *, make_object_id(project_id, experiment_id, dataset_id, prompt_session_id, log_id) object_id
        from ${ident(fromTable)}
        where
            sequence_id > ${maxSequenceId}
            and sequence_id <= ${maxSequenceId} + ${currBatchSize}
            and sequence_id <= ${sourceMaxSequenceId}
            ${blockBackfillObjectIdsExpr}
    `.toPlainStringQuery();
    try {
      const clickhouseConn = getClickhouse();
      await clickhouseConn.command({ query: queryText });
    } catch (e) {
      const elapsedTimeS = (new Date().getTime() - startTime.getTime()) / 1000;
      pino.error(
        {
          toTable,
          elapsedTimeS,
          queryText,
          error: e,
        },
        "Failed to execute clickhouse query",
      );
    }
    const endTime = new Date();
    batches.push({
      maxSequenceId,
      elapsedTimeS: (endTime.getTime() - startTime.getTime()) / 1000,
    });
    pino.debug(
      {
        toTable,
        maxSequenceId,
        batch: batches[batches.length - 1],
      },
      "Completed backfill",
    );
  }
  return batches;
}

async function selectMaxSequenceId(pgClient: Pool | PoolClient, table: string) {
  const { query, params } =
    sql`select max(sequence_id) res from ${ident(table)}`.toNumericParamQuery();
  const { rows } = await pgClient.query(query, params);
  const res = z.coerce.number().nullish().parse(rows[0]["res"]);
  return res ?? 0;
}

async function getActiveTransactions(conn: Pool | PoolClient) {
  const { rows } = await conn.query(
    `SELECT DISTINCT backend_xid FROM pg_stat_activity WHERE backend_xid is not null AND backend_type = 'client backend' and query like '%${LOG_PG_TRANSACTION_QUERY_IDENTIFIER}%'`,
  );
  return new Set(rows.map((row) => row["backend_xid"]));
}

const FLUSH_TIMEOUT_MS = 600 * 1000; // 5 minutes
async function flushAwaitActiveTransactions(
  conn: Pool | PoolClient,
  callerIdentifier?: string,
) {
  const pino = getLogger();
  let activeTransactions = await getActiveTransactions(conn);
  const startTime = new Date();
  let iter = 0;
  while (activeTransactions.size > 0) {
    if (callerIdentifier && iter % 10 === 0) {
      pino.debug(
        {
          callerIdentifier,
          activeTransactions: activeTransactions.size,
        },
        "Active transactions remaining",
      );
    }
    if (new Date().getTime() - startTime.getTime() > FLUSH_TIMEOUT_MS) {
      throw new Error(
        `Timed out waiting for active transactions to flush after ${FLUSH_TIMEOUT_MS}ms`,
      );
    }
    const currentlyActiveTransactions = await getActiveTransactions(conn);
    const stillActiveTransactions = new Set();
    for (const tx of activeTransactions) {
      if (currentlyActiveTransactions.has(tx)) {
        stillActiveTransactions.add(tx);
      }
    }
    activeTransactions = stillActiveTransactions;

    if (activeTransactions.size > 0) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    iter++;
  }

  if (startTime.getTime() - new Date().getTime() > 10 * 1000) {
    pino.warn(
      {
        activeTransactions: activeTransactions.size,
        durationMs: new Date().getTime() - startTime.getTime(),
      },
      "Flushed active transactions took a long time",
    );
  }
}

// Notes:
// * There is a tricky race condition where two concurrent writes may do the following:
//
//   Thread A   |   Thread B | ETL thread
//   ------------------------------------
//    begin      |   begin    |
//    insert     |            |
//               |   insert   |
//               |   commit   |
//               |            |  perform etl
//               |            |    - note Thread B will have a higher sequence id
//               |            |      and Thread A's row is not visible yet, so we
//               |            |      will miss it.
//
// We work around this by snapshotting the max sequence id and the set of active transactions, and then waiting for
// all of those transactions to complete before proceeding. Technically, what this guarantees is that any inflight writes
// which reserved those sequence ids will have either committed (or rolled back), so we don't need to worry about
// missing any rows.
export async function getSourceMaxSequenceId(
  table: string,
  callerIdentifier?: string,
) {
  const pino = getLogger();
  if (!PG_URL) {
    throw new Error("Impossible");
  }
  const pgConn = getPG();
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
      },
      "getSourceMaxSequenceId: getting max sequence id...",
    );
  }
  const maxSequenceId = await selectMaxSequenceId(pgConn, table);
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
        maxSequenceId,
      },
      "max sequence id is",
    );
  }
  await flushAwaitActiveTransactions(pgConn, callerIdentifier);
  if (callerIdentifier) {
    pino.debug(
      {
        callerIdentifier,
      },
      "Finished flushing active transactions.",
    );
  }
  return maxSequenceId;
}

export async function acquireAdvisoryLock(
  conn: Pool | PoolClient,
  lock: keyof AdvisoryLocks,
) {
  const advisoryLocks = await advisoryLockInfo(conn);
  await conn.query(
    `select pg_advisory_xact_lock($1) result /* for ${lock} */`,
    [advisoryLocks[lock]],
  );
}

export async function tryAcquireAdvisoryLock(
  conn: Pool | PoolClient,
  lock: keyof AdvisoryLocks,
) {
  const advisoryLocks = await advisoryLockInfo(conn);
  const { rows } = await conn.query(
    "select pg_try_advisory_xact_lock($1) result /* for ${lock} */",
    [advisoryLocks[lock]],
  );
  return z.boolean().parse(rows[0]["result"]);
}

export async function tryAcquireEtlLock(conn: Pool | PoolClient) {
  return tryAcquireAdvisoryLock(conn, "clickhouse_etl");
}

const etlStatusSchema = z.object({
  ch_max_logs: z.coerce.number().nullish(),
  pg_max_logs: z.coerce.number().nullish(),
  caught_up_logs: z.coerce.number().nullish(),
  ch_max_comments: z.coerce.number().nullish(),
  pg_max_comments: z.coerce.number().nullish(),
  caught_up_comments: z.coerce.number().nullish(),
});

export type EtlStatus = z.infer<typeof etlStatusSchema>;

export async function etlStatus(): Promise<EtlStatus> {
  const { query, params } = sql`
WITH
max_local AS
(
    SELECT max(sequence_id) AS m
    FROM ${ident(CLICKHOUSE_LOGS_TABLE)}
),
max_local_comments AS
(
    SELECT max(sequence_id) AS m
    FROM ${ident(CLICKHOUSE_COMMENTS_TABLE)}
),
max_remote AS
(
    SELECT max(sequence_id) AS m
    FROM ${ident(PG_REMOTE_LOGS_TABLE)}
),
max_remote_comments AS
(
    SELECT max(sequence_id) AS m
    FROM ${ident(PG_REMOTE_COMMENTS_TABLE)}
)
SELECT
    max_local.m AS ch_max_logs,
    max_remote.m AS pg_max_logs,
    if(max_remote.m = 0, 1, max_local.m / max_remote.m) AS caught_up_logs,
    max_local_comments.m AS ch_max_comments,
    max_remote_comments.m AS pg_max_comments,
    if(max_remote_comments.m = 0, 1, max_local_comments.m / max_remote_comments.m) AS caught_up_comments
FROM
    max_local, max_remote, max_local_comments, max_remote_comments
  `.toNumericParamQuery();

  const conn = getClickhousePG();
  const { rows } = await conn.query(query, params);
  if (rows.length !== 1) {
    throw new Error("Expected exactly one row output");
  }
  return etlStatusSchema.parse(rows[0]);
}

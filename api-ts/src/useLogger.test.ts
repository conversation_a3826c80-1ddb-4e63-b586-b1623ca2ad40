import { afterEach, beforeEach, expect, test, vi } from "vitest";
import { registry, useLogger } from "./useLogger";

let originalDebug: string | undefined;
let originalNodeEnv: string | undefined;

beforeEach(() => {
  originalDebug = process.env.DEBUG;
  originalNodeEnv = process.env.NODE_ENV;

  // let's make sure our env is setup so that tests are useful
  delete process.env.NODE_ENV;
  process.env.DEBUG = "true";

  vi.spyOn(console, "log").mockImplementation(() => {});
  vi.spyOn(console, "error").mockImplementation(() => {});
  vi.spyOn(console, "warn").mockImplementation(() => {});
  vi.spyOn(console, "info").mockImplementation(() => {});
  vi.spyOn(console, "debug").mockImplementation(() => {});
});

afterEach(() => {
  if (originalDebug === undefined) {
    delete process.env.DEBUG;
  } else {
    process.env.DEBUG = originalDebug;
  }
  if (originalNodeEnv === undefined) {
    delete process.env.NODE_ENV;
  } else {
    process.env.NODE_ENV = originalNodeEnv;
  }
  vi.restoreAllMocks();
  registry.clear();
});

test("logger initialization and namespace uniqueness", () => {
  useLogger("test1");
  expect(() => useLogger("test1")).toThrow("Logger already registered: test1");
  expect(() => useLogger("test2")).not.toThrow();
});

test("logger formats messages with namespace", () => {
  const logger = useLogger("test");
  logger.info("test message");

  expect(console.info).toHaveBeenCalledWith(
    "test message",
    JSON.stringify({ namespace: "test", level: "info" }),
  );
});

test("logger handles error objects", () => {
  const logger = useLogger("test");
  const error = new Error("test error");

  logger.error("error", { error });

  expect(console.error).toHaveBeenCalledWith(
    "error",
    JSON.stringify({
      namespace: "test",
      level: "error",
      "error.name": "Error",
      "error.message": "test error",
    }),
  );
});

test("logger stringifies objects", () => {
  const logger = useLogger("test");

  logger.info("object", { foo: "bar" });

  expect(console.info).toHaveBeenCalledWith(
    "object",
    JSON.stringify({ namespace: "test", level: "info", foo: "bar" }),
  );
});

test("logger respects DEBUG environment variable", () => {
  process.env.NODE_ENV = "development";

  const logger = useLogger("test");
  logger.debug("debug message");

  expect(console.debug).toHaveBeenCalledWith(
    "debug message",
    JSON.stringify({ namespace: "test", level: "debug" }),
  );
});

test("logger suppresses logs in test environment", () => {
  delete process.env.DEBUG;
  process.env.NODE_ENV = "test";

  const logger = useLogger("test");
  logger.info("test message");

  expect(console.info).not.toHaveBeenCalled();
});

test("logger.log works like info", () => {
  const logger = useLogger("test");
  logger.log("test message");

  expect(console.log).not.toHaveBeenCalled();
  expect(console.info).toHaveBeenCalledWith(
    "test message",
    JSON.stringify({ namespace: "test", level: "info" }),
  );
});

test("logger serializes different types correctly", () => {
  const logger = useLogger("test");
  const date = new Date("2024-01-01T00:00:00Z");
  const url = new URL("https://example.com");
  const params = new URLSearchParams("foo=bar");

  logger.info("complex objects", {
    date,
    url,
    params,
    nested: {
      timestamp: date,
      metadata: {
        url,
        queryParams: params,
      },
    },
  });

  expect(console.info).toHaveBeenCalledWith(
    "complex objects",
    JSON.stringify({
      namespace: "test",
      level: "info",
      date: "2024-01-01T00:00:00.000Z",
      url: "https://example.com/",
      params: "foo=bar",
      "nested.timestamp": "2024-01-01T00:00:00.000Z",
      "nested.metadata.url": "https://example.com/",
      "nested.metadata.queryParams": "foo=bar",
    }),
  );
});

test("logger omits undefined and null values from context", () => {
  const logger = useLogger("test");

  logger.info("test message", {
    defined: "value",
    undef: undefined,
    nullValue: null,
    valid: true,
  });

  expect(console.info).toHaveBeenCalledWith(
    "test message",
    JSON.stringify({
      namespace: "test",
      level: "info",
      defined: "value",
      valid: true,
    }),
  );
});

test("logger handles nested objects and arrays with complex types", () => {
  const logger = useLogger("test");
  const error = new Error("nested error");
  const date = new Date("2024-01-01T00:00:00Z");
  const url = new URL("https://example.com");
  const params = new URLSearchParams("foo=bar");

  logger.info("complex nested structures", {
    object: {
      nested: {
        error,
        timestamp: date,
        metadata: {
          url,
          queryParams: params,
        },
      },
    },
    array: [
      error,
      date,
      { url, params },
      [new URL("https://other.com"), new Date("2024-02-01T00:00:00Z")],
    ],
  });

  expect(console.info).toHaveBeenCalledWith(
    "complex nested structures",
    JSON.stringify({
      namespace: "test",
      level: "info",
      "object.nested.error.name": "Error",
      "object.nested.error.message": "nested error",
      "object.nested.timestamp": "2024-01-01T00:00:00.000Z",
      "object.nested.metadata.url": "https://example.com/",
      "object.nested.metadata.queryParams": "foo=bar",
      "array.0.name": "Error",
      "array.0.message": "nested error",
      "array.1": "2024-01-01T00:00:00.000Z",
      "array.2.url": "https://example.com/",
      "array.2.params": "foo=bar",
      "array.3.0": "https://other.com/",
      "array.3.1": "2024-02-01T00:00:00.000Z",
    }),
  );
});

import { z } from "zod";
import { getRedis } from "./redis";
import { mapAt, deterministicReplacer } from "@braintrust/core";
import { functionCacheKeyPrefix } from "./proxy/functions";
import { REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX } from "./util";
import { sha256Digest } from "./proxy/proxy";

const promptCacheInvalidationSchema = z.strictObject({
  projectId: z.string(),
  slug: z.string().nullish(),
  promptId: z.string(),
});

export type PromptCacheInvalidation = z.infer<
  typeof promptCacheInvalidationSchema
>;

export async function executePromptCacheInvalidations({
  promptCacheInvalidations,
  projectIdToName,
}: {
  promptCacheInvalidations: PromptCacheInvalidation[];
  projectIdToName: Map<string, string>;
}) {
  // De-duplicate the invalidations.
  const invalidationsSet = new Set<string>();
  for (const entry of promptCacheInvalidations) {
    invalidationsSet.add(JSON.stringify(entry, deterministicReplacer));
  }
  promptCacheInvalidations = promptCacheInvalidationSchema
    .array()
    .parse([...invalidationsSet.keys()].map((x) => JSON.parse(x)));

  const invalidationPrefixes = promptCacheInvalidations
    .map((x) =>
      collectPromptCacheInvalidationPrefixes({
        ...x,
        projectName: mapAt(projectIdToName, x.projectId),
      }),
    )
    .flat();
  await commitPromptCacheInvalidations(invalidationPrefixes);
}

function collectPromptCacheInvalidationPrefixes({
  projectId,
  projectName,
  slug,
  promptId,
}: PromptCacheInvalidation & { projectName: string }): string[] {
  const prefixes = [];
  if (projectId && slug) {
    prefixes.push(promptCacheKeyPrefix({ projectId, slug }));
  }
  if (projectName && slug) {
    prefixes.push(
      promptCacheKeyPrefix({ projectName, slug }),
      functionCacheKeyPrefix({ project_name: projectName, slug }),
    );
  }
  if (promptId) {
    prefixes.push(
      promptCacheKeyPrefix({ promptId }),
      functionCacheKeyPrefix({ function_id: promptId }),
    );
  }
  return prefixes;
}

async function commitPromptCacheInvalidations(
  prefixes: string[],
): Promise<void> {
  const redisConn = await getRedis();
  // Grab all the cache keys from each prefix-set. Delete all the cache keys and
  // the prefix sets themselves.
  const prefixSetKeys = prefixes.map(makePromptKeysPrefixSetKey);
  const prefixMatchingKeys = (
    await Promise.all(
      prefixSetKeys.map((prefixSetKey) => {
        return redisConn.sMembers(prefixSetKey);
      }),
    )
  ).flat();
  const allKeys = prefixMatchingKeys.concat(prefixSetKeys);
  if (!allKeys.length) {
    return;
  }
  await redisConn.del(allKeys);
}

export function promptCacheKeyPrefix(
  args:
    | { promptId: string }
    | { projectId: string; slug: string }
    | { projectName: string; slug: string },
): string {
  if ("promptId" in args) {
    return `load_prompt_id:${args.promptId}`;
  } else if ("projectId" in args) {
    return `load_prompt_project_id_slug:${args.projectId}:${args.slug}`;
  } else {
    return `load_prompt_project_id_slug:${args.projectName}:${args.slug}`;
  }
}

function makePromptKeysPrefixSetKey(promptPrefix: string): string {
  return `${REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX}:${promptPrefix}`;
}

export function constructPrefixSetAndCacheKeys({
  prefix,
  version,
  token,
}: {
  prefix: string;
  version?: string;
  token?: string;
}): { prefixSetKey: string | undefined; cacheKey: string } {
  const tokenDigest = sha256Digest(token ?? "anon");
  // If there is a version, don't include the key in the prefix set, so that
  // it's not automatically invalidated.
  return version
    ? {
        prefixSetKey: undefined,
        cacheKey: `${prefix}:${version}:${tokenDigest}`,
      }
    : {
        prefixSetKey: makePromptKeysPrefixSetKey(prefix),
        cacheKey: `${prefix}:latest:${tokenDigest}`,
      };
}

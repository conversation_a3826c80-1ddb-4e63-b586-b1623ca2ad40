import { z } from "zod";

const ISO8061Schema = z.string().datetime();
const OrganizationIdSchema = z.string();

// keep values that are undefined
// https://github.com/colinhacks/zod/issues/2762#issuecomment-2345380864
const undef = Symbol("undef");
const record = (val: z.ZodType) =>
  z
    .record(
      z.string(),
      z.preprocess(
        (v) => (v == undefined ? undef : v),
        z.union([val, z.symbol(undef), z.undefined()]),
      ),
    )
    .transform((v) => {
      for (const key in v) {
        if (v[key] === undef) v[key] = undefined;
      }
      return v;
    });

const EventPropertiesSchema = record(
  z.union([z.string(), z.number(), z.boolean()]),
);

type EventProperties = z.infer<typeof EventPropertiesSchema>;

export const BaseEventSchema = <
  T extends z.ZodType<string>,
  P extends z.ZodType<EventProperties>,
>(
  eventName: T,
  properties: P,
) =>
  z.object({
    event_name: eventName,
    idempotency_key: z.string(),
    timestamp: ISO8061Schema,
    external_customer_id: OrganizationIdSchema,
    properties,
  });

export const AnyEventSchema = BaseEventSchema(
  z.string(),
  EventPropertiesSchema,
).strict();

export type AnyEvent = z.infer<typeof AnyEventSchema>;
export type TelemetryConfig = {
  url: string;
  secret?: string;
};

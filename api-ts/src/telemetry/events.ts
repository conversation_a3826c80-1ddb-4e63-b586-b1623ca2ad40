import { z } from "zod";
import { BaseEventSchema } from "./types";

export const FunctionInvokedEventSchema = BaseEventSchema(
  z.literal("FunctionInvokedEvent"),
  z.object({
    org_id: z.string(),
    xact_id: z.string(),
    app_origin: z.string(),
    proxy_url: z.string(),
    project_id: z.string(),
    row_id: z.string(),
    span_id: z.string(),
    root_span_id: z.string(),
    span_parents_csv: z.string().nullish(),
    mode: z.string().optional(),
    timeout_ms: z.number().optional(),
    messages_bytes: z.number(),
    input_bytes: z.number(),
    function_id: z.string(),
    function_type: z.string().nullish(),
    function_data_type: z.string(),
    function_data_bytes: z.number(),
    prompt_data_prompt_type: z.string().optional(),
    prompt_data_bytes: z.number(),
    invoke_method_type: z.string().optional(),
    duration_ms: z.number(),
  }),
);

export type FunctionInvokedEvent = z.infer<typeof FunctionInvokedEventSchema>;

export const LogInsertedEventSchema = BaseEventSchema(
  z.literal("LogInsertedEvent"),
  z.object({
    object_type: z.string(),
    object_id: z.string(),
    row_id: z.string(),
    org_id: z.string(),
    log_bytes: z.number(),
    project_id: z.string().nullish(),
    xact_id: z.string(),
    span_attributes_type: z.string().nullish(),
    span_id: z.string().nullish(),
    root_span_id: z.string().nullish(),
    span_parents_csv: z.string().nullish(),
  }),
);

export type LogInsertedEvent = z.infer<typeof LogInsertedEventSchema>;

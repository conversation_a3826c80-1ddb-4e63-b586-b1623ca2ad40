import { Request, Response } from "express";
import { getRequestContext } from "../request_context";
import { getTelemetryConfigs, redactSecrets } from "./getTelemetryConfig";
import { checkTokenAuthorized } from "../token_auth";
import { useTelemetryCache } from "./cache";
import { TelemetryConfig } from "./types";
import { TELEMETRY_ENABLED } from "../env";

// These API handlers are internal for debugging purposes. Re-enable in api.ts and app.py, if needed.

export async function getBillingStatusRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);

  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
    })
  ).me;

  const telemetryConfigs = await getTelemetryConfigs({
    token: ctx.token,
    orgIds: authInfo.organizations.map((org) => org.id),
  });

  res.json({
    enabled: TELEMETRY_ENABLED,
    configs: redactSecrets(telemetryConfigs, ctx.token),
  });
}

export async function invalidateTelemetryCacheRequest(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);

  const telemetryCache = useTelemetryCache();

  const authInfo = await (
    await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
    })
  ).me;

  // Set the cache to expire in 1 second, so that it will be invalidated
  await telemetryCache.setTelemetryConfigs(
    authInfo.organizations.reduce(
      (acc: Record<string, TelemetryConfig>, org) => {
        acc[org.id] = { url: "", secret: "" };
        return acc;
      },
      {},
    ),
    1,
  );

  res.json({});
}

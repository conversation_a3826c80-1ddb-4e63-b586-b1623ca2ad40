import { RedisClientType } from "redis";
import { getRedis } from "../redis";
import { TelemetryConfig } from "./types";

function useRedisNamespace(namespace: string) {
  let redisClient: RedisClientType | undefined;

  const getClient = async () => {
    if (!redisClient) {
      redisClient = await getRedis();
    }
    return redisClient;
  };

  return {
    get: async (key: string): Promise<string | null> => {
      const client = await getClient();
      const value = await client.get(`${namespace}:${key}`);
      return value ?? null;
    },

    set: async (
      key: string,
      value: string,
      ttlSeconds = 3600,
    ): Promise<void> => {
      const client = await getClient();
      await client.set(`${namespace}:${key}`, value, {
        EX: ttlSeconds,
      });
    },

    batchGet: async (keys: string[]): Promise<Record<string, string>> => {
      const client = await getClient();
      const values = await client.mGet(keys.map((k) => `${namespace}:${k}`));

      return Object.fromEntries(
        values
          .map((v: string | null, i: number) => [keys[i], v ?? null])
          .filter(([_, v]) => v !== null),
      );
    },

    batchSet: async (
      items: Record<string, string>,
      ttlSeconds = 3600,
    ): Promise<void> => {
      const client = await getClient();
      const pipeline = client.multi();
      for (const [key, value] of Object.entries(items)) {
        pipeline.set(`${namespace}:${key}`, value, {
          EX: ttlSeconds,
        });
      }
      await pipeline.exec();
    },
  };
}

export function useTelemetryCache() {
  const redis = useRedisNamespace("telemetry");

  return {
    getTelemetryConfigs: async (
      orgIds: string[],
    ): Promise<Record<string, TelemetryConfig>> => {
      return Object.fromEntries(
        Object.entries(await redis.batchGet(orgIds)).map(([orgId, config]) => [
          orgId,
          JSON.parse(config),
        ]),
      );
    },

    setTelemetryConfigs: async (
      configs: Record<string, TelemetryConfig>,
      ttlSeconds = 3600,
    ) => {
      await redis.batchSet(
        Object.fromEntries(
          Object.entries(configs).map(([orgId, config]) => [
            orgId,
            JSON.stringify(config),
          ]),
        ),
        ttlSeconds,
      );
    },
  };
}

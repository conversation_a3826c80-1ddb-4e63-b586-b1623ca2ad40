import { Request, Response } from "express";
import { z } from "zod";
import { getPG } from "./db/pg";
import { join, sql } from "@braintrust/btql/planner";
import { sha256Digest } from "./proxy/proxy";
import {
  decryptMessage,
  encryptedMessageSchema,
  encryptMessage,
} from "@braintrust/proxy/utils";
import {
  encryptedEnvSecretSchema,
  EncryptedEnvSecret,
} from "@braintrust/local/functions";
import { FUNCTION_SECRET_KEY } from "./env";
import {
  AccessDeniedError,
  BadRequestError,
  BT_ENABLE_AUDIT_HEADER,
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  getWasCachedToken,
  parseUrlBool,
  REDIS_ENV_SECRET_FUNCTION_KEY_PREFIX,
  wrapZodError,
} from "./util";
import { getRedis } from "./redis";
import {
  AclObjectType,
  envVarObjectTypeEnum,
  envVarSchema,
  EnvVar,
} from "@braintrust/core/typespecs";
import { getRequestContext } from "./request_context";
import { objectNullish, parseNoStrip } from "@braintrust/core";
import { OBJECT_CACHE } from "./object_cache";
import { checkTokenAuthorized } from "./token_auth";
import { AuditObject, setAuditHeaders } from "./audit";
import { parseHeader } from "./auth-header";

export const secretIdSchema = z.object({
  orgId: z.string().optional(),
  projectId: z.string().optional(),
  functionId: z.string().optional(),
});
export type SecretScope = z.infer<typeof secretIdSchema>;

export async function loadSecret(
  args: SecretScope & {
    secret_name: string;
  },
): Promise<EncryptedEnvSecret | undefined> {
  const values = await loadFunctionSecrets(args);
  if (values.length === 0) {
    return undefined;
  } else if (values.length > 1) {
    throw new Error("Ambiguous secret scope (returned multiple secrets)");
  }
  return values[0];
}

const SECRET_PROJECTION = sql`id, object_type, object_id, secret_name, secret_value, to_json(created_at)#>>'{}' as created_at, to_json(used_at)#>>'{}' as used_at`;
export async function loadFunctionSecrets({
  orgId,
  projectId,
  functionId,
  secretName,
  ids,
  skipUsedAt,
}: SecretScope & {
  secretName?: string;
  ids?: string[];
  skipUsedAt?: boolean;
}): Promise<EncryptedEnvSecret[]> {
  if (!orgId && !projectId && !functionId && !(ids && ids.length > 0)) {
    throw new Error("No scope provided");
  }

  const scopeFilters =
    orgId || projectId || functionId
      ? sql`AND (
      FALSE
      OR ${orgId ? sql`(object_type = 'organization' AND object_id = ${orgId})` : sql`FALSE`}
      OR ${projectId ? sql`(object_type = 'project' AND object_id = ${projectId})` : sql`FALSE`}
      OR ${functionId ? sql`(object_type = 'function' AND object_id = ${functionId})` : sql`FALSE`}
  )`
      : sql``;

  const whereClause = sql`WHERE
        TRUE
        ${scopeFilters}
        ${secretName ? sql`AND secret_name = ${secretName}` : sql``}
        ${
          ids && ids.length > 0
            ? sql`AND id IN (${join(
                ids.map((id) => sql`${id}`),
                ", ",
              )})`
            : sql``
        }
  `;

  const pg = getPG();
  const query = skipUsedAt
    ? sql`
  SELECT ${SECRET_PROJECTION} FROM function_secrets ${whereClause}
  `
    : sql`
    UPDATE function_secrets
    SET used_at = NOW()
    ${whereClause}
    RETURNING ${SECRET_PROJECTION}
    `;
  const { query: queryText, params } = query.toNumericParamQuery();

  const { rows } = await pg.query(queryText, params);
  return await Promise.all(
    rows.map(async (row) => {
      const message = encryptedMessageSchema.parse(
        JSON.parse(row.secret_value),
      );
      return encryptedEnvSecretSchema.parse({
        ...row,
        name: row.secret_name,
        created: row.created_at + "Z",
        used: row.used_at ? row.used_at + "Z" : undefined,
        ...message,
      });
    }),
  );
}

export async function decryptSecrets(
  secrets: EncryptedEnvSecret[],
): Promise<Record<string, string>> {
  if (secrets.length === 0) {
    return {};
  }
  const encryptionKey = await getFunctionSecretEncryptionKey();
  return Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    (
      await Promise.all(
        secrets.map(
          async (secret): Promise<[string, string | undefined]> => [
            secret.name,
            await decryptMessage(encryptionKey, secret.iv, secret.data).catch(
              (e) => {
                throw new Error(
                  `Failed to decrypt secret ${secret.name}: ${e}`,
                );
              },
            ),
          ],
        ),
      )
    ).filter(([_, value]) => value !== undefined) as [string, string][],
  );
}

function rowToEnvVar(row: Record<string, unknown>): EnvVar {
  const { secret_value: _, ...rest } = row;
  return envVarSchema.parse({
    ...rest,
    name: row.secret_name,
    created: row.created_at + "Z",
    used: row.used_at ? row.used_at + "Z" : undefined,
  });
}

export async function saveFunctionSecret({
  secretName,
  secretValue,
  objectType,
  objectId,
  ignoreExisting,
}: {
  secretName: string;
  secretValue: string;
  objectType: z.infer<typeof envVarObjectTypeEnum>;
  objectId: string;
  ignoreExisting?: boolean;
}): Promise<EnvVar> {
  const encryptionKey = await getFunctionSecretEncryptionKey();
  const encrypted = await encryptMessage(encryptionKey, secretValue);

  const pg = getPG();
  const query = sql`
    WITH upsert AS (
      INSERT INTO function_secrets (secret_name, secret_value, object_type, object_id)
      VALUES (${secretName}, ${encrypted}, ${objectType}, ${objectId})
      ON CONFLICT (secret_name, object_type, object_id)
        ${ignoreExisting ? sql`DO NOTHING` : sql`DO UPDATE SET secret_value = ${encrypted}`}
      RETURNING *
    )
    SELECT ${SECRET_PROJECTION} FROM upsert
    UNION ALL
    SELECT ${SECRET_PROJECTION} FROM function_secrets
    WHERE secret_name = ${secretName} AND object_type = ${objectType} AND object_id = ${objectId}
      AND NOT EXISTS (SELECT 1 FROM upsert)
    LIMIT 1
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);
  const row = rowToEnvVar(rows[0]);

  await invalidateSecretCache({
    objectType: row.object_type,
    objectId: row.object_id,
  });
  return row;
}

export async function deleteFunctionSecret({
  secretId,
}: {
  secretId: string;
}): Promise<EnvVar | undefined> {
  const pg = getPG();
  const query = sql`
    DELETE FROM function_secrets WHERE id = ${secretId}
    RETURNING ${SECRET_PROJECTION}
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);
  if (rows.length === 0) {
    return undefined;
  }
  const row = rowToEnvVar(rows[0]);

  await invalidateSecretCache({
    objectType: row.object_type,
    objectId: row.object_id,
  });

  return row;
}

export async function updateFunctionSecret({
  secretId,
  secretName,
  secretValue,
}: {
  secretId: string;
  secretName?: string | null;
  secretValue?: string | null;
}): Promise<EnvVar | undefined> {
  const encryptionKey = await getFunctionSecretEncryptionKey();
  const clauses = [
    ...(secretName ? [sql`secret_name = ${secretName}`] : []),
    ...(secretValue
      ? [
          sql`secret_value = ${await encryptMessage(encryptionKey, secretValue)}`,
        ]
      : []),
  ];

  if (clauses.length === 0) {
    throw new BadRequestError("Must specify either name or value");
  }

  const pg = getPG();
  const query = sql`
    UPDATE function_secrets
    SET ${join(clauses, ", ")}
    WHERE id = ${secretId}
    RETURNING ${SECRET_PROJECTION}
  `;
  const { query: queryText, params } = query.toNumericParamQuery();
  const { rows } = await pg.query(queryText, params);

  if (rows.length === 0) {
    return undefined;
  }
  const row = rowToEnvVar(rows[0]);

  await invalidateSecretCache({
    objectType: row.object_type,
    objectId: row.object_id,
  });

  return row;
}

export async function invalidateSecretCache({
  objectType,
  objectId,
}: {
  objectType: z.infer<typeof envVarObjectTypeEnum>;
  objectId: string;
}) {
  const redisConn = await getRedis();
  if (redisConn) {
    const prefixes = getFunctionSecretCacheKeyPrefixes({
      orgId: objectType === "organization" ? objectId : undefined,
      projectId: objectType === "project" ? objectId : undefined,
      functionId: objectType === "function" ? objectId : undefined,
    });
    const keys = await Promise.all(
      prefixes.map(async (prefix) => {
        const keys = await redisConn.sMembers(prefix);
        if (keys.length > 0) {
          await redisConn.sRem(prefix, keys);
        }
        return keys;
      }),
    );

    const allKeys = keys.flat();
    if (allKeys.length > 0) {
      await redisConn.del(allKeys);
    }
  }
}

const envVarApiBaseSchema = envVarSchema
  .omit({ name: true, created: true, used: true })
  .extend({
    env_var_name: z.string(),
  });

const envVarReadSchema = envVarApiBaseSchema.omit({ id: true }).extend({
  ids: z.union([z.string(), z.array(z.string())]),
});

const envVarWriteSchema = envVarSchema
  .omit({
    id: true,
    created: true,
    used: true,
  })
  .extend({
    value: z.string(),
  });

export async function v1EnvVarReadHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() =>
    parseNoStrip(objectNullish(envVarReadSchema), ctx.data),
  );

  // This is somewhat annoying, in that you _must_ provide the scope (object_type and object_id)
  // even if you provide ids, but it's a bit tricky to implement the ACL checks if you specify
  // a list of ids. If it becomes something people need, we can implement it after retrieving
  // from the database.
  if (!params.object_type || !params.object_id) {
    throw new BadRequestError("Must specify both object_type and object_id");
  }

  const secrets = await loadFunctionSecrets({
    ...(params.object_type === "organization" && params.object_id
      ? { orgId: params.object_id }
      : {}),
    ...(params.object_type === "project" && params.object_id
      ? { projectId: params.object_id }
      : {}),
    ...(params.object_type === "function" && params.object_id
      ? { functionId: params.object_id }
      : {}),
    secretName: params.env_var_name ?? undefined,
    ids: params.ids
      ? Array.isArray(params.ids)
        ? params.ids
        : [params.ids]
      : undefined,
    skipUsedAt: true,
  });

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: [
        await envVarReadPermissionCheck({
          appOrigin: ctx.appOrigin,
          token: ctx.token,
          objectType: params.object_type,
          objectId: params.object_id,
          wasCachedToken: getWasCachedToken(
            req,
            BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
          ),
        }),
      ],
    });
  }
  res.json({
    objects: secrets.map(
      (s): z.infer<typeof envVarSchema> => ({
        id: s.id,
        name: s.name,
        object_type: s.object_type,
        object_id: s.object_id,
        created: s.created,
        used: s.used,
      }),
    ),
  });
}

export async function v1EnvVarReadIdHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  // No query params.
  parseNoStrip(z.object({}), ctx.data);
  const secrets = await loadFunctionSecrets({
    ids: [req.params.id],
    skipUsedAt: true,
  });
  if (secrets.length === 0) {
    throw new AccessDeniedError({
      permission: "read",
      objectType: "env_var",
      objectId: req.params.id,
    });
  }

  const secret = secrets[0];
  const auditObject = await envVarReadPermissionCheck({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    objectType: secret.object_type,
    objectId: secret.object_id,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: [auditObject],
    });
  }
  const envVar: EnvVar = {
    id: secret.id,
    name: secret.name,
    object_type: secret.object_type,
    object_id: secret.object_id,
    created: secret.created,
    used: secret.used,
  };
  res.json(envVar);
}

export async function v1EnvVarWriteHandler(req: Request, res: Response) {
  if (!(req.method === "POST" || req.method === "PUT")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  const update = req.method !== "POST";
  const params = wrapZodError(() => parseNoStrip(envVarWriteSchema, ctx.data));

  const aclObjectType: AclObjectType =
    params.object_type === "function" ? "prompt" : params.object_type;
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    aclObjectType,
    overrideRestrictObjectType: undefined,
    objectId: params.object_id,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });
  if (!permissions.permissions.includes("update")) {
    throw new AccessDeniedError({
      permission: "update",
      aclObjectType,
      overrideRestrictObjectType: undefined,
      objectId: params.object_id,
    });
  }

  const insertedRow = await saveFunctionSecret({
    secretName: params.name,
    secretValue: params.value,
    objectType: params.object_type,
    objectId: params.object_id,
    ignoreExisting: !update,
  });

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: [permissions],
    });
  }
  res.json(insertedRow);
}

export async function v1EnvVarIdWriteHandler(req: Request, res: Response) {
  if (!(req.method === "DELETE" || req.method === "PATCH")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  // First, read the secret and validate that we have permissions to update it. The
  // object_type and object_id cannot be changed, so I don't think there is a race
  // condition.

  const secrets = await loadFunctionSecrets({
    ids: [req.params.id],
    skipUsedAt: true,
  });
  const permission = "update";
  if (secrets.length === 0) {
    throw new AccessDeniedError({
      permission,
      objectId: req.params.id,
      objectType: "env_var",
    });
  }

  const secret = secrets[0];
  const aclObjectType: AclObjectType =
    secret.object_type === "function" ? "prompt" : secret.object_type;
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    aclObjectType,
    overrideRestrictObjectType: undefined,
    objectId: secret.object_id,
  });
  if (!permissions.permissions.includes(permission)) {
    throw new AccessDeniedError({
      permission,
      aclObjectType,
      overrideRestrictObjectType: undefined,
      objectId: secret.object_id,
    });
  }

  let row: EnvVar;
  if (req.method === "DELETE") {
    const deletedRow = await deleteFunctionSecret({
      secretId: req.params.id,
    });
    if (!deletedRow) {
      throw new AccessDeniedError({
        permission,
        objectId: req.params.id,
        objectType: "env_var",
      });
    }
    row = deletedRow;
  } else {
    const nameAndValue = wrapZodError(() =>
      parseNoStrip(
        objectNullish(
          z.object({
            name: z.string(),
            value: z.string(),
          }),
        ),
        ctx.data,
      ),
    );
    if (!nameAndValue.name && !nameAndValue.value) {
      row = {
        id: secret.id,
        name: secret.name,
        object_type: secret.object_type,
        object_id: secret.object_id,
        created: secret.created,
        used: secret.used,
      };
    } else {
      const updatedRow = await updateFunctionSecret({
        secretId: req.params.id,
        secretName: nameAndValue.name,
        secretValue: nameAndValue.value,
      });
      if (!updatedRow) {
        throw new AccessDeniedError({
          permission: "update",
          objectId: req.params.id,
          objectType: "env_var",
        });
      }
      row = updatedRow;
    }
  }

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: [permissions],
    });
  }
  res.json(row);
}

// We use the SHA256 to make sure it's the right length and base64 encoded.
async function getFunctionSecretEncryptionKey(): Promise<string> {
  if (!FUNCTION_SECRET_KEY) {
    throw new Error("FUNCTION_SECRET_KEY is not set");
  }
  return sha256Digest(FUNCTION_SECRET_KEY);
}

export function getFunctionSecretCacheKeyPrefixes({
  orgId,
  projectId,
  functionId,
}: SecretScope): string[] {
  const ret: string[] = [];
  if (orgId) {
    ret.push(REDIS_ENV_SECRET_FUNCTION_KEY_PREFIX + "org:" + orgId);
  }
  if (projectId) {
    ret.push(REDIS_ENV_SECRET_FUNCTION_KEY_PREFIX + "project:" + projectId);
  }
  if (functionId) {
    ret.push(REDIS_ENV_SECRET_FUNCTION_KEY_PREFIX + "function:" + functionId);
  }
  return ret;
}

async function envVarReadPermissionCheck({
  appOrigin,
  token,
  objectType,
  objectId,
  wasCachedToken,
}: {
  appOrigin: string;
  token: string | undefined;
  objectType: z.infer<typeof envVarObjectTypeEnum>;
  objectId: string;
  wasCachedToken: string | undefined;
}): Promise<AuditObject> {
  // For reading organization env vars, we just require the user to be a member
  // of the org.
  if (objectType === "organization") {
    const { me: mePromise } = await checkTokenAuthorized({
      ctxToken: token,
      appOrigin,
    });
    const me = await mePromise;
    const org = me.organizations.find((x) => x.id === objectId);
    if (!org) {
      throw new AccessDeniedError({
        permission: "org-membership",
        objectType,
        objectId,
      });
    }
    return {
      acl_object_type: objectType,
      object_id: org.id,
      object_name: org.name,
      parent_cols: {},
    };
  }

  const aclObjectType: AclObjectType =
    objectType === "function" ? "prompt" : objectType;
  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin: appOrigin,
    authToken: token,
    aclObjectType,
    overrideRestrictObjectType: undefined,
    objectId,
    wasCachedToken: wasCachedToken,
  });
  if (!permissions.permissions.includes("read")) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType,
      overrideRestrictObjectType: undefined,
      objectId,
    });
  }
  return permissions;
}

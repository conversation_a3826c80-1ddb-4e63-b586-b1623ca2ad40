import { parsedQuerySchema, parseQuery } from "@braintrust/btql/parser";
import { bindQuery, getResultSchema } from "@braintrust/btql/binder";
import { addCoercions } from "@braintrust/btql/planner";
import { expect, test } from "vitest";
import { BRAINTRUST_LOGICAL_SCHEMA } from "@braintrust/local/api-schema";

test("binder corner cases", () => {
  // These are laborious tests to track but they allow us to ensure that we bind tricky cases correctly.
  const cases = [
    {
      query:
        "from: project('495d041a-7790-4907-b3c7-20ee864569cc') | select: scores.foo",
    },
    {
      query:
        "from: project('495d041a-7790-4907-b3c7-20ee864569cc') | measures: sum(metrics.prompt_tokens+metrics.completion_tokens) as a, sum(metrics.tokens) as b",
    },
    {
      query:
        "from: project_logs('495d041a-7790-4907-b3c7-20ee864569cc') | select: * | filter: metadata.mode='assistant'",
    },
    {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: ["project"],
          },
          args: [],
        },
        dimensions: [
          {
            alias: "project_id",
            expr: {
              btql: "project_id",
            },
          },
        ],
        measures: [
          {
            alias: "num_logs",
            expr: {
              btql: "sum(is_root and log_id='g')",
            },
          },
          {
            alias: "tokens",
            expr: {
              btql: "sum(metrics.tokens)",
            },
          },
          {
            alias: "ttf",
            expr: {
              btql: "avg(metrics.time_to_first_token)",
            },
          },
          {
            alias: "p50_duration",
            expr: {
              btql: "percentile(is_root ? metrics.end-metrics.start : null, 0.5)",
            },
          },
          {
            alias: "p95_duration",
            expr: {
              btql: "percentile(is_root ? metrics.end-metrics.start : null, 0.95)",
            },
          },
        ],
        filter: {
          op: "ge",
          left: {
            btql: "created",
          },
          right: {
            op: "literal",
            value: "2024-04-14T07:00:00Z",
          },
        },
      },
    },
    {
      // Percentile should always be "number"
      query:
        "from: project_logs('495d041a-7790-4907-b3c7-20ee864569cc') | measures: percentile(metrics.tokens, 95)",
    },
    {
      // Cast should propagate through the coalesce
      query:
        "from: project_logs('495d041a-7790-4907-b3c7-20ee864569cc') | select: 1 | filter: COALESCE(span_attributes.purpose, 'default') != 'scorer'",
    },
  ];

  for (const { query } of cases) {
    const parsed =
      typeof query === "string"
        ? parseQuery(query)
        : parsedQuerySchema.parse(query);
    const queryText = typeof query === "string" ? query : undefined;
    const bound = addCoercions(
      bindQuery({
        query: parsed,
        schema: BRAINTRUST_LOGICAL_SCHEMA,
        queryText,
        applyComputedFields: undefined,
      }),
    );
    expect(bound).toMatchSnapshot();
    const resultType = getResultSchema(bound);
    expect(resultType).toMatchSnapshot();
  }
});

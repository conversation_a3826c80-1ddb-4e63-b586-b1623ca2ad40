import { ident, join, sql, ToSQL } from "@braintrust/btql/planner";
import { Dialect, LEAF_OBJECT_ID_FIELDS } from "./schema";

export function makeObjectIdExprFromTable(
  table: string,
  dialect: Dialect,
): ToSQL {
  if (dialect === "postgres") {
    return sql`make_object_id(${join(
      LEAF_OBJECT_ID_FIELDS.map((field) => sql`${ident([table, field])}`),
      ", ",
    )})`;
  } else {
    return sql`${ident([table, "object_id"])}`;
  }
}

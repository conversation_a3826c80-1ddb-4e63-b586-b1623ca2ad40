import { ParsedQuery } from "@braintrust/btql/parser";
import {
  AUDIT_METADATA_FIELD,
  AUDIT_SOURCE_FIELD,
  BT_CURSOR_HEADER,
  BT_FOUND_EXISTING_HEADER,
  BT_IMPERSONATE_USER,
  BT_PARENT,
  IS_MERGE_FIELD,
  MERGE_PATHS_FIELD,
  OBJECT_DELETE_FIELD,
  SpanComponentsV3,
  SpanObjectTypeV3,
  deterministicReplacer,
  isEmpty,
  mapSetDefault,
  parseNoStrip,
  recordSetDefault,
  resolveParentHeader,
} from "@braintrust/core";
import { logger } from "./telemetry/logger";
import {
  EnvVar,
  ViewData,
  aclObjectTypeEnum,
  comparisonExperimentIdParamSchema,
  fetchEventsRequestSchema,
  objectTypes,
  patchOrganizationMembersOutputSchema,
  permissionEnum,
  promptLogIdLiteralSchema,
  summarizeDataParamSchema,
  summarizeScoresParamSchema,
  type AclObjectType,
} from "@braintrust/core/typespecs";
import {
  BRAINTRUST_AUTH_TOKEN_HEADER,
  EMPTY_ERROR_CONTEXT,
  IMPERSONATE_TOKEN_PREFIX,
  augmentWithErrorContext,
} from "@braintrust/local";
import { brainstoreSystemStatus, Me } from "@braintrust/local/app-schema";
import {
  OtelBadRequestError,
  OtelProtoLoader,
  makeExportTraceServiceResponse,
  otelTraceToRows,
  type RejectedSpan,
  type Row,
} from "./otel";
import { ProxyBadRequestError } from "@braintrust/proxy";
import bodyParser from "body-parser";
import { FailedHTTPResponse, spanComponentsToObjectId } from "braintrust";
import compression from "compression";
import cors from "cors";
import express, { NextFunction, Request, Response } from "express";
import { InternalServerError as InternalServerErrorOpenAI } from "openai";
import * as node_util from "util";
import { z } from "zod";
import {
  ASYNC_SCORING_CONFIG_CACHE,
  flushAsyncScoringCache,
} from "./async_scoring";
import { secretBroadcastKeyRequest } from "./broadcast_key";
import { formatCursor, runBtqlRequest } from "./btql";
import { ETL_BATCH_SIZE, etlStatus, runEtlLoop } from "./clickhouse_etl";
import {
  BRAINTRUST_APP_ORIGIN_HEADER,
  ORIGIN_HEADER,
  baseAllowedHeaders,
  checkOrigin,
  extractAllowedOrigin,
} from "./cors";
import { CustomFetchResponse, customFetchRequest } from "./custom_fetch";
import { getPG, pgStats } from "./db/pg";
import {
  ALLOW_CODE_FUNCTION_EXECUTION,
  CLICKHOUSE_PG_URL,
  DEPLOYMENT_MODE,
  GIT_COMMIT,
  PROXY_URL,
  PUBLIC_ALLOWED_ORIGIN,
  REALTIME_URL,
  TS_API_PORT,
} from "./env";
import { ERROR_CONTEXT_CACHE } from "./error_context_cache";
import { EventPublisher, LogToPublisherFn } from "./event_publisher";
import { runStatusCheck } from "./healthcheck";
import {
  liveCronJobs,
  migrationStatus,
  migrationVersion,
  repairMigrationTable,
} from "./migration_info";
import {
  OBJECT_CACHE,
  ObjectCacheEntry,
  flushObjectCache,
  flushOrgObjectCache,
  objectCacheEntryOrgId,
} from "./object_cache";
import {
  PromptFunctionObjectType,
  retrievePrompts,
  runInsertFunctions,
  sanitizeInsertedPromptRows,
  stripPromptMetadata,
} from "./prompt_function_ops";
import { cachedLogin } from "./proxy/functions";
import { RequestContext, getRequestContext } from "./request_context";
import { RunLogDataOutput, runLogData } from "./run_log_data";
import { objectTypeSchema } from "./schema";
import {
  flushSummaryCache,
  flushSummaryCacheRequest,
  runDatasetSummarize,
  runDatasetSummarizeRequest,
  runExperimentSummarize,
  runExperimentSummarizeRequest,
} from "./summary";
import {
  AccessDeniedError,
  BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER,
  BT_AUTOMATION_WAS_CACHED_REDIS_TOKEN_HEADER,
  BT_ENABLE_AUDIT_HEADER,
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER,
  BT_QUERY_PLAN_HEADER,
  BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER,
  BadRequestError,
  ForbiddenError,
  FullAclObjectType,
  HTTPError,
  InternalServerError,
  NotFoundError,
  QueryTooCostlyError,
  TooManyRequestsError,
  extractErrorText,
  getAclObjectId,
  getSysadminRoles,
  getWasCachedToken,
  logRequestFailure,
  objectTypeToAclObjectType,
  parseUrlBool,
  postDefaultHeaders,
  wrapZodError,
} from "./util";
import { hydrateViewData, hydrateViewDatas, insertViewData } from "./view";
import { nextXactId } from "./xact_id";

require("express-async-errors");

// This initializes the duckdb functions, so that in other places we use btql but
// not duckdb, we can polyfill an empty connection.
import {
  OBJECT_TYPE_FIELD,
  objectIdsUnionSchema,
} from "@braintrust/local/api-schema";
import {
  runGetAttachmentRequest,
  runPutAttachmentStatusRequest,
  runUploadAttachmentRequest,
} from "./attachment";
import { AuditObject, setAuditHeaders } from "./audit";
import { parseBraintrustAuthHeader, parseHeader } from "./auth-header";
import {
  brainstoreBackfillRunRequest,
  getGlobalBackfillStatus,
  getProjectBackfillStatus,
  runBrainstoreEtlLoopRequest,
} from "./brainstore/backfill";
import {
  assertBrainstoreEnabled,
  brainstoreDefault,
  brainstoreEnabled,
  canContainRowRefs,
  canPgInsertLogs2,
  runBrainstore,
} from "./brainstore/brainstore";
import {
  deleteTrackingRequest,
  enableTrackingRequest,
  getActiveBackfillOperationsRequest,
  getFailedSegmentsRequest,
  getObjectInfoRequest,
  getSegmentInfoRequest,
  optimizeObjectRequest,
  trackObjectsRequest,
} from "./brainstore/tracked-objects";
import {
  getVacuumStatusRequest,
  resetVacuumStateRequest,
  vacuumObjectRequest,
} from "./brainstore/vacuum";
import "./db/duckdb-conn";
import {
  decryptSecrets,
  loadFunctionSecrets,
  v1EnvVarIdWriteHandler,
  v1EnvVarReadHandler,
  v1EnvVarReadIdHandler,
  v1EnvVarWriteHandler,
} from "./function-secrets";
import { dynamic_installProxyEndpoints } from "./install_proxy_endpoints_var";
import { canUseLambdaQuarantine } from "./lambda-quarantine/pool";
import {
  v1CustomColumnIdWriteHandler,
  v1CustomColumnReadHandler,
  v1CustomColumnWriteHandler,
} from "./project-columns";
import { ResourceCheckException } from "./resource_check";
import { checkTokenAuthorized } from "./token_auth";
import { getCurrentSpan, getTracer } from "./instrumentation/api";
import { SpanStatusCode } from "@opentelemetry/api";
import {
  AUTOMATION_CACHE,
  flushAutomationCache,
  getCloudIdentity,
  testAutomation,
} from "./automations";
import { getLogger } from "./instrumentation/logger";
import {
  deleteAutomationCronJob,
  getCronJobStatusRequest,
  registerCronJobRequest,
  resetCronJobRequest,
  runCronJobRequest,
} from "./cron/cron";
import { forceCronLoopRun } from "./cron/worker";

// Falling back to 0.0.75 as a form of extreme caution, since we are newly
// introducing the API_VERSION environment variable.
export const VERSION = process.env.API_VERSION ?? "0.0.75";
const API_VERSION = 2;

function requireApiVersion(ctx: RequestContext) {
  if (!ctx.api_version) {
    throw new BadRequestError("Must supply api_version in request");
  } else if (ctx.api_version > API_VERSION) {
    throw new BadRequestError(
      `API version ${API_VERSION} cannot fulfill request for version ${ctx.api_version}`,
    );
  }
}

function stringParam(x: qs.ParsedQs[string]): string | undefined {
  if (typeof x === "string") {
    return x;
  } else {
    return undefined;
  }
}

function intParam(x: qs.ParsedQs[string]): number | undefined {
  const res = Number(x);
  return isNaN(res) ? undefined : res;
}

const flushAclEntrySchema = z.object({
  org_id: z.string(),
  object_type: z.string(),
  object_id: z.string(),
});

type FlushAclEntry = z.infer<typeof flushAclEntrySchema>;

class AclFlusher {
  private entries = new Set<string>();

  public addEntry(entry: FlushAclEntry) {
    this.entries.add(JSON.stringify(entry, deterministicReplacer));
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  public addAclObject(acl: any) {
    this.addEntry(
      flushAclEntrySchema.parse({
        org_id: acl["_object_org_id"],
        object_type: acl["object_type"],
        object_id: acl["object_id"],
      }),
    );
  }

  public async flush(): Promise<void> {
    await Promise.all(
      [...this.entries.values()].map((entryStr) => {
        const entry = flushAclEntrySchema.parse(JSON.parse(entryStr));
        return OBJECT_CACHE.flushEntries({
          orgId: entry.org_id,
          aclObjectType: entry.object_type,
          objectId: entry.object_id,
        });
      }),
    );
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
async function flushSingleAclObject(acl: any) {
  const flusher = new AclFlusher();
  flusher.addAclObject(acl);
  await flusher.flush();
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function aclBatchUpdateObjects(resp: any): unknown[] {
  resp = resp ?? {};
  return (resp["added_acls"] || []).concat(resp["removed_acls"] || []);
}

class AsyncScoringConfigFlusher {
  private projectIds = new Set<string>();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  public addProjectScore(projectScore: any) {
    this.projectIds.add(z.string().parse(projectScore["project_id"]));
  }

  public async flush(): Promise<void> {
    await Promise.all(
      [...this.projectIds.keys()].map((projectId) =>
        ASYNC_SCORING_CONFIG_CACHE.flushEntries({ projectId }),
      ),
    );
  }
}

class AutomationFlusher {
  private projectIds = new Set<string>();
  private automationIds = new Set<string>();

  public addProjectAutomation(projectAutomation: unknown) {
    this.projectIds.add(
      z
        .string()
        .parse(
          projectAutomation &&
            typeof projectAutomation === "object" &&
            "project_id" in projectAutomation
            ? projectAutomation["project_id"]
            : undefined,
        ),
    );
    this.automationIds.add(
      z
        .string()
        .parse(
          projectAutomation &&
            typeof projectAutomation === "object" &&
            "id" in projectAutomation
            ? projectAutomation["id"]
            : undefined,
        ),
    );
  }

  public async flush(): Promise<void> {
    await Promise.all([
      ...Array.from(this.projectIds.keys()).map((projectId) =>
        AUTOMATION_CACHE.flushEntries({ projectId }),
      ),
      ...Array.from(this.automationIds.keys()).map((automationId) =>
        Promise.all([
          AUTOMATION_CACHE.flushAutomationInterval({ automationId }),
          AUTOMATION_CACHE.flushEntries({ automationId }),
        ]),
      ),
    ]);
  }
}

async function flushSingleProjectScore(projectScore: unknown) {
  const flusher = new AsyncScoringConfigFlusher();
  flusher.addProjectScore(projectScore);
  await flusher.flush();
}

async function flushSingleProjectAutomation(projectAutomation: unknown) {
  const flusher = new AutomationFlusher();
  flusher.addProjectAutomation(projectAutomation);
  await flusher.flush();
}

function authorizeRequest(req: Request, next: NextFunction) {
  try {
    const ctx: RequestContext = {
      appOrigin: extractAllowedOrigin(
        req.headers[BRAINTRUST_APP_ORIGIN_HEADER]
          ? req.headers[BRAINTRUST_APP_ORIGIN_HEADER][
              req.headers[BRAINTRUST_APP_ORIGIN_HEADER].length - 1
            ]
          : req.headers[ORIGIN_HEADER],
      ),
      token: undefined,
      data: null,
      api_version: undefined,
    };

    // Extract token and data from request
    if (
      req.headers.authorization ||
      req.headers[BRAINTRUST_AUTH_TOKEN_HEADER]
    ) {
      const tokenText = parseBraintrustAuthHeader(req.headers);
      if (!tokenText) {
        throw new BadRequestError(
          "Invalid authorization token format. Please provide your API key in the following format: Bearer [your API key]",
        );
      }
      ctx.token = tokenText.toLowerCase() === "null" ? undefined : tokenText;
      if (req.method === "GET") {
        ctx.data = req.query;
      } else {
        ctx.data = req.body || {};
      }
    } else if (req.method === "GET") {
      const { token, ...data } = req.query;
      ctx.token = stringParam(token);
      ctx.data = data;
    } else {
      ctx.data = req.body.data || {};
      ctx.token =
        wrapZodError(() => z.string().nullish().parse(req.body.token)) ??
        undefined;
    }

    const impersonateUserId = req.headers[BT_IMPERSONATE_USER];
    if (ctx.token && impersonateUserId) {
      ctx.token = `${IMPERSONATE_TOKEN_PREFIX}${JSON.stringify({
        token: ctx.token,
        user_id: impersonateUserId,
      })}`;
    }

    const apiVersion = ctx.data.api_version;
    ctx.api_version = intParam(apiVersion);

    req.ctx = ctx;

    next(); // Proceed to next middleware/controller
  } catch (e) {
    next(e);
  }
}

function apiAuthorize(req: Request, res: Response, next: NextFunction) {
  bodyParser.json({ type: () => true, limit: "1gb" })(req, res, () =>
    authorizeRequest(req, next),
  );
}

function apiAuthorizeOtel(req: Request, res: Response, next: NextFunction) {
  const contentType = req.headers["content-type"];

  if (
    contentType !== "application/json" &&
    contentType !== "application/x-protobuf"
  ) {
    throw new BadRequestError(`Unsupported content type: ${contentType}`);
  }

  const nextFn = (err?: unknown) => {
    if (err) {
      next(err);
      return;
    }
    const ctx = getRequestContext(req);
    ctx.otelContentType = contentType;
    next();
  };

  switch (contentType) {
    case "application/json":
      apiAuthorize(req, res, nextFn);
      return;
    case "application/x-protobuf":
      bodyParser.raw({ type: contentType, limit: "1gb" })(req, res, () => {
        if (req.method !== "POST") {
          next(
            new BadRequestError("Protocol buffer data must be sent via POST"),
          );
        }
        authorizeRequest(req, nextFn);
      });
      return;
    default:
      const _exhaustiveCheck: never = contentType;
      throw new BadRequestError(
        `Impossible: got content type ${_exhaustiveCheck}`,
      );
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function middlewareWithExcludedPaths(exclusions: RegExp[], middleware: any) {
  return function (req: Request, res: Response, next: NextFunction) {
    if (exclusions.some((r) => r.test(req.path))) {
      return next();
    } else {
      return middleware(req, res, next);
    }
  };
}

async function checkAuthorized(
  ctx: RequestContext,
): Promise<{ me: Promise<Me> }> {
  return await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
  });
}

export const app = express();

// required by soc2 compliance
app.disable("x-powered-by");

app.use((req, res, next) => {
  const start = Date.now();
  const span = getCurrentSpan();
  if (span) {
    res.setHeader("x-bt-internal-trace-id", span.spanContext().traceId);
  }

  next();

  const duration = Date.now() - start;
  const msg = `request complete status=${res.statusCode} duration=${duration}ms ${req.method} ${req.path}`;
  logger.debug(msg);
});

dynamic_installProxyEndpoints({
  app,
  proxyServerPort: TS_API_PORT,
  proxyPrefix: "/v1/proxy",
});

app.use(
  // These should match the settings in api/app.py.
  cors({
    origin: checkOrigin,
    methods: ["GET", "PATCH", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: baseAllowedHeaders,
    credentials: true,
    exposedHeaders: [
      BT_CURSOR_HEADER,
      BT_FOUND_EXISTING_HEADER,
      BT_QUERY_PLAN_HEADER,
    ],
    maxAge: 86400,
  }),
);

app.get(`/`, async (_req, res) => {
  res.json({ hello: "world" });
});

app.get(`/version`, async (_req, res) => {
  res.json({
    version: VERSION,
    commit: GIT_COMMIT,
    deployment_mode: DEPLOYMENT_MODE,
    has_columnstore: !!CLICKHOUSE_PG_URL,
    brainstore_default: !!brainstoreDefault(),
    brainstore_can_contain_row_refs: canContainRowRefs(
      "project_logs",
      "foobar",
    ),
    has_logs2: canPgInsertLogs2("project_logs", "foobar"),
    js: true,
    universal: true,
    code_execution: canUseLambdaQuarantine() || ALLOW_CODE_FUNCTION_EXECUTION,
  });
});

app.get(`/version-${VERSION}`, async (_req, res) => {
  res.json({
    version: VERSION,
    has_columnstore: !!CLICKHOUSE_PG_URL,
  });
});

const COMPRESS_EXCLUSIONS = [/^\/btql$/, /^\/v1.*\/fetch$/];

app.use(
  middlewareWithExcludedPaths(
    COMPRESS_EXCLUSIONS,
    compression({
      threshold: 0,
    }),
  ),
);

async function pingHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  if (!ctx.token) {
    res.json({
      id: "80c48a10-4888-4382-a55b-255018e70fe5",
      email: "<EMAIL>",
      organizations: [],
    });
    return;
  }
  const headers = postDefaultHeaders({ token: ctx.token });
  return fetchToExpressResponse(
    await customFetchRequest(`${ctx.appOrigin}/api/self/me`, {
      method: "POST",
      headers,
      body: JSON.stringify({}),
    }),
    res,
  );
}

app.get(`/ping`, apiAuthorize, pingHandler);

app.get(`/xact-id`, apiAuthorize, async (req: Request, res: Response) => {
  await checkAuthorized(getRequestContext(req));
  res.send(await nextXactId());
});

async function broadcastKeyHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  res.json(await secretBroadcastKeyRequest(ctx.appOrigin, ctx.token, ctx.data));
}

app
  .route(`/broadcast-key`)
  .get(apiAuthorize, broadcastKeyHandler)
  .post(apiAuthorize, broadcastKeyHandler);

async function withEventPublisher<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  F extends (logToPublisher: LogToPublisherFn) => Promise<any>,
>(ctx: RequestContext, f: F): Promise<Awaited<ReturnType<F>>> {
  const publisher = new EventPublisher({
    appOrigin: ctx.appOrigin,
    token: ctx.token,
  });
  try {
    return await f((item) => publisher.log(item));
  } finally {
    await publisher.flush();
  }
}

async function logsHandlerBase({
  req,
  res,
  preCheck,
  getRows,
  getForceDisableLogs2,
  getOutput,
}: {
  req: Request;
  res: Response;
  preCheck?: (ctx: RequestContext) => void;
  getRows: (data: unknown) => unknown;
  getForceDisableLogs2?: (data: unknown) => boolean | null | undefined;
  getOutput: (ret: RunLogDataOutput) => unknown;
}) {
  const ctx = getRequestContext(req);
  preCheck?.(ctx);
  const ret = await withEventPublisher(ctx, (logToPublisher) => {
    const rows = getRows(ctx.data);
    const forceDisableLogs2 = getForceDisableLogs2?.(ctx.data);
    return runLogData({
      rows,
      appOrigin: ctx.appOrigin,
      token: ctx.token,
      resourceCheckWasCachedToken: getWasCachedToken(
        req,
        BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER,
      ),
      asyncScoringConfigWasCachedToken: getWasCachedToken(
        req,
        BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER,
      ),
      automationWasCachedToken: getWasCachedToken(
        req,
        BT_AUTOMATION_WAS_CACHED_REDIS_TOKEN_HEADER,
      ),
      logToPublisher,
      forceDisableLogs2,
    });
  });
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: Array.from(ret.objectsByType.values()).flatMap((obj) =>
        Object.values(obj),
      ),
    });
  }
  res.json(getOutput(ret));
}

app.post(`/logs`, apiAuthorize, async (req: Request, res: Response) =>
  logsHandlerBase({
    req,
    res,
    getRows: (data) => data,
    getOutput: (ret) => ret.ids,
  }),
);

app.post(`/logs2`, apiAuthorize, async (req: Request, res: Response) =>
  logsHandlerBase({
    req,
    res,
    getRows: (data) => data,
    getOutput: (ret) => ({ ids: ret.ids, xact_id: ret.xactId }),
  }),
);

app.post(`/logs3`, apiAuthorize, async (req: Request, res: Response) =>
  logsHandlerBase({
    req,
    res,
    preCheck: (ctx) => requireApiVersion(ctx),
    getRows: (data) =>
      wrapZodError(() => z.object({ rows: z.unknown() }).parse(data).rows),
    getForceDisableLogs2: (data) =>
      wrapZodError(
        () =>
          z.object({ force_disable_logs2: z.boolean().nullish() }).parse(data)
            .force_disable_logs2,
      ),
    getOutput: (ret) => ({
      ids: ret.ids,
      xact_id: ret.xactId,
    }),
  }),
);

async function controlPlaneProxyHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const headers = {
    ...(ctx.token ? { Authorization: `Bearer: ${ctx.token}` } : {}),
  };
  const resp = await (async () => {
    const reqPath = `${ctx.appOrigin}${req.url}`;
    if (req.method === "GET") {
      return customFetchRequest(reqPath, {
        method: "GET",
        headers,
      });
    } else if (req.method === "POST") {
      return customFetchRequest(reqPath, {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json",
        },
        body: ctx.data ? JSON.stringify(ctx.data) : undefined,
      });
    } else {
      throw new BadRequestError(`Unsupported method ${req.method}`);
    }
  })();

  return fetchToExpressResponse(resp, res, {
    postProcessSuccess: async (body) => {
      const pino = getLogger();
      const bodyObj = JSON.parse(body.toString());

      // For any API operations which modify ACLs (either directly or indirectly),
      // flush the corresponding object cache entries.
      async function flushObjectCache() {
        if (!bodyObj) {
          return;
        }

        const flusher = new AclFlusher();
        if (
          ["/api/acl/add_remove_multi", "/api/acl/batch_update"].includes(
            req.path,
          )
        ) {
          aclBatchUpdateObjects(bodyObj).forEach((x: unknown) =>
            flusher.addAclObject(x),
          );
        } else if (["/api/acl/delete_id"].includes(req.path)) {
          flusher.addAclObject(bodyObj);
        } else if (["/api/acl/register"].includes(req.path)) {
          flusher.addAclObject(bodyObj["acl"]);
        } else if (
          ["/api/group/patch_id", "/api/role/patch_id"].includes(req.path)
        ) {
          // Note that creating and deleting groups/roles should not affect ACLs
          // because the caller must have deleted any corresponding ACLs before
          // deleting a group/role, and creating a new one does not affect ACLs.
          const org_id = bodyObj["org_id"];
          if (org_id) {
            flusher.addEntry({
              org_id,
              object_type: "organization",
              object_id: org_id,
            });
          }
        } else if (["/api/experiment/patch_id"].includes(req.path)) {
          // Patching an experiment allows changing the public setting. We need to
          // fetch the object cache entry in order to get the org id to flush it.
          const experimentEntry = await OBJECT_CACHE.checkAndGet({
            appOrigin: ctx.appOrigin,
            authToken: ctx.token,
            aclObjectType: "experiment",
            overrideRestrictObjectType: undefined,
            objectId: bodyObj["id"],
          });
          flusher.addEntry({
            org_id: z.string().parse(objectCacheEntryOrgId(experimentEntry)),
            object_type: "experiment",
            object_id: bodyObj["id"],
          });
        }
        await flusher.flush();
      }

      // For any API operations which modify project scores, flush the
      // corresponding async score config cache entries.
      async function flushAsyncScoringConfigCache() {
        if (!bodyObj) {
          return;
        }
        if (
          [
            "/api/project_score/delete_id",
            "/api/project_score/patch_id",
          ].includes(req.path)
        ) {
          await flushSingleProjectScore(bodyObj);
        } else if (["/api/project_score/register"].includes(req.path)) {
          await flushSingleProjectScore(bodyObj["project_score"]);
        }
      }

      // For any API operations which modify automations, post-process the
      // automations.
      async function postProcessAutomations() {
        if (!bodyObj) {
          return;
        }
        if (
          [
            "/api/project_automation/delete_id",
            "/api/project_automation/patch_id",
          ].includes(req.path)
        ) {
          await flushSingleProjectAutomation(bodyObj);
        } else if (["/api/project_automation/register"].includes(req.path)) {
          await flushSingleProjectAutomation(bodyObj["project_automation"]);
        }

        if (["/api/project_automation/delete_id"].includes(req.path)) {
          await deleteAutomationCronJob(bodyObj["id"]);
        }
      }

      // Try to flush the caches, but don't fail the whole request if we fail,
      // since the actual work has already been done.
      try {
        await flushObjectCache();
      } catch (e) {
        pino.error({ error: e }, "Failed to flush object cache");
      }

      try {
        await flushAsyncScoringConfigCache();
      } catch (e) {
        pino.error({ error: e }, "Failed to flush async scoring config cache");
      }

      try {
        await postProcessAutomations();
      } catch (e) {
        pino.error({ error: e }, "Failed to flush automation cache");
      }
    },
  });
}

app
  .route("/api/:object_type")
  .get(apiAuthorize, controlPlaneProxyHandler)
  .post(apiAuthorize, controlPlaneProxyHandler);

app
  .route("/api/:object_type/:action")
  .get(apiAuthorize, controlPlaneProxyHandler)
  .post(apiAuthorize, controlPlaneProxyHandler);

app.get(`/proxy-url`, apiAuthorize, async (req: Request, res: Response) => {
  await checkAuthorized(getRequestContext(req));
  if (!PROXY_URL) {
    throw new BadRequestError("No proxy URL configured");
  }
  res.json({ url: PROXY_URL });
});

app.get(`/realtime-url`, apiAuthorize, async (req: Request, res: Response) => {
  await checkAuthorized(getRequestContext(req));
  if (!REALTIME_URL) {
    throw new BadRequestError("No realtime URL configured");
  }
  res.json({ url: REALTIME_URL });
});

app.get(`/db-health`, apiAuthorize, async (req: Request, res: Response) => {
  await checkAuthorized(getRequestContext(req));
  res.json({});
});

app.get(
  `/migration-version`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    await checkAuthorized(getRequestContext(req));
    res.json(await migrationVersion());
  },
);

app.post(
  `/repair-migration-version`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const { me } = await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
      checkSysadmin: true,
    });
    if (!(await me).is_sysadmin) {
      throw new AccessDeniedError({
        permission: "sysadmin",
        objectType: "migration_version",
        objectId: "repair",
      });
    }
    res.json(await repairMigrationTable());
  },
);

app.get(
  `/migration-status`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    await checkAuthorized(ctx);
    res.json(await migrationStatus(ctx.data));
  },
);

app.get(
  `/live-cron-jobs`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    await checkAuthorized(ctx);
    res.json(await liveCronJobs(ctx.data));
  },
);

app.get(
  `/clickhouse/etl-status`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    await checkAuthorized(getRequestContext(req));
    res.json(await etlStatus());
  },
);

app.get(
  `/clickhouse/run-etl`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    await checkAuthorized(ctx);
    const timeout = Number(ctx.data["timeout"] ?? 60);
    if (isNaN(timeout)) {
      throw new BadRequestError(`Invalid timeout ${timeout}`);
    }
    const batchSize = Number(ctx.data["batch_size"] ?? ETL_BATCH_SIZE);
    if (isNaN(batchSize)) {
      throw new BadRequestError(`Invalid batch_size ${batchSize}`);
    }
    res.json(await runEtlLoop({ timeoutS: timeout, batchSize }));
  },
);

app.get(`/brainstore/version`, async (_req: Request, res: Response) => {
  assertBrainstoreEnabled();
  const result = await runBrainstore({
    path: "/version",
    args: null,
    schema: z.object({ commit: z.string() }),
    method: "GET",
  });
  res.json(result);
});

app.get(
  `/brainstore/status`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    assertBrainstoreEnabled();
    const ctx = getRequestContext(req);
    const { me } = await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
      checkSysadmin: true,
    });
    if (!(await me).is_sysadmin) {
      throw new AccessDeniedError({
        permission: "sysadmin",
        objectType: "brainstore",
        objectId: "status",
      });
    }
    const isWrite = ctx.data.writer ?? false;
    res.json(
      await runBrainstore({
        path: "/status",
        args: undefined,
        schema: brainstoreSystemStatus,
        method: "GET",
        isWrite,
      }),
    );
  },
);

app.get(
  `/brainstore/backfill/status`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    assertBrainstoreEnabled();
    await checkAuthorized(getRequestContext(req));
    res.json(await getGlobalBackfillStatus());
  },
);

app.get(
  `/brainstore/backfill/status/active`,
  apiAuthorize,
  getActiveBackfillOperationsRequest,
);

app.get(
  `/brainstore/backfill/status/failed`,
  apiAuthorize,
  getFailedSegmentsRequest,
);

app.get(
  `/brainstore/backfill/status/project/:project_id`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    assertBrainstoreEnabled();
    // Check that the user has "write" permissions on the project
    const ctx = getRequestContext(req);
    const permissions = await OBJECT_CACHE.checkAndGet({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      aclObjectType: "project",
      objectId: req.params.project_id,
      overrideRestrictObjectType: undefined,
      wasCachedToken: getWasCachedToken(
        req,
        BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
      ),
      allowSysadminRoles: getSysadminRoles(),
    });
    if (!permissions.is_allowed_sysadmin) {
      for (const perm of ["update", "delete", "read"] as const) {
        if (!permissions.permissions.includes(perm)) {
          throw new AccessDeniedError({
            permission: perm,
            objectType: "project",
            objectId: req.params.project_id,
          });
        }
      }
    }

    res.json(
      await getProjectBackfillStatus({
        projectId: req.params.project_id,
      }),
    );
  },
);
app.get(
  `/brainstore/backfill/status/object/:object_id`,
  apiAuthorize,
  getObjectInfoRequest,
);
app.get(`/brainstore/segment/:segment_id`, apiAuthorize, getSegmentInfoRequest);

app.post(`/brainstore/backfill/track`, apiAuthorize, trackObjectsRequest);
app.post(`/brainstore/backfill/enable`, apiAuthorize, enableTrackingRequest);
app.post(`/brainstore/backfill/delete`, apiAuthorize, deleteTrackingRequest);
app.post(`/brainstore/backfill/optimize`, apiAuthorize, optimizeObjectRequest);

app.post(
  `/brainstore/backfill/run`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    assertBrainstoreEnabled();
    await checkAuthorized(getRequestContext(req));
    const ctx = getRequestContext(req);
    const params = wrapZodError(() =>
      brainstoreBackfillRunRequest.parse(ctx.data),
    );
    res.json(await runBrainstoreEtlLoopRequest(params));
  },
);

app.get(
  `/brainstore/backfill/metrics`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    assertBrainstoreEnabled();
    const ctx = getRequestContext(req);
    const { me } = await checkTokenAuthorized({
      ctxToken: ctx.token,
      appOrigin: ctx.appOrigin,
      checkSysadmin: true,
    });
    if (!(await me).is_sysadmin) {
      throw new AccessDeniedError({
        permission: "sysadmin",
        objectType: "brainstore",
        objectId: "status",
      });
    }
    const conn = await getPG().connect();
    const { rows } = await conn.query(
      "select count(*) cnt from brainstore_backfill_tracked_objects where org_id is null",
    );
    const null_org_ids_count = z.object({ cnt: z.number() }).parse(rows[0]);
    res.json({
      null_org_ids_count,
    });
  },
);

app.post(`/brainstore/vacuum/status`, apiAuthorize, getVacuumStatusRequest);

app.post(
  `/brainstore/vacuum/reset_state`,
  apiAuthorize,
  resetVacuumStateRequest,
);

app.post(
  `/brainstore/vacuum/object/:object_id`,
  apiAuthorize,
  vacuumObjectRequest,
);

app.post(
  `/flush-object-cache`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const { me: mePromise } = await checkAuthorized(ctx);
    const me = await mePromise;
    await flushObjectCache(me, ctx.data);
    res.json({});
  },
);

app.post(
  `/flush-org-object-cache`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const { me: mePromise } = await checkAuthorized(ctx);
    const me = await mePromise;
    await flushOrgObjectCache(me, ctx.data);
    res.json({});
  },
);

app.post(
  `/flush-async-scoring-cache`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    await flushAsyncScoringCache({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      ctxData: ctx.data,
    });
    res.json({});
  },
);

app.post(
  `/flush-automation-cache`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    await flushAutomationCache({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      ctxData: ctx.data,
    });
    res.json({});
  },
);

app.post(`/flush-summary-cache`, apiAuthorize, flushSummaryCacheRequest);

app.post(
  `/test-automation`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const result = await testAutomation(ctx);
    res.json(result);
  },
);

app.get(
  `/automation/cloud-identity`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const result = await getCloudIdentity();
    res.json(result);
  },
);

app.post(
  `/automation/cron`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    await registerCronJobRequest(req, res);
  },
);

app.post(
  `/automation/cron-force-run`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    forceCronLoopRun();
    res.json({ success: true });
  },
);

app.get(
  `/automation/cron/:id/status`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const { id } = req.params;
    await getCronJobStatusRequest(req, res, id);
  },
);

app.post(
  `/automation/cron/:id/run`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const { id } = req.params;
    await runCronJobRequest(req, res, id);
  },
);

app.post(
  `/automation/cron/:id/reset`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const { id } = req.params;
    await resetCronJobRequest(req, res, id);
  },
);

app.get(
  `/crud/base_experiments`,
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const params = wrapZodError(() =>
      z.object({ id: z.string() }).parse(ctx.data),
    );
    const headers = postDefaultHeaders({ token: ctx.token });
    const resp = await customFetchRequest(
      `${ctx.appOrigin}/api/base_experiment/get_id`,
      {
        method: "POST",
        headers,
        body: JSON.stringify({ id: params.id }),
      },
    );
    if (!resp.ok) {
      return fetchToExpressResponse(resp, res);
    } else {
      res.json([await resp.body.json()]);
    }
  },
);

async function btqlHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  await runBtqlRequest({
    req,
    res,
    ctxData: ctx.data,
    appOrigin: ctx.appOrigin,
    ctxToken: ctx.token,
    acceptsEncodings: req.acceptsEncodings.bind(req),
    auditHeaders: parseUrlBool(
      parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
    ),
  });
}

app.post("/btql", apiAuthorize, btqlHandler);

app.all(`/experiment-comparison2`, apiAuthorize, runExperimentSummarizeRequest);
app.all(`/dataset-summary`, apiAuthorize, runDatasetSummarizeRequest);

// The following endpoints are internal for debugging purposes
// app.get(`/billing/status`, apiAuthorize, getBillingStatusRequest);
// app.post(`/billing/refresh`, apiAuthorize, invalidateTelemetryCacheRequest);

app.post(
  "/insert-functions",
  apiAuthorize,
  async (req: Request, res: Response) => {
    const ctx = getRequestContext(req);
    const result = await withEventPublisher(ctx, (logToPublisher) =>
      runInsertFunctions({
        rows: ctx.data,
        appOrigin: ctx.appOrigin,
        token: ctx.token,
        logToPublisher,
      }),
    );
    if (result.status === "error") {
      if (typeof result.resp === "string") {
        res.status(400).send(result.resp);
      } else {
        return fetchToExpressResponse(result.resp, res);
      }
    } else {
      res.json({ status: "success" });
    }
  },
);

app.post("/attachment", apiAuthorize, runUploadAttachmentRequest);
app.get("/attachment", apiAuthorize, runGetAttachmentRequest);
app.post("/attachment/status", apiAuthorize, runPutAttachmentStatusRequest);

app.get(
  "/function-env-decrypt/:id",
  apiAuthorize,
  async (req: Request, res: Response) => {
    const { id } = req.params;

    const secrets = await loadFunctionSecrets({
      ids: [id],
    });
    if (secrets.length === 0) {
      throw new AccessDeniedError({
        permission: "owner",
        objectId: id,
        objectType: "env_var",
      });
    } else if (secrets.length > 1) {
      throw new InternalServerError(`Invalid id`);
    }
    const secret = secrets[0];
    const aclObjectType =
      secret.object_type === "function" ? "prompt" : secret.object_type;
    const overrideRestrictObjectType = undefined;

    const ctx = getRequestContext(req);
    const permissions = await OBJECT_CACHE.checkAndGet({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      aclObjectType,
      overrideRestrictObjectType,
      objectId: secret.object_id,
      wasCachedToken: getWasCachedToken(
        req,
        BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
      ),
    });
    for (const permission of permissionEnum.options) {
      if (!permissions.permissions.includes(permission)) {
        throw new AccessDeniedError({
          permission,
          aclObjectType,
          overrideRestrictObjectType,
          objectId: secret.object_id,
        });
      }
    }

    const decryptedValue = Object.values(await decryptSecrets([secret]))[0];
    const envVar: EnvVar & { value: string } = {
      id: secret.id,
      name: secret.name,
      object_type: secret.object_type,
      object_id: secret.object_id,
      created: secret.created,
      used: secret.used,
      value: decryptedValue,
    };

    res.status(200).json(envVar);
  },
);

const otelProtoLoader = new OtelProtoLoader();

async function spanComponentsToAclObject({
  parent,
  appOrigin,
  token,
}: {
  parent: SpanComponentsV3;
  appOrigin: string;
  token: string | undefined;
}): Promise<{
  aclObjectType: "project_log" | "experiment" | "prompt_session";
  objectId: string;
}> {
  const aclObjectType =
    parent.data.object_type === SpanObjectTypeV3.EXPERIMENT
      ? "experiment"
      : parent.data.object_type === SpanObjectTypeV3.PLAYGROUND_LOGS
        ? "prompt_session"
        : "project_log";

  const objectId = parent.data.object_id;
  if (objectId) {
    return {
      aclObjectType,
      objectId,
    };
  }

  // If no parent objectId was specified, we need to resolve it from the
  // object metadata instead. This is only supported for projects.
  if (parent.data.object_type === SpanObjectTypeV3.EXPERIMENT) {
    throw new BadRequestError(
      "Must specify experiment_id when providing a parent experiment",
    );
  }

  // TODO(austin): Cache project name lookups in `object_cache.ts` so that we
  // don't have to make a separate request to the webapp for every OTEL trace
  // that specifies an object name or metadata instead of an ID.
  const state = await cachedLogin({
    appOrigin,
    token,
    orgName: undefined,
    setHeader: () => {},
  });
  return {
    aclObjectType,
    objectId: await spanComponentsToObjectId({
      components: parent,
      state,
    }),
  };
}

async function otelTraceHandler(req: Request, res: Response) {
  const pino = getLogger();
  const body = req.body;

  const ctx = getRequestContext(req);
  if (!ctx.otelContentType) {
    throw new BadRequestError("No OTEL content type specified");
  }

  // parse the trace from the request body.
  let trace: unknown;
  if (ctx.otelContentType === "application/x-protobuf") {
    if (!body || !Buffer.isBuffer(body)) {
      throw new BadRequestError("Invalid body (expected buffer)");
    }
    try {
      trace = await otelProtoLoader.decodeExportTraceServiceRequest(body);
    } catch (e) {
      pino.error({ error: e }, "Failed to decode OTEL body");
      throw new BadRequestError("Failed to decode OTEL body");
    }
  } else if (ctx.otelContentType === "application/json") {
    trace = req?.ctx?.data;
  } else {
    throw new BadRequestError(
      `Impossible: got content type ${ctx.otelContentType}`,
    );
  }

  // Parse the trace into rows grouped by parent.
  const rawParent = req.headers[BT_PARENT];
  const defaultParent =
    rawParent && typeof rawParent === "string" ? rawParent : undefined;

  const { rowsByParent, rejectedSpans } = otelTraceToRows(trace, defaultParent);
  if (rejectedSpans.length) {
    pino.warn({ rejectedSpans }, `Rejected ${rejectedSpans.length} spans`);
  }

  // Check if we have any spans to process
  if (rowsByParent.size === 0) {
    // FIXME[matt] show a better error message here
    throw new BadRequestError(
      `No valid spans in this request: ${rejectedSpans.length} rejected, ${rowsByParent.size} valid`,
    );
  }

  const allRejectedSpans: RejectedSpan[] = [...rejectedSpans];
  let allRows: Row[] = [];

  // Process each parent group
  for (const [parentStr, rows] of rowsByParent.entries()) {
    if (!parentStr) {
      continue; // should never happen
    }

    let parent: SpanComponentsV3;
    try {
      parent = resolveParentHeader(parentStr);
    } catch (e) {
      pino.warn(
        { parentStr, error: e },
        "Unable to resolve parent header, skipping group",
      );
      // Add these spans to rejected spans
      for (const row of rows) {
        allRejectedSpans.push({
          traceId: row.root_span_id || "unknown",
          spanId: row.span_id || "unknown",
          error: `Unable to resolve parent header: ${e}`,
        });
      }
      continue;
    }

    const { aclObjectType, objectId } = await spanComponentsToAclObject({
      parent,
      appOrigin: ctx.appOrigin,
      token: ctx.token,
    });
    const overrideRestrictObjectType = undefined;
    const aclCheck = await OBJECT_CACHE.checkAndGet({
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
      aclObjectType,
      objectId,
      overrideRestrictObjectType,
    });
    if (!aclCheck.permissions.includes("update")) {
      pino.warn(
        { parentStr, aclObjectType, objectId },
        "Access denied for parent, skipping group",
      );
      // Add these spans to rejected spans
      for (const row of rows) {
        allRejectedSpans.push({
          traceId: row.root_span_id || "unknown",
          spanId: row.span_id || "unknown",
          error: `Access denied for parent ${parentStr}`,
        });
      }
      continue;
    }

    const parentWithObjectId = new SpanComponentsV3({
      ...parent.data,
      object_id: objectId,
      compute_object_metadata_args: undefined,
    });

    // Add parent information to each row
    const rowsWithParent: Row[] = rows.map((unresolvedRow) => {
      // Recreate the original parentSpanId logic:
      // If this OTEL span has a parent span ID, use that; otherwise use the Braintrust parent's span_id
      const parentSpanId = unresolvedRow.span_parents?.length
        ? unresolvedRow.span_parents[0]
        : parent.data.span_id;

      return {
        ...unresolvedRow,
        ...parentWithObjectId.objectIdFields(),
        // If we are assigning a Braintrust parent span to this trace, use the parent
        // span's root_span_id as the trace's root_span_id. Otherwise, keep the OTEL trace ID.
        root_span_id: parent.data.root_span_id || unresolvedRow.root_span_id,
        // Set span_parents array with the computed parentSpanId
        span_parents: parentSpanId ? [parentSpanId] : [],
      };
    });

    allRows = allRows.concat(rowsWithParent);
  }

  // If we have spans but no valid rows due to ACL failures, return an error
  if (rowsByParent.size > 0 && allRows.length === 0) {
    throw new AccessDeniedError({
      permission: "update",
      objectType: "spans",
      objectId: "trace",
    });
  }

  // Write all valid rows
  if (allRows.length > 0) {
    await withEventPublisher(ctx, (logToPublisher) => {
      return runLogData({
        rows: allRows,
        appOrigin: ctx.appOrigin,
        token: ctx.token,
        resourceCheckWasCachedToken: getWasCachedToken(
          req,
          BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER,
        ),
        asyncScoringConfigWasCachedToken: getWasCachedToken(
          req,
          BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER,
        ),
        logToPublisher,
      });
    });
  }

  const response = makeExportTraceServiceResponse(allRejectedSpans);

  // Respond using the same content type as the request.
  res.setHeader("Content-Type", ctx.otelContentType);
  switch (ctx.otelContentType) {
    case "application/x-protobuf":
      const encoded =
        await otelProtoLoader.encodeExportTraceServiceResponse(response);
      return res.status(200).send(encoded);
    case "application/json":
      return res.status(200).json(response);
    default:
      const _exhaustiveCheck: never = ctx.otelContentType;
      throw new BadRequestError(
        `Impossible: got content type ${_exhaustiveCheck}`,
      );
  }
}

// Begin: Rest API v1

async function fetchToExpressResponse(
  fetchResponse: CustomFetchResponse,
  res: Response,
  opts?: {
    postProcessSuccess?: (body: Buffer) => Promise<void>;
  },
) {
  if (fetchResponse.ok) {
    res.set("Content-Type", "application/json");
  } else {
    res.set("Content-Type", "text/plain");
  }
  const statusCode = fetchResponse.statusCode;
  const respBody = Buffer.from(await fetchResponse.body.arrayBuffer());
  if (fetchResponse.ok && opts?.postProcessSuccess) {
    await opts.postProcessSuccess(respBody);
  }
  res.status(statusCode);
  res.send(respBody);
}

async function objectCacheEntriesFromRows({
  appOrigin,
  authToken,
  rows,
}: {
  appOrigin: string;
  authToken?: string;
  rows: Record<string, unknown>[];
}): Promise<ObjectCacheEntry[]> {
  const idsByType = new Map<
    string,
    FullAclObjectType & { objectIds: string[] }
  >();
  for (const row of rows) {
    const objectIds = objectIdsUnionSchema.parse(row);
    const objectId = getAclObjectId(objectIds);
    const ot = objectTypeToAclObjectType(objectIds[OBJECT_TYPE_FIELD]);
    const v = mapSetDefault(
      idsByType,
      JSON.stringify(ot, deterministicReplacer),
      {
        ...ot,
        objectIds: [],
      },
    );
    v.objectIds.push(objectId);
  }

  const promises: Promise<ObjectCacheEntry[]>[] = [];
  for (const [
    _fullType,
    { aclObjectType, overrideRestrictObjectType, objectIds },
  ] of idsByType) {
    promises.push(
      (async () => {
        return Object.values(
          await OBJECT_CACHE.checkAndGetMulti({
            appOrigin,
            authToken,
            aclObjectType,
            overrideRestrictObjectType,
            objectIds,
          }),
        );
      })(),
    );
  }
  return (await Promise.all(promises)).flat();
}

async function v1PromptFunctionReadHandler(
  req: Request,
  res: Response,
  objectType: PromptFunctionObjectType,
) {
  if (req.method !== "GET") {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);
  const result = await retrievePrompts({
    params: ctx.data,
    objectType,
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    wasCachedToken: getWasCachedToken(
      req,
      BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  if (result.status === "error") {
    return fetchToExpressResponse(result.resp, res);
  }
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: await objectCacheEntriesFromRows({
        appOrigin: ctx.appOrigin,
        authToken: ctx.token,
        rows: result.rows,
      }),
    });
  }
  res.json({ objects: result.rows });
}

async function v1PromptReadHandler(req: Request, res: Response) {
  return v1PromptFunctionReadHandler(req, res, "project_prompts");
}

async function v1FunctionReadHandler(req: Request, res: Response) {
  return v1PromptFunctionReadHandler(req, res, "project_functions");
}

const PROMPT_METADATA_OBJECT_TYPE = "prompt";

async function v1PromptFunctionWriteHandler(req: Request, res: Response) {
  if (!(req.method === "POST" || req.method === "PUT")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  // Adapted from the v1ObjectType handler.
  const update = req.method !== "POST";

  // Because this handler makes requests to the control plane and to runLogData,
  // is difficult to "blindly" forward arguments to one or the other handler. We
  // pick the "conservative" option of explicitly selecting out the arguments we
  // send to the control plane, so that we are careful about not sending
  // sensitive data.
  //
  // But note that adding arguments to the control plane will require updating
  // the data plane.
  const { project_id, slug, org_name, ...opaqueData } = wrapZodError(() =>
    z
      .object({
        project_id: z.string(),
        slug: z.string().min(1),
        org_name: z.string().nullish(),
        name: z.string().min(1),
      })
      .passthrough()
      .parse(ctx.data),
  );

  const headers = postDefaultHeaders({ token: ctx.token });
  const resp = await customFetchRequest(
    `${ctx.appOrigin}/api/${PROMPT_METADATA_OBJECT_TYPE}/register`,
    {
      method: "POST",
      headers,
      body: JSON.stringify({ project_id, slug, org_name, update }),
    },
  );
  if (!resp.ok) {
    return fetchToExpressResponse(resp, res);
  }

  const respBody = await resp.body.json();
  const promptMetadata = stripPromptMetadata(
    wrapZodError(
      () =>
        z
          .object({
            [PROMPT_METADATA_OBJECT_TYPE]: z.record(z.unknown()),
          })
          .parse(respBody)[PROMPT_METADATA_OBJECT_TYPE],
    ),
  );
  const logData = {
    log_id: promptLogIdLiteralSchema.value,
    project_id,
    slug,
    ...promptMetadata,
    ...opaqueData,
  };
  const { insertedRows: insertedRowsRaw, objectsByType } =
    await withEventPublisher(ctx, (logToPublisher) =>
      runLogData({
        rows: [logData],
        appOrigin: ctx.appOrigin,
        token: ctx.token,
        logToPublisher,
      }),
    );
  const insertedRows = sanitizeInsertedPromptRows(
    insertedRowsRaw.map(({ fullRowData }) => fullRowData),
  );
  if (insertedRows.length !== 1) {
    throw new Error("Expected exactly one row to be inserted");
  }
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: Array.from(objectsByType.values()).flatMap((obj) =>
        Object.values(obj),
      ),
    });
  }
  res.json(insertedRows[0]);
}

async function v1PromptFunctionIdReadHandler(
  req: Request,
  res: Response,
  objectType: PromptFunctionObjectType,
) {
  if (!(req.method === "GET")) {
    throw new Error("Impossible");
  }

  const ctx = getRequestContext(req);
  const retrievePromptParams =
    objectType === "project_prompts"
      ? { prompt_id: req.params.id, version: req.query.version }
      : { function_id: req.params.id, version: req.query.version };

  const result = await retrievePrompts({
    params: retrievePromptParams,
    objectType,
    appOrigin: ctx.appOrigin,
    token: ctx.token,
    wasCachedToken: getWasCachedToken(
      req,
      BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });

  if (result.status === "error") {
    return fetchToExpressResponse(result.resp, res);
  } else if (result.rows.length === 0) {
    throw new BadRequestError(
      `${objectType === "project_prompts" ? "Prompt" : "Function"} does not exist or you do not have access`,
    );
  } else if (result.rows.length !== 1) {
    throw new BadRequestError(
      `More than one ${objectType === "project_prompts" ? "prompt" : "function"} matched`,
    );
  }
  const retrievedPrompt = z.record(z.unknown()).parse(result.rows[0]);
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: await objectCacheEntriesFromRows({
        appOrigin: ctx.appOrigin,
        authToken: ctx.token,
        rows: result.rows,
      }),
    });
  }
  res.json(retrievedPrompt);
}

async function v1PromptIdReadHandler(req: Request, res: Response) {
  return v1PromptFunctionIdReadHandler(req, res, "project_prompts");
}

async function v1FunctionIdReadHandler(req: Request, res: Response) {
  return v1PromptFunctionIdReadHandler(req, res, "project_functions");
}

async function v1PromptFunctionIdWriteHandler(req: Request, res: Response) {
  if (!(req.method === "DELETE" || req.method === "PATCH")) {
    throw new Error("Impossible");
  }
  const ctx = getRequestContext(req);

  const ctxDataRaw = wrapZodError(() =>
    z.record(z.unknown()).nullish().parse(ctx.data),
  );

  const { slug, ...ctxData } = z
    .object({
      slug: z.string().nullish(),
    })
    .passthrough()
    .parse(ctxDataRaw ?? {});

  // Similar to v1PromptFunctionWriteHandler, we explicitly specify the set of
  // params we send to the control plane.
  const headers = postDefaultHeaders({ token: ctx.token });
  const resp = await customFetchRequest(
    `${ctx.appOrigin}/api/${PROMPT_METADATA_OBJECT_TYPE}/${req.method.toLowerCase()}_id`,
    {
      method: "POST",
      headers,
      body: JSON.stringify({ id: req.params.id, slug }),
    },
  );
  if (!resp.ok) {
    return fetchToExpressResponse(resp, res);
  }

  const respBody = await resp.body.json();
  const promptMetadata = stripPromptMetadata(
    wrapZodError(() => z.record(z.unknown()).parse(respBody)),
  );
  const logData: Record<string, unknown> = {
    id: req.params.id,
    log_id: promptLogIdLiteralSchema.value,
    ...promptMetadata,
    ...ctxData,
  };

  if (req.method === "DELETE") {
    logData[OBJECT_DELETE_FIELD] = true;
  } else if (req.method === "PATCH") {
    logData[IS_MERGE_FIELD] = true;
    logData[MERGE_PATHS_FIELD] = Object.keys(ctxData ?? {}).map((p) => [p]);
  }
  const { insertedRows: insertedRowsRaw, objectsByType } =
    await withEventPublisher(ctx, (logToPublisher) =>
      runLogData({
        rows: [logData],
        appOrigin: ctx.appOrigin,
        token: ctx.token,
        logToPublisher,
      }),
    );
  const insertedRows = sanitizeInsertedPromptRows(
    insertedRowsRaw.map(({ fullRowData }) => fullRowData),
  );
  if (insertedRows.length !== 1) {
    throw new Error("Expected exactly one row to be inserted");
  }
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: Array.from(objectsByType.values()).flatMap((obj) =>
        Object.values(obj),
      ),
    });
  }
  res.json(insertedRows[0]);
}

async function v1AclBatchUpdateHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  if (!["POST"].includes(req.method)) {
    throw new Error("Impossible");
  }
  const headers = postDefaultHeaders({ token: ctx.token });
  const fetchResponse = await customFetchRequest(
    `${ctx.appOrigin}/api/acl/batch_update`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(ctx.data),
    },
  );
  return fetchToExpressResponse(fetchResponse, res, {
    postProcessSuccess: async (body) => {
      const bodyObj = JSON.parse(body.toString());
      const flusher = new AclFlusher();
      aclBatchUpdateObjects(bodyObj).forEach((x: unknown) =>
        flusher.addAclObject(x),
      );
      await flusher.flush();
    },
  });
}

async function v1AclListOrgHandler(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  if (!["GET"].includes(req.method)) {
    throw new Error("Impossible");
  }
  const headers = postDefaultHeaders({ token: ctx.token });
  const fetchResponse = await customFetchRequest(
    `${ctx.appOrigin}/api/acl/list_org`,
    {
      method: "GET",
      headers,
      body: JSON.stringify(ctx.data),
    },
  );
  if (!fetchResponse.ok) {
    return fetchToExpressResponse(fetchResponse, res);
  }
  const objects = await fetchResponse.body.json();
  await handleV1ObjectTypeGetAuditObjects({
    req,
    res,
    objectType: "acl",
    objects,
  });
  res.json({ objects });
}

function auditAclObjectType({
  objectType,
  object,
}: {
  objectType: string;
  object: unknown;
}):
  | {
      aclObjectType: AclObjectType;
      id: string;
    }
  | undefined {
  const objectSchema = z.object({ id: z.string() });
  const orgScopedSchema = z.object({
    org_id: z.string().nullish(),
  });
  const projectScopedSchema = z.object({
    project_id: z.string(),
  });
  const objectScopedSchema = z.object({
    object_type: aclObjectTypeEnum,
    object_id: z.string(),
  });
  const ot = objectTypes.parse(objectType);
  switch (ot) {
    case "project":
    case "experiment":
    case "dataset":
    case "prompt_session":
    case "role":
    case "group":
    case "organization":
      return {
        aclObjectType: ot,
        id: objectSchema.parse(object).id,
      };
    case "acl":
    case "view":
      const o = objectScopedSchema.parse(object);
      return {
        aclObjectType: o.object_type,
        id: o.object_id,
      };
    case "project_automation":
    case "project_score":
    case "project_tag":
    case "span_iframe":
      return {
        aclObjectType: "project",
        id: projectScopedSchema.parse(object).project_id,
      };
    case "api_key":
    case "ai_secret":
      const { org_id } = orgScopedSchema.parse(object);
      if (!org_id) {
        return undefined;
      }
      return {
        aclObjectType: "organization",
        id: org_id,
      };
    case "user":
      return undefined;
    default:
      throw new Error("Impossible. Should be handled by a different route.");
  }
}

async function handleV1ObjectTypeGetAuditObjects({
  req,
  res,
  objectType,
  objects,
}: {
  req: Request;
  res: Response;
  objectType: string;
  objects: unknown;
}) {
  const ctx = getRequestContext(req);
  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    const auditInfos = z
      .array(z.unknown())
      .parse(objects)
      .map((object) => auditAclObjectType({ objectType, object }))
      .filter((x) => x !== undefined);
    await setAuditHeaders({
      req,
      res,
      objects:
        auditInfos.length === 0
          ? []
          : Object.values(
              await OBJECT_CACHE.checkAndGetMulti({
                appOrigin: ctx.appOrigin,
                authToken: ctx.token,
                aclObjectType: auditInfos[0].aclObjectType,
                overrideRestrictObjectType: undefined,
                objectIds: [
                  ...new Set<string>(auditInfos.map((x) => x.id)).values(),
                ],
              }),
            ),
    });
  }
}

async function v1ObjectType(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  if (!["GET", "POST", "PUT", "DELETE"].includes(req.method)) {
    throw new Error("Impossible");
  }

  const objectType = req.params.object_type;
  const headers = postDefaultHeaders({ token: ctx.token });
  if (req.method === "GET") {
    const resp = await customFetchRequest(
      `${ctx.appOrigin}/api/${objectType}/get`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(ctx.data),
      },
    );
    if (!resp.ok) {
      return fetchToExpressResponse(resp, res);
    }
    let objects = await resp.body.json();
    if (objectType === "view") {
      objects = await hydrateViewDatas(objects);
    }
    await handleV1ObjectTypeGetAuditObjects({ req, res, objectType, objects });
    res.json({ objects });
  } else if (["POST", "PUT"].includes(req.method)) {
    // For experiments, 'update' means "return unmodified if already exists".
    // For other objects, 'update' means "replace with provided contents if
    // already exists". We make POST use `update=True` by default (unless
    // `ensure_new` is specified) and PUT behave the same as POST.
    //
    // For other objects, we make POST use `update=False` and PUT use
    // `update=True`.
    const { ensure_new, name, view_data, ...ctxData } = ctx.data;
    const update =
      req.params.object_type === "experiment"
        ? !ensure_new
        : req.method !== "POST";
    const jsonData = {
      ...ctxData,
      update,
      [`${req.params.object_type}_name`]: name,
    };
    if (objectType === "view") {
      const { object_type: aclObjectType, object_id: objectId } = ctxData;
      jsonData.view_data_id = (
        await insertViewData({
          appOrigin: ctx.appOrigin,
          authToken: ctx.token,
          aclObjectType,
          objectId,
          view_data,
        })
      ).view_data_id;
    }
    const resp = await customFetchRequest(
      `${ctx.appOrigin}/api/${req.params.object_type}/register`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(jsonData),
      },
    );
    if (!resp.ok) {
      return fetchToExpressResponse(resp, res);
    } else {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const fullResp = (await resp.body.json()) as any;
      if (fullResp.found_existing) {
        res.set(BT_FOUND_EXISTING_HEADER, "true");
      }

      // If we are creating an ACL, flush the corresponding object cache
      // entries.
      if (req.params.object_type === "acl" && fullResp?.acl) {
        await flushSingleAclObject(fullResp.acl);
      }

      // If we are creating a project score, flush the corresponding async
      // scoring config entries.
      if (
        req.params.object_type === "project_score" &&
        fullResp?.project_score
      ) {
        await Promise.all([
          flushSingleProjectScore(fullResp.project_score),
          flushSummaryCache({
            projectId: fullResp.project_score.project_id,
          }),
        ]);
      }

      if (req.params.object_type === "project") {
        await flushSummaryCache({ projectId: fullResp.project.id });
      }

      let objResp = fullResp[req.params.object_type];
      if (objectType === "view") {
        objResp = await hydrateViewData(objResp);
      }

      const audit = parseUrlBool(
        parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
      );
      if (audit) {
        const auditInfo = auditAclObjectType({ objectType, object: objResp });
        if (auditInfo) {
          await setAuditHeaders({
            req,
            res,
            objects: [
              await OBJECT_CACHE.checkAndGet({
                appOrigin: ctx.appOrigin,
                authToken: ctx.token,
                aclObjectType: auditInfo.aclObjectType,
                overrideRestrictObjectType: undefined,
                objectId: auditInfo.id,
              }),
            ],
          });
        }
      }
      res.json(objResp);
    }
  } else {
    const fetchResponse = await customFetchRequest(
      `${ctx.appOrigin}/api/${objectType}/${req.method.toLowerCase()}`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(ctx.data),
      },
    );
    return fetchToExpressResponse(fetchResponse, res, {
      postProcessSuccess: async (body) => {
        const isAclModificationOp =
          req.params.object_type === "acl" &&
          req.method.toLowerCase() === "delete";
        if (isAclModificationOp) {
          await flushSingleAclObject(JSON.parse(body.toString()));
        }
      },
    });
  }
}

async function v1OrganizationMembersPatch(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  if (!["PATCH"].includes(req.method)) {
    throw new Error("Impossible");
  }

  const headers = postDefaultHeaders({ token: ctx.token });
  const resp = await customFetchRequest(
    `${ctx.appOrigin}/api/organization/members`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(ctx.data),
    },
  );
  return fetchToExpressResponse(resp, res, {
    postProcessSuccess: async (body) => {
      const audit = parseUrlBool(
        parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
      );
      if (audit) {
        const { org_id: orgId } = patchOrganizationMembersOutputSchema.parse(
          JSON.parse(body.toString()),
        );
        // In case of self-removal from the org, we may no longer have access to
        // the object cache entry. In this case, we soft-fail by just using a
        // single AuditObject with the org_id.
        const auditObject = await (async (): Promise<AuditObject> => {
          try {
            return await OBJECT_CACHE.checkAndGet({
              appOrigin: ctx.appOrigin,
              authToken: ctx.token,
              aclObjectType: "organization",
              overrideRestrictObjectType: undefined,
              objectId: orgId,
            });
          } catch (e) {
            if (e instanceof ForbiddenError || e instanceof AccessDeniedError) {
              return {
                acl_object_type: "organization",
                object_id: orgId,
                object_name: "__unknown__",
                parent_cols: {},
              };
            } else {
              throw e;
            }
          }
        })();
        await setAuditHeaders({
          req,
          res,
          objects: [auditObject],
        });
      }
    },
  });
}

async function v1ObjectTypeId(req: Request, res: Response) {
  const pino = getLogger();
  const ctx = getRequestContext(req);

  const headers = postDefaultHeaders({ token: ctx.token });
  if (req.method === "GET" && req.params.object_type !== "acl") {
    const resp = await customFetchRequest(
      `${ctx.appOrigin}/api/${req.params.object_type}/get`,
      {
        method: "POST",
        headers,
        body: JSON.stringify({ ...ctx.data, ids: [req.params.id] }),
      },
    );
    if (!resp.ok) {
      return fetchToExpressResponse(resp, res);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    let objects = (await resp.body.json()) as any;
    if (objects.length === 0) {
      throw new AccessDeniedError({
        permission: "read",
        objectType: req.params.object_type,
        objectId: req.params.id,
      });
    }
    if (objects.length > 1) {
      pino.error(
        { objects, params: req.params },
        `Expected exactly one result from get request with params. Got multiple`,
      );
      throw new InternalServerError("Failed to get object by id");
    }
    if (req.params.object_type === "view") {
      objects = await hydrateViewDatas(objects);
    }
    const audit = parseUrlBool(
      parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
    );
    if (audit) {
      const auditInfo = auditAclObjectType({
        objectType: req.params.object_type,
        object: objects[0],
      });
      if (auditInfo) {
        await setAuditHeaders({
          req,
          res,
          objects: [
            await OBJECT_CACHE.checkAndGet({
              appOrigin: ctx.appOrigin,
              authToken: ctx.token,
              aclObjectType: auditInfo.aclObjectType,
              overrideRestrictObjectType: undefined,
              objectId: auditInfo.id,
            }),
          ],
        });
      }
    }
    res.json(objects[0]);
  } else {
    const { view_data, ...ctxData } = ctx.data;
    const jsonData = {
      ...ctxData,
      id: req.params.id,
    };
    let patchViewData: ViewData | null = null;
    if (
      req.params.object_type === "view" &&
      req.method === "PATCH" &&
      // If `view_data` is not provided in the PATCH request, we skip the write
      // to the api DB and also don't update the `view_data_id` in the app DB.
      view_data
    ) {
      const { object_type: aclObjectType, object_id: objectId } = ctxData;
      const viewStorage = await insertViewData({
        appOrigin: ctx.appOrigin,
        authToken: ctx.token,
        aclObjectType,
        objectId,
        view_data,
      });
      jsonData.view_data_id = viewStorage.view_data_id;
      patchViewData = viewStorage.view_data;
    }
    const fetchResponse = await customFetchRequest(
      `${ctx.appOrigin}/api/${
        req.params.object_type
      }/${req.method.toLowerCase()}_id`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(jsonData),
      },
    );

    // If we are deleting an ACL, patching a role/group, or patching an
    // experiment (which allows changing the public setting), flush the
    // corresponding object cache entries.
    const isAclModificationOp =
      (req.params.object_type === "acl" &&
        req.method.toLowerCase() === "delete") ||
      (["role", "group", "experiment"].includes(req.params.object_type) &&
        req.method.toLowerCase() === "patch");
    const isViewOp = req.params.object_type === "view";
    const isProjectModificationOp =
      req.params.object_type === "project" &&
      ["patch", "delete"].includes(req.method.toLowerCase());
    const isProjectAutomationModificationOp =
      req.params.object_type === "project_automation" &&
      ["patch", "delete"].includes(req.method.toLowerCase());
    const isProjectAutomationDeleteOp =
      req.params.object_type === "project_automation" &&
      req.method.toLowerCase() === "delete";
    const isProjectScoreModificationOp =
      req.params.object_type === "project_score" &&
      ["patch", "delete"].includes(req.method.toLowerCase());

    const getAuditObjects = async (auditInfo: {
      aclObjectType: AclObjectType;
      id: string;
    }): Promise<AuditObject[]> => {
      return [
        await OBJECT_CACHE.checkAndGet({
          appOrigin: ctx.appOrigin,
          authToken: ctx.token,
          aclObjectType: auditInfo.aclObjectType,
          overrideRestrictObjectType: undefined,
          objectId: auditInfo.id,
          includeDeletedObjects: req.method.toLowerCase() === "delete",
        }),
      ];
    };

    if (fetchResponse.ok && isViewOp) {
      const fullResp = z
        .record(z.unknown())
        .parse(await fetchResponse.body.json());
      const { view_data_id: _, ...rest } = fullResp;
      const hydratedView = patchViewData
        ? { ...rest, view_data: patchViewData }
        : await hydrateViewData(fullResp);
      const audit = parseUrlBool(
        parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
      );
      if (audit) {
        const auditInfo = auditAclObjectType({
          objectType: req.params.object_type,
          object: hydratedView,
        });
        if (auditInfo) {
          await setAuditHeaders({
            req,
            res,
            objects: await getAuditObjects(auditInfo),
          });
        }
      }
      res.json(hydratedView);
    } else {
      return fetchToExpressResponse(fetchResponse, res, {
        postProcessSuccess: async (body) => {
          const bodyObj = JSON.parse(body.toString());
          if (isAclModificationOp) {
            const flusher = new AclFlusher();
            if (req.params.object_type === "acl") {
              flusher.addAclObject(bodyObj);
            } else if (req.params.object_type === "experiment") {
              // We need to fetch the object cache entry in order to get the org id
              // to flush it.
              const objectId = z.string().parse(req.params.id);
              const experimentEntry = await OBJECT_CACHE.checkAndGet({
                appOrigin: ctx.appOrigin,
                authToken: z.string().nullish().parse(ctx.token) ?? undefined,
                aclObjectType: "experiment",
                overrideRestrictObjectType: undefined,
                objectId,
              });
              flusher.addEntry({
                org_id: objectCacheEntryOrgId(experimentEntry) ?? "",
                object_type: "experiment",
                object_id: objectId,
              });
            } else if (bodyObj.org_id) {
              const orgId = z.string().parse(bodyObj.org_id);
              flusher.addEntry({
                org_id: orgId,
                object_type: "organization",
                object_id: orgId,
              });
            }
            await flusher.flush();
          } else if (isProjectModificationOp) {
            await flushSummaryCache({ projectId: bodyObj.id });
          } else if (isProjectAutomationModificationOp) {
            await flushSingleProjectAutomation(bodyObj);
            if (isProjectAutomationDeleteOp) {
              await deleteAutomationCronJob(bodyObj.id);
            }
          } else if (isProjectScoreModificationOp) {
            await Promise.all([
              flushSingleProjectScore(bodyObj),
              flushSummaryCache({ projectId: bodyObj.project_id }),
            ]);
          }
          const audit = parseUrlBool(
            parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
          );
          if (audit) {
            const auditInfo = auditAclObjectType({
              objectType: req.params.object_type,
              object: bodyObj,
            });
            if (auditInfo) {
              await setAuditHeaders({
                req,
                res,
                objects: await getAuditObjects(auditInfo),
              });
            }
          }
        },
      });
    }
  }
}

async function v1ObjectTypeIdFetch(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  // Create a payload for the BTQL handler out of the fetch params.
  const fetchData = wrapZodError(() =>
    fetchEventsRequestSchema.parse(ctx.data),
  );
  if (
    brainstoreEnabled() &&
    (fetchData.max_xact_id || fetchData.max_root_span_id)
  ) {
    throw new BadRequestError(
      "Explicitly-constructed pagination cursor not supported when brainstore is enabled",
    );
  }
  const cursor = fetchData.cursor
    ? fetchData.cursor
    : fetchData.max_xact_id && fetchData.max_root_span_id
      ? formatCursor({
          max_xact_id: fetchData.max_xact_id,
          max_root_span_id: fetchData.max_root_span_id,
        })
      : undefined;
  const query: ParsedQuery = {
    select: [
      {
        op: "star",
      },
    ],
    from: {
      op: "function",
      name: {
        op: "ident",
        name: [req.params.object_type],
      },
      args: [
        {
          op: "literal",
          value: req.params.id,
        },
      ],
    },
    limit: fetchData.limit,
    cursor,
  };
  const ctxData = {
    query,
    fmt: "json",
    version: fetchData.version ?? undefined,
    // Once we add pagination to the SDK, we could consider changing this to a
    // user-supplied param. For now, we disable the limit for just the
    // fetch-able objects in the SDK.
    disable_limit: ["experiment", "dataset"].includes(req.params.object_type)
      ? true
      : undefined,
    // Brainstore is not supported for explicitly-constructed pagination keys.
    use_brainstore:
      cursor &&
      !fetchData.cursor &&
      fetchData.max_xact_id &&
      fetchData.max_root_span_id
        ? false
        : undefined,
  };

  await runBtqlRequest({
    req,
    res,
    ctxData,
    appOrigin: ctx.appOrigin,
    ctxToken: ctx.token,
    acceptsEncodings: req.acceptsEncodings.bind(req),
    isV1FetchRequest: true,
    objectCacheWasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
    auditHeaders: parseUrlBool(
      parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
    ),
  });
}

const summarizeExperimentParamsSchema = z.object({
  summarize_scores: summarizeScoresParamSchema.nullish(),
  comparison_experiment_id: comparisonExperimentIdParamSchema.nullish(),
});

const summarizeDatasetParamsSchema = z.object({
  summarize_data: summarizeDataParamSchema.nullish(),
});

async function v1ObjectTypeIdSummarize(req: Request, res: Response) {
  const ctx = getRequestContext(req);

  if (req.method !== "GET") {
    throw new BadRequestError(`Method ${req.method} not allowed`);
  }

  const { objectType, params } = (() => {
    const objectTypeParsed = objectTypeSchema.safeParse(req.params.object_type);
    if (
      !objectTypeParsed.success ||
      !["dataset", "experiment"].includes(objectTypeParsed.data)
    ) {
      throw new BadRequestError(
        `Summarize not supported for object type ${req.params.object_type}`,
      );
    }
    const objectType = objectTypeParsed.data;

    if (objectType === "experiment") {
      return {
        objectType,
        params: parseNoStrip(summarizeExperimentParamsSchema, ctx.data),
      };
    } else if (objectType === "dataset") {
      return {
        objectType,
        params: parseNoStrip(summarizeDatasetParamsSchema, ctx.data),
      };
    } else {
      throw new Error("Impossible");
    }
  })();

  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType(objectType);

  const entry = await OBJECT_CACHE.checkAndGet({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    aclObjectType,
    overrideRestrictObjectType,
    objectId: req.params.id,
  });

  if (!entry.permissions.includes("read")) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType,
      overrideRestrictObjectType,
      objectId: req.params.id,
    });
  }

  const objectName = entry.object_name;
  const project = entry.parent_cols.project;
  if (!project) {
    throw new BadRequestError(`No project for ${aclObjectType}`);
  }
  const org = entry.parent_cols.organization;
  if (!org) {
    throw new BadRequestError(`No org for ${aclObjectType}`);
  }
  const projectUrl = `${PUBLIC_ALLOWED_ORIGIN}/app/${encodeURIComponent(
    org.name,
  )}/p/${encodeURIComponent(project.name)}`;

  const objectUrlSeparator =
    objectType === "experiment" ? "/experiments/" : "/datasets/";
  const objectUrl = `${projectUrl}${objectUrlSeparator}${encodeURIComponent(
    objectName,
  )}`;

  const ret = {
    project_name: project.name,
    project_url: projectUrl,
    [`${objectType}_name`]: objectName,
    [`${objectType}_url`]: objectUrl,
  };

  const objectEntries = [entry];
  if (objectType === "experiment" && params.summarize_scores) {
    let comparisonExperimentId = params.comparison_experiment_id;
    let comparisonExperimentName: string | null = null;

    if (!comparisonExperimentId) {
      const resp = await customFetchRequest(
        `${ctx.appOrigin}/api/base_experiment/get_id`,
        {
          method: "POST",
          headers: postDefaultHeaders({ token: ctx.token }),
          body: JSON.stringify({ id: req.params.id }),
        },
      );
      if (resp.ok) {
        comparisonExperimentId = z
          .object({ base_exp_id: z.string() })
          .parse(await resp.body.json()).base_exp_id;
      } else if (resp.statusCode !== 400) {
        return fetchToExpressResponse(resp, res);
      }
    }

    if (comparisonExperimentId) {
      const comparisonEntry = await OBJECT_CACHE.checkAndGet({
        appOrigin: ctx.appOrigin,
        authToken: ctx.token,
        aclObjectType,
        overrideRestrictObjectType,
        objectId: comparisonExperimentId,
      });

      if (!comparisonEntry.permissions.includes("read")) {
        throw new AccessDeniedError({
          permission: "read",
          aclObjectType,
          overrideRestrictObjectType,
          objectId: comparisonExperimentId,
        });
      }

      objectEntries.push(comparisonEntry);
      comparisonExperimentName = comparisonEntry.object_name;
      ret.comparison_experiment_name = comparisonExperimentName;
    }

    ctx.data = {
      experiment_id: req.params.id,
      base_experiment_id: comparisonExperimentId,
    };
    Object.assign(
      ret,
      await runExperimentSummarize({
        experimentId: req.params.id,
        baseExperimentId: comparisonExperimentId ?? undefined,
        appOrigin: ctx.appOrigin,
        authToken: ctx.token,
        orgName: org.name,
        projectName: project.name,
        projectId: project.id,
      }),
    );
  } else if (objectType === "dataset" && params.summarize_data) {
    Object.assign(ret, {
      data_summary: await runDatasetSummarize({
        datasetId: req.params.id,
      }),
    });
  }

  const audit = parseUrlBool(
    parseHeader(req.headers, BT_ENABLE_AUDIT_HEADER) ?? "false",
  );
  if (audit) {
    await setAuditHeaders({
      req,
      res,
      objects: objectEntries,
    });
  }
  res.json(ret);
}

const v1InsertObjectTypeSchema = z.enum([
  "experiment",
  "dataset",
  "project_logs",
]);
type V1InsertObjectType = z.infer<typeof v1InsertObjectTypeSchema>;
type V1InsertObjectTypeIdInfo = {
  idField: string;
  extraIdFields?: Record<string, unknown>;
};

const v1InsertObjectTypeIdInfos: Record<
  V1InsertObjectType,
  V1InsertObjectTypeIdInfo
> = {
  experiment: {
    idField: "experiment_id",
  },
  dataset: {
    idField: "dataset_id",
  },
  project_logs: {
    idField: "project_id",
    extraIdFields: { log_id: "g" },
  },
};

function v1InsertAddObjectIds({
  rows,
  objectType: objectTypeRaw,
  id,
}: {
  rows: Record<string, unknown>[];
  objectType: string;
  id: string;
}): Record<string, unknown>[] {
  const objectType = wrapZodError(() =>
    v1InsertObjectTypeSchema.parse(objectTypeRaw),
  );
  const { idField, extraIdFields } = v1InsertObjectTypeIdInfos[objectType];
  const fieldsToAdd = { [idField]: id, ...extraIdFields };
  return rows.map((row) => {
    Object.keys(fieldsToAdd).forEach((field) => {
      if (row[field]) {
        throw new BadRequestError(
          `Input object may not contain field ${field}`,
        );
      }
    });
    return {
      ...row,
      ...fieldsToAdd,
    };
  });
}

async function v1ObjectTypeIdInsert(req: Request, res: Response) {
  function getRows(ctxData: unknown): unknown {
    const rowsRaw = wrapZodError(
      () =>
        z.object({ events: z.record(z.unknown()).array() }).parse(ctxData)
          .events,
    );
    return v1InsertAddObjectIds({
      rows: rowsRaw,
      objectType: req.params.object_type,
      id: req.params.id,
    });
  }
  await logsHandlerBase({
    req,
    res,
    getRows,
    getOutput: (x) => ({
      row_ids: x.ids,
    }),
  });
}

// Splits a feedback row into separate log events and comment events.
function v1SplitFeedbackRows(
  rows: Record<string, unknown>[],
): Record<string, unknown>[] {
  const ret: Record<string, unknown>[] = [];
  rows.forEach((row) => {
    const { id, scores, expected, comment, metadata, source, tags } =
      wrapZodError(() =>
        z
          .object({
            id: z.string(),
            scores: z.unknown().nullish(),
            expected: z.unknown().nullish(),
            comment: z.unknown().nullish(),
            metadata: z.unknown().nullish(),
            source: z.preprocess((x) => x ?? "external", z.string()),
            tags: z.unknown().nullish(),
          })
          .parse(row),
      );

    // Log the row update.
    if (![scores, expected].every(isEmpty)) {
      ret.push({
        id,
        scores,
        expected,
        tags,
        [AUDIT_SOURCE_FIELD]: source,
        [AUDIT_METADATA_FIELD]: metadata,
        [IS_MERGE_FIELD]: true,
      });
    }

    // Log the comment.
    if (!isEmpty(comment)) {
      ret.push({
        origin: {
          // NOTE: We do not know (or care?) what the transaction id of the row
          // that we're commenting on is here, so we omit it.
          id,
        },
        comment: {
          text: comment,
        },
        [AUDIT_SOURCE_FIELD]: source,
        [AUDIT_METADATA_FIELD]: metadata,
      });
    }
  });
  return ret;
}

async function v1ObjectTypeIdFeedback(req: Request, res: Response) {
  function getRows(ctxData: unknown): unknown {
    const rowsRaw = wrapZodError(
      () =>
        z.object({ feedback: z.record(z.unknown()).array() }).parse(ctxData)
          .feedback,
    );
    return v1InsertAddObjectIds({
      rows: v1SplitFeedbackRows(rowsRaw),
      objectType: req.params.object_type,
      id: req.params.id,
    });
  }
  await logsHandlerBase({
    req,
    res,
    getRows,
    getOutput: () => ({
      status: "success",
    }),
  });
}

const crossObjectInsertSchema = z.record(
  z.record(
    z.object({
      events: z.preprocess((x) => x ?? [], z.record(z.unknown()).array()),
      feedback: z.preprocess((x) => x ?? [], z.record(z.unknown()).array()),
    }),
  ),
);

async function v1CrossObjectInsert(req: Request, res: Response) {
  const origRowsMetadata: ({ objectType: string; objectId: string } | null)[] =
    [];

  function getRows(ctxData: unknown): unknown {
    const rows: Record<string, unknown>[] = [];
    const objectTypeToObjectIdToItems = crossObjectInsertSchema.parse(ctxData);
    Object.entries(objectTypeToObjectIdToItems).forEach(
      ([objectType, objectIdToItems]) => {
        Object.entries(objectIdToItems).forEach(([objectId, items]) => {
          const events = items.events;
          const feedback = v1SplitFeedbackRows(items.feedback);
          rows.push(
            ...v1InsertAddObjectIds({
              rows: events.concat(feedback),
              objectType,
              id: objectId,
            }),
          );
          origRowsMetadata.push(
            ...events.map(() => ({ objectType, objectId })),
            ...feedback.map(() => null),
          );
        });
      },
    );
    if (rows.length !== origRowsMetadata.length) {
      throw new Error("Impossible");
    }
    return rows;
  }

  function getOutput(ret: RunLogDataOutput): unknown {
    const { ids: origRowIds } = ret;
    if (origRowIds.length !== origRowsMetadata.length) {
      throw new Error("Impossible");
    }
    const output: Record<string, Record<string, { row_ids: string[] }>> = {};
    origRowIds.forEach((rowId, idx) => {
      const metadata = origRowsMetadata[idx];
      if (isEmpty(metadata)) {
        return;
      }
      const { objectType, objectId } = metadata;
      recordSetDefault(recordSetDefault(output, objectType, {}), objectId, {
        row_ids: [],
      }).row_ids.push(rowId);
    });
    return output;
  }

  await logsHandlerBase({ req, res, getRows, getOutput });
}

app.use("/v1", apiAuthorize);

app.get("/v1", async (_req, res) => {
  res.send("Hello, World!");
});
app.patch("/v1/organization/members", v1OrganizationMembersPatch);
app.post("/v1/insert", v1CrossObjectInsert);
app
  .route(`/v1/prompt`)
  .get(v1PromptReadHandler)
  .post(v1PromptFunctionWriteHandler)
  .put(v1PromptFunctionWriteHandler);
app
  .route(`/v1/function`)
  .get(v1FunctionReadHandler)
  .post(v1PromptFunctionWriteHandler)
  .put(v1PromptFunctionWriteHandler);
app
  .route(`/v1/env_var`)
  .get(v1EnvVarReadHandler)
  .post(v1EnvVarWriteHandler)
  .put(v1EnvVarWriteHandler);
app
  .route(`/v1/column`)
  .get(v1CustomColumnReadHandler)
  .post(v1CustomColumnWriteHandler)
  .put(v1CustomColumnWriteHandler);
app.route(`/v1/acl/batch_update`).post(v1AclBatchUpdateHandler);
app.route(`/v1/acl/list_org`).get(v1AclListOrgHandler);
app
  .route("/v1/:object_type")
  .get(v1ObjectType)
  .post(v1ObjectType)
  .put(v1ObjectType)
  .delete(v1ObjectType);
app
  .route(`/v1/prompt/:id`)
  .get(v1PromptIdReadHandler)
  .delete(v1PromptFunctionIdWriteHandler)
  .patch(v1PromptFunctionIdWriteHandler);
app
  .route(`/v1/function/:id`)
  .get(v1FunctionIdReadHandler)
  .delete(v1PromptFunctionIdWriteHandler)
  .patch(v1PromptFunctionIdWriteHandler);
app
  .route("/v1/env_var/:id")
  .get(v1EnvVarReadIdHandler)
  .delete(v1EnvVarIdWriteHandler)
  .patch(v1EnvVarIdWriteHandler);
app
  .route("/v1/column/:id")
  .delete(v1CustomColumnIdWriteHandler)
  .patch(v1CustomColumnIdWriteHandler);
app
  .route("/v1/:object_type/:id")
  .get(v1ObjectTypeId)
  .delete(v1ObjectTypeId)
  .patch(v1ObjectTypeId);
app
  .route("/v1/:object_type/:id/fetch")
  .get(v1ObjectTypeIdFetch)
  .post(v1ObjectTypeIdFetch);
app.route("/v1/:object_type/:id/summarize").get(v1ObjectTypeIdSummarize);
app.post("/v1/:object_type/:id/insert", apiAuthorize, v1ObjectTypeIdInsert);
app.post("/v1/:object_type/:id/feedback", v1ObjectTypeIdFeedback);

// End: Rest API v1

app.route("/otel/v1/traces").post(apiAuthorizeOtel, otelTraceHandler);

// This must be the last middleware and come after all routes.
// Also, this signature must be exactly as defined here, otherwise express
// won't recognize it as an error handler.
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
app.use(
  async (err: unknown, req: Request, res: Response, _next: NextFunction) => {
    const pino = getLogger();
    // Get the current span to record error information
    const currentSpan = getCurrentSpan();

    if (
      err instanceof QueryTooCostlyError ||
      err instanceof ResourceCheckException
    ) {
      // For these specific errors, we'll mark them as non-error status but add an event
      if (currentSpan) {
        currentSpan.addEvent("query_too_costly_or_resource_check", {
          error: err.message,
        });
      }
      return res
        .status(400)
        .json({ Code: "BadRequestError", Message: err.message });
    }

    const errorContext = await (async () => {
      if (!req.ctx) return EMPTY_ERROR_CONTEXT;
      const ctx = req.ctx;
      try {
        return await ERROR_CONTEXT_CACHE.getErrorContext({
          authToken: ctx.token,
          appOrigin: ctx.appOrigin,
        });
      } catch (err) {
        pino.error(
          { error: node_util.inspect(err, { depth: null }) },
          "Error details",
        );
        if (currentSpan) {
          currentSpan.setStatus({
            code: SpanStatusCode.ERROR,
            message: "Failed to get error context",
          });
          if (err instanceof Error) {
            currentSpan.recordException(err);
          }
        }
        return EMPTY_ERROR_CONTEXT;
      }
    })();

    // Get error message
    const errText = extractErrorText(err);
    const errMessage = errText
      ? augmentWithErrorContext(errText, errorContext)
      : "";

    // Record error information on the span
    if (currentSpan) {
      getTracer().startActiveSpan("error", (span) => {
        try {
          span.setAttribute(
            "error.type",
            err instanceof Error ? err.constructor.name : "UnknownError",
          );
          span.setAttribute("error.status_code", res.statusCode);
          for (const [key, value] of Object.entries(errorContext)) {
            if (value) {
              span.setAttribute(`error_context.${key}`, value);
            }
          }

          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: errMessage || "Request failed",
          });
          if (err instanceof Error) {
            span.recordException(err);
          }
        } finally {
          span.end();
        }
      });
    }

    const {
      statusCode,
      jsonData,
    }: { statusCode: number; jsonData: Record<string, unknown> } = (() => {
      // Handle specific error types
      if (
        (err instanceof HTTPError || err instanceof FailedHTTPResponse) &&
        err.status >= 400 &&
        err.status < 500
      ) {
        return {
          statusCode: err.status,
          jsonData: { Code: "HTTPError", Message: errMessage },
        };
      } else if (
        err instanceof AccessDeniedError ||
        err instanceof ForbiddenError
      ) {
        return {
          statusCode: 403,
          jsonData: { Code: "ForbiddenError", Message: errMessage },
        };
      } else if (
        err instanceof BadRequestError ||
        err instanceof OtelBadRequestError ||
        err instanceof ProxyBadRequestError
      ) {
        return {
          statusCode: 400,
          jsonData: { Code: "BadRequestError", Message: errMessage },
        };
      } else if (err instanceof NotFoundError) {
        return {
          statusCode: 404,
          jsonData: { Code: "NotFoundError", Message: errMessage },
        };
      } else if (err instanceof TooManyRequestsError) {
        res.setHeader("Retry-After", err.tryAfter.toString());
        return {
          statusCode: 429,
          jsonData: { Code: "TooManyRequestsError", Message: errMessage },
        };
      } else if (
        err instanceof InternalServerErrorOpenAI ||
        err instanceof InternalServerError
      ) {
        return {
          statusCode: 500,
          jsonData: { Code: "InternalServerError", Message: errMessage },
        };
      } else {
        return {
          statusCode: 500,
          jsonData: { Code: "InternalServerError" },
        };
      }
    })();

    if (currentSpan) {
      jsonData["InternalTraceId"] = currentSpan.spanContext().traceId;
      jsonData["Path"] = req.path;
      jsonData["Service"] = "api";
    }

    logRequestFailure({ logger: pino, req, statusCode, err, errorContext });
    res.status(statusCode).json(jsonData);
  },
);

// Define a separate server for returning system health information. This can be
// exposed in a more locked-down fashion than the primary server.

export const healthServerApp = express();

async function systemStatsHandler(_req: Request, res: Response) {
  const systemStats = {
    postgres: pgStats(),
  };
  res.json(systemStats);
}

healthServerApp.get("/system-stats", systemStatsHandler);

healthServerApp.get("/status", bodyParser.json(), async (req, res) => {
  const response = await runStatusCheck(req.body);
  res.json(response);
});

healthServerApp.use(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  (err: any, _req: Request, res: Response, _next: NextFunction) => {
    getLogger().error({ error: err }, "Error for debugging");
    res.status(500).json({ Code: "InternalServerError" });
  },
);

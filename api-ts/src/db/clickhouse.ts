import { CLICKHOUSE_CONNECT_URL, CLICKHOUSE_PG_URL } from "../env";
import { createClient } from "@clickhouse/client";
import { NodeClickHouseClient } from "@clickhouse/client/dist/client";
import { getPG } from "./pg";

let _clickhouse_conn: NodeClickHouseClient | null = null;
export function getClickhouse() {
  if (CLICKHOUSE_CONNECT_URL === undefined) {
    throw new Error("CLICKHOUSE_CONNECT_URL not set");
  }
  if (_clickhouse_conn === null) {
    _clickhouse_conn = createClient({
      url: CLICKHOUSE_CONNECT_URL,
      // The default is 30s, but this seems to be too short for ETL operations.
      request_timeout: 60000,
    });
  }
  return _clickhouse_conn;
}

export function getClickhousePG() {
  if (!CLICKHOUSE_PG_URL) {
    throw new Error("CLICKHOUSE_PG_URL not set");
  }
  return getPG({ url: CLICKHOUSE_PG_URL });
}

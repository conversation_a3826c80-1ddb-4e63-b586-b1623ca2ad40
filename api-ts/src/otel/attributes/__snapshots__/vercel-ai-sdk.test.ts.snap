// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`vercel-ai-sdk convertAttributesToSpan > braintrustFields 1`] = `
{
  "input": [
    {
      "content": "Tell me a story about a dragon and a knight.",
      "role": "user",
    },
  ],
  "metadata": {
    "characters": [
      "dragon",
      "knight",
    ],
    "foo": "bar",
    "model": "gpt-4o-mini",
    "story": "fantasy",
    "temperature": 0.8,
  },
  "metrics": {
    "bar": 43,
    "completion_tokens": 400,
    "foo": 12,
    "input_tokens": 100,
    "output_tokens": 400,
    "prompt_tokens": 100,
    "tokens": 500,
  },
  "output": [
    {
      "content": "The dragon and the knight faced off in a fierce battle, each determined to emerge victorious. The dragon breathed fire, but the knight's armor was strong. With a mighty swing of his sword, the knight struck the dragon down, ending the battle and saving the kingdom.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextBasic 1`] = `
{
  "input": [
    {
      "content": [
        {
          "text": "What is the weather typically like in Seattle? Give a one sentence response.",
          "type": "text",
        },
      ],
      "role": "user",
    },
  ],
  "metadata": {
    "foo": {
      "nested": [
        "zero",
        "one",
      ],
    },
    "model": "gpt-4o-mini",
    "temperature": 0.72,
    "top_p": 0.52,
  },
  "metrics": {},
  "output": [
    {
      "content": "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextDoGenerateMessages 1`] = `
{
  "input": [
    {
      "content": "What is the weather typically like in Seattle? Give a one sentence response.",
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "bar",
    "model": "gpt-4o-mini",
    "temperature": 0.72,
    "top_p": 0.52,
  },
  "metrics": {},
  "output": [
    {
      "content": "Seattle typically experiences a mild, maritime climate with cool, wet winters and warm, dry summers, characterized by frequent overcast skies and light rain throughout much of the year.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextDoGenerateMultimodal 1`] = `
{
  "input": [
    {
      "content": "Speak like a pirate. Arrrr.",
      "role": "system",
    },
    {
      "content": [
        {
          "text": "Tell a brief story about the provided image(s).",
          "type": "text",
        },
        {
          "image_url": {
            "url": "data:image/png;base64,iVBORw0K",
          },
          "type": "image_url",
        },
        {
          "image_url": {
            "url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
          },
          "type": "image_url",
        },
      ],
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "baz",
    "model": "gpt-4o-mini",
  },
  "metrics": {},
  "output": [
    {
      "content": "Arrr, gather 'round, me hearties! Let me spin ye a tale of a jolly banana named Bananarama. This bright yellow fellow lived in a bustling fruit market, always sportin' a cheerful grin. One fateful day, Bananarama dreamt of adventure beyond the market stalls.

With a mighty leap, he rolled away, escapin' the clutches of the fruit vendor! He sailed down the street, dodgin' curious critters and mischievous seagulls. His quest? To find the legendary Fruit Kingdom, where all fruits lived in harmony.

Along his journey, he made merry friends—a wise old apple and a zesty lemon—who joined him on his quest. Together, they faced challenges, shared laughter, and discovered the true treasure of friendship.

At long last, they reached the Fruit Kingdom, where Bananarama became a hero, celebrated for his bravery and sunny spirit. And so, they lived happily ever after, spreadin' joy and fruity fun across the land! Arrr! 🍌✨",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextDoGenerateTools 1`] = `
{
  "input": [
    {
      "content": "What is the weather in San Francisco and what attractions should I visit?",
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "bar",
    "model": "gpt-4o-mini",
    "temperature": 0.5,
  },
  "metrics": {
    "completion_tokens": 46,
    "input_tokens": 81,
    "output_tokens": 46,
    "prompt_tokens": 81,
    "tokens": 127,
  },
  "output": [
    {
      "content": undefined,
      "role": "assistant",
      "tool_calls": [
        {
          "function": {
            "arguments": "{"location": "San Francisco"}",
            "name": "weather",
          },
          "id": "call_Dkaya8Vs6S9AKVudxLAh7UhX",
          "type": "function",
        },
        {
          "function": {
            "arguments": "{"city": "San Francisco"}",
            "name": "cityAttractions",
          },
          "id": "call_LtCrKXW0j2HIKa0ltnijxobP",
          "type": "function",
        },
      ],
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextMultimodal 1`] = `
{
  "input": [
    {
      "content": "Speak like a pirate. Arrrr.",
      "role": "system",
    },
    {
      "content": [
        {
          "text": "Tell a brief story about the provided image(s).",
          "type": "text",
        },
        {
          "image_url": {
            "url": "data:image/png;base64,iVBORw0KGgo",
          },
          "type": "image_url",
        },
        {
          "image_url": {
            "url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
          },
          "type": "image_url",
        },
      ],
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "baz",
    "model": "gpt-4o-mini",
    "temperature": 0.5,
  },
  "metrics": {},
  "output": [
    {
      "content": "Arrr, gather 'round, me hearties! Let me spin ye a tale of a jolly banana named Bananarama.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > generateTextTools 1`] = `
{
  "input": [
    {
      "content": [
        {
          "text": "What is the weather in San Francisco and what attractions should I visit?",
          "type": "text",
        },
      ],
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "bar",
    "maxSteps": 1,
    "model": "gpt-4o-mini",
    "temperature": 0.5,
  },
  "metrics": {
    "completion_tokens": 46,
    "prompt_tokens": 81,
    "tokens": 127,
  },
  "output": [
    {
      "content": undefined,
      "role": "assistant",
      "tool_calls": [
        {
          "function": {
            "arguments": "{"location": "San Francisco"}",
            "name": "weather",
          },
          "id": "call_Dkaya8Vs6S9AKVudxLAh7UhX",
          "type": "function",
        },
        {
          "function": {
            "arguments": "{"city": "San Francisco"}",
            "name": "cityAttractions",
          },
          "id": "call_LtCrKXW0j2HIKa0ltnijxobP",
          "type": "function",
        },
      ],
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > serializedBraintrustFields 1`] = `
{
  "input": [
    {
      "content": "Tell me a story about a dragon and a knight.",
      "role": "user",
    },
  ],
  "metadata": {
    "characters": [
      "dragon",
      "knight",
    ],
    "story": "fantasy",
  },
  "metrics": {
    "bar": 43,
    "foo": 12,
  },
  "output": [
    {
      "content": "The dragon and the knight faced off in a fierce battle, each determined to emerge victorious. The dragon breathed fire, but the knight's armor was strong. With a mighty swing of his sword, the knight struck the dragon down, ending the battle and saving the kingdom.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > serializedBraintrustFieldsInvalid 1`] = `
{
  "input": undefined,
  "metadata": {},
  "metrics": {},
  "output": undefined,
  "span_attributes": {
    "type": "task",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > streamObject 1`] = `
{
  "input": [
    {
      "content": [
        {
          "text": "Generate a celery salad recipe.",
          "type": "text",
        },
      ],
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "baz",
    "model": "gpt-4o-mini",
    "output": "object",
    "response_format": {
      "json_schema": {
        "name": "response",
        "schema": {
          "$schema": "http://json-schema.org/draft-07/schema#",
          "additionalProperties": false,
          "properties": {
            "recipe": {
              "additionalProperties": false,
              "properties": {
                "ingredients": {
                  "items": {
                    "additionalProperties": false,
                    "properties": {
                      "amount": {
                        "type": "string",
                      },
                      "name": {
                        "type": "string",
                      },
                    },
                    "required": [
                      "name",
                      "amount",
                    ],
                    "type": "object",
                  },
                  "type": "array",
                },
                "name": {
                  "type": "string",
                },
                "steps": {
                  "items": {
                    "type": "string",
                  },
                  "type": "array",
                },
              },
              "required": [
                "name",
                "ingredients",
                "steps",
              ],
              "type": "object",
            },
          },
          "required": [
            "recipe",
          ],
          "type": "object",
        },
      },
      "name": "response",
      "type": "json_schema",
    },
    "temperature": 0.9,
  },
  "metrics": {},
  "output": [
    {
      "content": "{"recipe":{"name":"Crunchy Celery Salad","ingredients":[{"name":"Celery","amount":"4 stalks, chopped"},{"name":"Red bell pepper","amount":"1, diced"},{"name":"Carrot","amount":"1, grated"},{"name":"Red onion","amount":"¼, finely chopped"},{"name":"Fresh parsley","amount":"¼ cup, chopped"},{"name":"Lemon juice","amount":"2 tablespoons"},{"name":"Olive oil","amount":"2 tablespoons"},{"name":"Honey","amount":"1 teaspoon"},{"name":"Salt","amount":"to taste"},{"name":"Black pepper","amount":"to taste"}],"steps":["In a large mixing bowl, combine chopped celery, diced red bell pepper, grated carrot, and finely chopped red onion.","Add the chopped parsley to the vegetable mixture.","In a separate small bowl, whisk together the lemon juice, olive oil, honey, salt, and black pepper until well combined.","Pour the dressing over the salad and toss everything together until the vegetables are evenly coated with the dressing.","Taste and adjust seasoning if necessary.","Chill in the refrigerator for about 15-30 minutes before serving to allow the flavors to meld.","Serve chilled as a refreshing side dish."]}}",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > streamTextDoStream 1`] = `
{
  "input": [
    {
      "content": "What is the weather typically like in Mexico City?",
      "role": "user",
    },
  ],
  "metadata": {
    "foo": "baz",
    "model": "gpt-4o-mini",
  },
  "metrics": {
    "ms_to_finish": 64.01300001144409,
    "ms_to_first_chunk": 50.01574993133545,
  },
  "output": [
    {
      "content": "Mexico City has a temperate climate, characterized by mild temperatures and a distinct wet and dry season. Here are some key features of the weather in Mexico City:

1. **Temperature**: The average temperature typically ranges from about 10°C (50°F) at night to 25°C (77°F) during the day. However, temperatures can vary, with warmer days in the spring and cooler nights in the winter.

2. **Seasons**:
   - **Dry Season**: This usually runs from November to April, with little to no rainfall. During this time, the weather is generally sunny and pleasant.
   - **Wet Season**: From May to October, Mexico City experiences its rainy season, with the most rainfall occurring in July and August. Afternoon thunderstorms are common during this period.

3. **Altitude**: Mexico City is situated at a high altitude (about 2,240 meters or 7,350 feet above sea level), which contributes to its cooler temperatures compared to other regions in Mexico.

4. **Humidity**: The city can experience varying humidity levels, especially during the rainy season, but overall, it tends to be moderate.

5. **Air Quality**: Due to its elevation and urban environment, air quality can fluctuate, particularly during the dry season when pollution can become more concentrated.

Overall, Mexico City enjoys a generally mild climate, making it a year-round destination, though visitors should be prepared for rain if traveling during the wet season.",
      "role": "assistant",
    },
  ],
  "span_attributes": {
    "type": "llm",
  },
}
`;

exports[`vercel-ai-sdk convertAttributesToSpan > toolCall 1`] = `
{
  "input": {
    "location": "San Francisco",
  },
  "metadata": {
    "function_name": "weather",
    "tool_call_id": "call_Dkaya8Vs6S9AKVudxLAh7UhX",
  },
  "metrics": {},
  "output": {
    "location": "San Francisco",
    "temperature": 68,
  },
  "span_attributes": {
    "type": "task",
  },
}
`;

import { AclObjectType, aclObjectTypeEnum } from "@braintrust/core/typespecs";
import * as tmp from "tmp";
import { Request } from "express";
import { ObjectType } from "./schema";
import { z } from "zod";
import { loadPrettyXact, ExtraFieldsError } from "@braintrust/core";
import {
  OBJECT_TYPE_FIELD,
  ObjectIdsUnion,
} from "@braintrust/local/api-schema";
import {
  FormatNotFoundErrorMessageInput,
  formatNotFoundErrorMessage,
} from "@braintrust/local";
import { BindError } from "@braintrust/btql/binder";
import { DISABLE_SYSADMIN_TELEMETRY } from "./env";
import { Logger } from "pino";
import * as node_util from "util";

// Copied from the usage example on
// https://github.com/jedisct1/siphash-js/tree/master?tab=readme-ov-file#usage.
export const SIPHASH_KEY = [0xdeadbeef, 0xcafebabe, 0x8badf00d, 0x1badb002];

export const REDIS_OUTBOUND_RATE_LIMIT_COUNTER = "outbound_rate_limit_counter";
export const BT_FUNCTION_LOGIN_CACHE_HEADER = "x-bt-function-creds-cached";
export const BT_FUNCTION_META_CACHE_HEADER = "x-bt-function-meta-cached";
export const REDIS_LOAD_PROMPT_KEYS_PREFIX_SET_PREFIX = "load_prompt_keys";
export const REDIS_ENV_SECRET_FUNCTION_KEY_PREFIX = "env_secret_function_";
export const REDIS_INSERT_LOGS_RESOURCE_CHECK_UNLIMITED_KEY =
  "insert_logs_resource_check_unlimited";
export const REDIS_INSERT_LOGS_RESOURCE_CHECK_FAILED_KEY =
  "insert_logs_resource_check_failed";
export const REDIS_OBJECT_CACHE_KEYS_SET_PREFIX = "object_cache_keys";
export const REDIS_ERROR_CONTEXT_CACHE_KEY = "error_context_cache_keys";
export const REDIS_ASYNC_SCORING_CACHE_KEY = "async_scoring_cache";
export const REDIS_ASYNC_SCORING_CACHE_KEYS_SET_KEY =
  "async_scoring_cache_keys";

export const REDIS_AUTOMATION_CACHE_KEY = "automation_cache";
export const REDIS_AUTOMATION_CACHE_KEYS_SET_KEY = "automation_cache_keys";

export const BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER =
  "x-bt-object-cache-was-cached-redis-token";
export const BT_RESOURCE_CHECK_WAS_CACHED_REDIS_TOKEN_HEADER =
  "x-bt-resource-check-cached-redis-token";
export const BT_PROMPT_WAS_CACHED_REDIS_TOKEN_HEADER =
  "x-bt-prompt-was-cached-redis-token";
export const BT_ASYNC_SCORING_CONFIG_WAS_CACHED_REDIS_TOKEN_HEADER =
  "x-bt-async-scoring-config-was-cached-redis-token";
export const BT_AUTOMATION_WAS_CACHED_REDIS_TOKEN_HEADER =
  "x-bt-automation-was-cached-redis-token";
export const BT_ENABLE_AUDIT_HEADER = "x-bt-enable-audit";
export const BT_AUDIT_USER_ID_HEADER = "x-bt-audit-user-id";
export const BT_AUDIT_USER_EMAIL_HEADER = "x-bt-audit-user-email";
export const BT_AUDIT_NORMALIZED_URL_HEADER = "x-bt-audit-normalized-url";
export const BT_AUDIT_RESOURCES_HEADER = "x-bt-audit-resources";
export const BT_INTERNAL_TRACE_ID_HEADER = "x-bt-internal-trace-id";
export const BT_QUERY_PLAN_HEADER = "x-bt-query-plan";

declare global {
  // We should really add
  // lib: ["ES2021"],
  // to tsconfig.json, but we can't because it creates a bunch of stream-related
  // type errors.
  class AggregateError extends Error {
    errors: Error[];
    constructor(errors: Iterable<Error>, message?: string);
  }
}

export class HTTPError extends Error {
  constructor(
    public status: number,
    public message: string,
  ) {
    super(message);
  }
}

export class BadRequestError extends Error {
  constructor(public message: string) {
    super(message);
  }
}

export class QueryTooCostlyError extends BadRequestError {
  constructor() {
    super(
      "Query too costly. Please make sure Brainstore is installed, configured, and fully backfilled for this project.",
    );
  }
}

export class InternalServerError extends Error {
  constructor(public message: string) {
    super(message);
  }
}

export class NotFoundError extends Error {
  constructor(public message: string) {
    super(message);
  }
}

export class AccessDeniedError extends Error {
  constructor(
    input: FormatNotFoundErrorMessageInput & Partial<FullAclObjectType>,
  ) {
    const { aclObjectType, overrideRestrictObjectType, objectType, ...rest } =
      input;
    if (aclObjectType && objectType) {
      throw new Error("Cannot specify both `aclObjectType` and `objectType`");
    }
    super(
      formatNotFoundErrorMessage({
        ...rest,
        ...(aclObjectType
          ? {
              objectType: fullAclObjectTypeName({
                aclObjectType,
                overrideRestrictObjectType,
              }),
            }
          : { objectType }),
      }),
    );
  }
}

export class ForbiddenError extends Error {
  constructor(public message: string) {
    super(message);
  }
}

export class TooManyRequestsError extends Error {
  constructor(public tryAfter: number) {
    super(
      "Too many requests. Please contact <NAME_EMAIL> to discuss remediation strategies",
    );
  }
}

export interface RequestContext {
  token?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  data: any;
  api_version?: number;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function isEmpty(a: any): a is null | undefined {
  return a === undefined || a === null;
}

export const fullAclObjectTypeSchema = z.strictObject({
  aclObjectType: aclObjectTypeEnum,
  overrideRestrictObjectType: aclObjectTypeEnum.optional(),
});

export type FullAclObjectType = z.infer<typeof fullAclObjectTypeSchema>;

export function objectTypeToAclObjectType(
  objectType: ObjectType,
): FullAclObjectType {
  switch (objectType) {
    case "experiment":
      return { aclObjectType: "experiment" };
    case "dataset":
      return { aclObjectType: "dataset" };
    case "prompt_session":
    case "playground_logs":
      return { aclObjectType: "prompt_session" };
    case "project":
      return { aclObjectType: "project" };
    case "project_prompts":
    case "project_functions":
      return { aclObjectType: "project", overrideRestrictObjectType: "prompt" };
    case "project_logs":
      return { aclObjectType: "project_log" };
    case "org_prompts":
    case "org_functions":
      return {
        aclObjectType: "org_project",
        overrideRestrictObjectType: "prompt",
      };
    case "org_project_metadata":
      return {
        aclObjectType: "org_project",
      };
  }
}

export function isVersionQuerySupported(objectType: ObjectType): boolean {
  return [
    "dataset",
    "project_prompts",
    "project_functions",
    "org_prompts",
    "org_functions",
    "prompt_session",
  ].includes(objectType);
}

export function fullAclObjectTypeName({
  aclObjectType,
  overrideRestrictObjectType,
}: FullAclObjectType): string {
  return `${aclObjectType}${overrideRestrictObjectType ? ` ${overrideRestrictObjectType}` : ""}`;
}

export function getAclObjectId(objectIds: ObjectIdsUnion): string {
  switch (objectIds[OBJECT_TYPE_FIELD]) {
    case "experiment":
      return objectIds.experiment_id;
    case "dataset":
      return objectIds.dataset_id;
    case "prompt_session":
    case "playground_logs":
      return objectIds.prompt_session_id;
    case "project_logs":
    case "project_prompts":
    case "project_functions":
      return objectIds.project_id;
    default:
      const x: never = objectIds;
      throw new Error(`Unknown objectIds: ${JSON.stringify(x)}`);
  }
}

// When possible, prefer the `getAclObjectId` function because it is type-safe.
export const aclObjectTypeToColName: { [K in AclObjectType]?: string } = {
  organization: "org_id",
  project: "project_id",
  project_log: "project_id",
  experiment: "experiment_id",
  dataset: "dataset_id",
  prompt: "id",
  prompt_session: "prompt_session_id",
  org_project: "org_id",
};

export function getCurrentUnixTimestamp(): number {
  return new Date().getTime() / 1000;
}

export function getWasCachedToken(
  req: Request,
  headerName: string,
): string | undefined {
  return wrapZodError(() =>
    z.string().optional().parse(req.headers[headerName]),
  );
}

export function normalizeXact(xact: string): string {
  return xact.length === 16 ? loadPrettyXact(xact) : xact;
}

export function postDefaultHeaders({ token }: { token: string | undefined }) {
  return {
    ...(token ? { Authorization: `Bearer: ${token}` } : {}),
    "Content-Type": "application/json",
  };
}

// https://stackoverflow.com/questions/715417/converting-from-a-string-to-boolean-in-python
export function parseUrlBool(s: string): boolean {
  return [
    "true",
    "1",
    "t",
    "y",
    "yes",
    "yeah",
    "yup",
    "certainly",
    "uh-huh",
  ].includes(s.trim().toLowerCase());
}

// Adapted from ChatGPT.
export function base64ToUrlSafe(s: string) {
  // Replace '+' with '-', '/' with '_', and remove '='.
  return s.replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}

// Given a string `prefix`, returns a pair of strings (start, end), where
// searching a sorted list for all strings lexicographically in between `start`
// inclusive and `end` exclusive will all start with `prefix`.
//
// We assume the character set being worked on is ASCII with no occurrences of
// the maximum ASCII character 127.
export function minmaxLexicographicalRangeAscii(prefix: string): {
  start: string;
  end: string;
} {
  return { start: prefix, end: prefix + String.fromCharCode(127) };
}

export function wrapZodError<T>(f: () => T, opts?: { errmsg?: string }): T {
  try {
    return f();
  } catch (e) {
    if (e instanceof z.ZodError) {
      throw new BadRequestError(
        JSON.stringify({
          error: opts?.errmsg ?? "Invalid request",
          errors: e.errors,
        }),
      );
    } else if (e instanceof ExtraFieldsError) {
      throw new BadRequestError(e.message);
    } else {
      throw e;
    }
  }
}

export function wrapBindError<T>(f: () => T): T {
  try {
    return f();
  } catch (e) {
    if (e instanceof BindError) {
      throw new BadRequestError(e.message);
    } else {
      throw e;
    }
  }
}

// Function to create a temporary directory using Promises
export function createTempDir(): Promise<{
  path: string;
  cleanupCallback: () => void;
}> {
  return new Promise((resolve, reject) => {
    tmp.dir({ unsafeCleanup: true }, (err, path, cleanupCallback) => {
      if (err) {
        reject(err);
      } else {
        resolve({ path, cleanupCallback });
      }
    });
  });
}

export function getSysadminRoles(): string[] | undefined {
  return DISABLE_SYSADMIN_TELEMETRY ? undefined : ["sysadmin"];
}

export function extractErrorText(err: unknown): string {
  if (typeof err === "string") {
    return err;
  }
  if (err instanceof AggregateError) {
    return err.errors.map(extractErrorText).join("\n");
  }
  if (!(err instanceof Object)) {
    return "";
  }
  if ("message" in err && typeof err.message === "string") {
    return err.message;
  }
  if ("text" in err && typeof err.text === "string") {
    return err.text;
  }
  return "";
}

// Logs the result of a non-successful request in a format that is easy to
// digest for our observability tools.
export function logRequestFailure({
  logger,
  req,
  statusCode,
  err,
  errorContext,
}: {
  logger: Logger;
  req: Request;
  statusCode: number;
  err: unknown;
  errorContext?: Record<string, unknown>;
}) {
  const errText = extractErrorText(err);
  const isInternalServerError = statusCode >= 500 && statusCode < 600;
  const loggerMethod = (() => {
    if (isInternalServerError) {
      return "error";
    } else {
      return "info";
    }
  })();
  logger[loggerMethod](
    {
      method: req.method,
      path: req.path,
      statusCode,
      error: `\n${node_util.inspect(err, { depth: null })}`,
      ...(errorContext ? { errorContext } : {}),
    },
    `REQUEST WILL FAIL${isInternalServerError ? " ( INTERNAL SERVER ERROR )" : ""}: ${errText}`,
  );
}

import * as esbuild from "esbuild";
import fs from "fs";
import esbuildPluginPino from "esbuild-plugin-pino";
import { join } from "path";

const gitCommit =
  process.env.GIT_COMMIT_HASH ||
  require("child_process").execSync("git rev-parse HEAD").toString().trim();

const version =
  process.env.API_VERSION ||
  fs
    .readFileSync(join(__dirname, "..", "..", "VERSION"), "utf8")
    .trim()
    .replace(/^v/, "");

const watch = process.argv.includes("--watch");

const LOCAL_EXTERNAL = [
  "nock",
  "smithy",
  "duckdb",
  "dockerode",
  "@braintrust/btql-wasm",
];
const LAMBDA_EXTERNAL = [
  "nock",
  "smithy",
  "duckdb",
  "@aws-sdk/*",
  "dd-trace",
  "datadog-lambda-js",
];

const baseBuildOptions: esbuild.BuildOptions = {
  platform: "node",
  bundle: true,
  minify: true,
  sourcemap: true,
  target: "es2020",
  loader: {
    ".html": "text",
  },
  define: {
    "process.env.GIT_COMMIT": JSON.stringify(gitCommit),
    "process.env.API_VERSION": JSON.stringify(version),
  },
  plugins: [],
};

const pinoPlugin = esbuildPluginPino({ transports: ["pino-pretty"] });

function makeBuildOptions({
  entryPoints,
  outDir,
  outFile,
  mode,
}: {
  entryPoints: string[];
  outDir?: string;
  outFile?: string;
  mode: "local" | "lambda";
}) {
  return {
    ...baseBuildOptions,
    entryPoints,
    outdir: outDir,
    outfile: outFile,
    define: {
      ...baseBuildOptions.define,
      "process.env.DEPLOYMENT_MODE": `"${mode}"`,
    },
    plugins: [
      ...(baseBuildOptions.plugins || []),
      ...(mode === "local" ? [pinoPlugin] : []),
    ],
    external: mode === "local" ? LOCAL_EXTERNAL : LAMBDA_EXTERNAL,
  };
}

async function build() {
  const targets = [
    // local api
    makeBuildOptions({
      entryPoints: ["src/local.ts"],
      outDir: "dist/local",
      mode: "local",
    }),
    makeBuildOptions({
      entryPoints: ["src/wrapper/wrapper-inline.ts"],
      outDir: "dist/local/vm",
      mode: "local",
    }),
    // local proxy
    makeBuildOptions({
      entryPoints: ["src/proxy/local.ts"],
      outDir: "dist/local-proxy",
      mode: "local",
    }),
    makeBuildOptions({
      entryPoints: ["src/proxy/test-proxy/index.ts"],
      outDir: "dist/test-proxy",
      mode: "local",
    }),
    // lambdas
    makeBuildOptions({
      entryPoints: ["src/index.ts"],
      outDir: "dist/lambda",
      mode: "lambda",
    }),
    makeBuildOptions({
      entryPoints: ["src/clickhouse-etl-lambda/index.ts"],
      outDir: "dist/lambda-etl",
      mode: "lambda",
    }),
    makeBuildOptions({
      entryPoints: ["src/cron/lambda/index.ts"],
      outDir: "dist/lambda-cron",
      mode: "lambda",
    }),
    // lambda wrappers
    makeBuildOptions({
      entryPoints: ["src/proxy/index.ts"],
      outDir: "dist/proxy",
      mode: "lambda",
    }),
    makeBuildOptions({
      entryPoints: ["src/wrapper/lambda-bundle.ts"],
      outFile: "dist/proxy/vm/wrapper-bundle.js",
      mode: "lambda",
    }),
    makeBuildOptions({
      entryPoints: ["src/wrapper/lambda-inline.ts"],
      outFile: "dist/proxy/vm/wrapper-inline.js",
      mode: "lambda",
    }),
  ];

  const buildPromises = targets.map(async (target) => {
    if (watch) {
      const context = await esbuild.context(target);
      await context.watch();
    } else {
      const entrypointNames = target.entryPoints.join(", ");
      await esbuild.build(target);
      console.log(`Build complete (${entrypointNames}): ${target.outdir}`);
    }
  });

  await Promise.all(buildPromises);
  if (watch) {
    console.log("Watching for changes...");
  } else {
    console.log("Build complete");
  }
}

build().catch((e) => {
  console.error(e);
  process.exit(1);
});

import argparse
import os
import os.path
import re
import sys
import tempfile
import uuid
from string import Template
from urllib.parse import urlparse

import clickhouse_connect
import psycopg2
import psycopg2.sql as pgsql
from psycopg2.errors import UndefinedTable

PG_URL = os.environ.get("PG_URL", f"postgres://postgres:postgres@localhost:5532/postgres")
CLICKHOUSE_CONNECT_URL = os.environ.get("CLICKHOUSE_CONNECT_URL", f"*************************************/default")
SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))
MIGRATIONS_DIR = os.path.join(SCRIPT_DIR, "migrations")
CLICKHOUSE_MIGRATIONS_DIR = os.path.join(SCRIPT_DIR, "clickhouse-migrations")
MIGRATION_VERSION_TABLE = os.environ.get("MIGRATION_VERSION_TABLE", "migration_version")
MIGRATION_VERSION_IDENT = pgsql.Identifier(MIGRATION_VERSION_TABLE)

BACKGROUND_MIGRATION_ID = 1


# Return whether the given table exists.
def _tbl_exists(tblname, cursor):
    cursor.execute(
        """
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name = %s)
        """,
        (tblname,),
    )
    return bool(cursor.fetchone()[0])


# Return whether the MIGRATION_VERSION_TABLE exists.
def _migration_version_exists(cursor):
    return _tbl_exists(MIGRATION_VERSION_TABLE, cursor)


# Get the last migration ID from the MIGRATION_VERSION_TABLE table. It should
# have at most one row. Empty if no migrations have been run.
def _get_last_migration_id(cursor):
    if _migration_version_exists(cursor):
        cursor.execute(pgsql.SQL("SELECT last_migration_id FROM {}").format(MIGRATION_VERSION_IDENT))
        all_ids = [r[0] for r in cursor.fetchall()]
        if len(all_ids) != 1:
            raise RuntimeError(
                f"Expected exactly one ID in {MIGRATION_VERSION_TABLE} table. Found {all_ids}. Please resolve manually"
            )
        return all_ids[0]
    else:
        return None


def parse_db_url(url):
    parsed = urlparse(url)
    return dict(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password or "",
        database=parsed.path.lstrip("/"),
    )


def connect_to_clickhouse():
    kwargs = parse_db_url(CLICKHOUSE_CONNECT_URL)
    return clickhouse_connect.get_client(**kwargs)


def single_quote(s):
    return "'" + escape_single_quote(s) + "'"


def escape_single_quote(s):
    return s.replace("'", "''")


def _clickhouse_tbl_exists(tblname, connection):
    return len(connection.query(f"SHOW TABLES LIKE {single_quote(tblname)}").result_rows) > 0


def _clickhouse_migration_version_exists(connection):
    return _clickhouse_tbl_exists(MIGRATION_VERSION_TABLE, connection)


def _clickhouse_get_last_migration_id(connection):
    if _clickhouse_migration_version_exists(connection):
        # Clickhouse doesn't support updates
        rows = connection.query(f"SELECT COUNT(*), MAX(last_migration_id) FROM {MIGRATION_VERSION_TABLE}").result_rows
        assert len(rows) == 1, "Programming error: aggregate should always return one row"
        assert rows[0][0] > 0, f"Expected at least one ID in {MIGRATION_VERSION_TABLE} table. Found {rows[0]}"
        return rows[0][1]
    else:
        return None


def collect_migration_files(migrations_dir, last_migration_id, include_draft=False):
    # Now collect all files in the migrations directory with an id greater than
    # the last.
    if not os.path.exists(migrations_dir):
        raise Exception(f"No migrations directory {migrations_dir}")
    all_migration_files = [f for f in os.listdir(migrations_dir) if os.path.isfile(os.path.join(migrations_dir, f))]

    extra_migrations = {}
    pending_migrations = []
    for f in all_migration_files:
        if not f.endswith(".sql"):
            print(f"Skipping non-SQL file {f}", file=sys.stderr)
            continue

        if f == "init.sql":
            if last_migration_id is None:
                extra_migrations["init"] = os.path.join(migrations_dir, f)
        elif f == "materializations.sql":
            extra_migrations["materializations"] = os.path.join(migrations_dir, f)
        else:
            migration_id = int(f[: f.index("_")])
            if last_migration_id is None or migration_id > last_migration_id:
                pending_migrations.append((migration_id, os.path.join(migrations_dir, f)))

    if include_draft:
        root, basename = os.path.split(migrations_dir)
        draft_migrations_dir = os.path.join(root, "draft-" + basename)

        if os.path.exists(draft_migrations_dir):
            for f in os.listdir(draft_migrations_dir):
                migration_id = int(f[: f.index("_")])
                if last_migration_id is None or migration_id > last_migration_id:
                    pending_migrations.append((migration_id, os.path.join(draft_migrations_dir, f)))

    # Check that the migration IDs are in strictly increasing order with no
    # missing values.
    pending_migrations.sort(key=lambda x: x[0])
    expected_migration_ids = [
        (-1 if last_migration_id is None else last_migration_id) + i + 1 for i in range(len(pending_migrations))
    ]
    pending_migration_ids = [x[0] for x in pending_migrations]
    assert (
        expected_migration_ids == pending_migration_ids
    ), f"Pending migration ids {pending_migration_ids} are not in strictly increasing order with no gaps from last migration id {last_migration_id}"

    return pending_migrations, extra_migrations


def run_pg(execute=False, run_all_in_foreground=False, include_draft=False, insert_logs2=False):
    with psycopg2.connect(PG_URL) as conn:
        with conn.cursor() as cursor:
            last_migration_id = _get_last_migration_id(cursor)
            include_init = not _tbl_exists("logs", cursor)
            if insert_logs2:
                print("Creating partman extension and schema", file=sys.stderr)
                cursor.execute(
                    "CREATE SCHEMA IF NOT EXISTS partman; CREATE EXTENSION IF NOT EXISTS pg_partman SCHEMA partman; CREATE EXTENSION IF NOT EXISTS pg_cron;"
                )
                cursor.execute(
                    """
                    SELECT (
                        (exists (select 1 from partman.part_config where parent_table = 'public.logs2')) AND
                        (exists (select 1 from cron.job where jobname = 'partman_maintenance_f0fdffe1'))
                    ) as has_setup_partman
                """
                )
                run_setup_partman = not cursor.fetchone()[0]
            else:
                run_setup_partman = False

    try:
        pending_migrations, extra_migrations = collect_migration_files(
            MIGRATIONS_DIR, last_migration_id, include_draft
        )
    except Exception as e:
        print(e, file=sys.stderr)
        return

    foreground_migrations = []
    background_migrations = []

    if (initial_migration := extra_migrations.get("init")) and include_init:
        foreground_migrations.append((None, initial_migration))
    if run_all_in_foreground:
        foreground_migrations.extend(pending_migrations)
    else:
        foreground_migrations.extend([x for x in pending_migrations if x[0] <= BACKGROUND_MIGRATION_ID])
        background_migrations.extend([x for x in pending_migrations if x[0] > BACKGROUND_MIGRATION_ID])

    if run_setup_partman:
        setup_temp_file = tempfile.NamedTemporaryFile(delete=False)
        setup_temp_file.write(b"SELECT setup_logs2_partman(p_schedule_cron_job => true);")
        setup_temp_file.close()
        if run_all_in_foreground:
            foreground_migrations.append((None, setup_temp_file.name))
        else:
            background_migrations.append((None, setup_temp_file.name))

    pending_migration_files = [x[1] for x in (foreground_migrations + background_migrations)]
    if len(pending_migration_files) == 0:
        print("No pending migrations found")
        return
    print("Found the following pending migration files:")
    for fpath in pending_migration_files:
        print(f"\t{fpath}")

    if not execute:
        return

    if len(foreground_migrations) > 0:
        print(f"Running the following migrations in the foreground:")
        for _, fpath in foreground_migrations:
            print(f"\t{fpath}")
        final_migration_id = max([x[0] for x in foreground_migrations if x[0] is not None], default=None)
        with psycopg2.connect(PG_URL) as conn:
            with conn.cursor() as cursor:
                # Acquire an advisory lock to prevent other migrators from
                # running concurrently.
                # This must be kept in sync with
                # advisory_locks.py/advisory_locks.ts (which defines the role of
                # each lock id)
                cursor.execute("SELECT pg_advisory_xact_lock(0)")
                actual_last_migration_id = _get_last_migration_id(cursor)
                if last_migration_id != actual_last_migration_id:
                    print(
                        f"Skipping migrations: Expected migration id {last_migration_id} does not match actual {actual_last_migration_id}."
                    )
                    return
                for _, fpath in foreground_migrations:
                    print(f"Running {fpath}")
                    with open(fpath) as f:
                        contents = f.read()
                        cursor.execute(contents)
                if final_migration_id is not None:
                    if last_migration_id is None:
                        cursor.execute(
                            pgsql.SQL("INSERT INTO {}(last_migration_id) VALUES (%s)").format(MIGRATION_VERSION_IDENT),
                            (final_migration_id,),
                        )
                    else:
                        cursor.execute(
                            pgsql.SQL("UPDATE {} SET last_migration_id = %s").format(MIGRATION_VERSION_IDENT),
                            (final_migration_id,),
                        )
        print("Success!")
        if final_migration_id is not None:
            last_migration_id = final_migration_id

    if len(background_migrations) > 0:
        assert last_migration_id is not None and last_migration_id >= BACKGROUND_MIGRATION_ID
        final_migration_id = max([x[0] for x in background_migrations if x[0] is not None], default=None)

        print(f"Running the following migrations in the background:")
        for _, fpath in background_migrations:
            print(f"\t{fpath}")

        stmts = []
        for _, fpath in background_migrations:
            with open(fpath) as f:
                stmts.append(f.read())

        job_name = str(uuid.uuid4())
        with psycopg2.connect(PG_URL) as conn:
            conn.autocommit = True
            with conn.cursor() as cursor:
                # Create a pending entry in the results table.
                cursor.execute(
                    """
                    INSERT INTO run_migration_results(job_name)
                    VALUES (%s)
                    RETURNING id
                    """,
                    (job_name,),
                )
                result_id = cursor.fetchone()[0]

                query = """
                    SELECT cron.schedule(
                        %(job_name)s,
                        %(cron_config)s,
                        $lambda_function_cron_job$
                        select run_migration(
                            %(job_name)s,
                            %(expected_current_migration_id)s,
                            %(next_migration_id)s,
                            %(stmts)s,
                            %(result_id)s);
                        $lambda_function_cron_job$);
                    """
                value_params = dict(
                    job_name=job_name,
                    cron_config="* * * * *",  # Every minute
                    expected_current_migration_id=last_migration_id,
                    next_migration_id=final_migration_id,
                    stmts=stmts,
                    result_id=result_id,
                )
                cursor.execute(query, value_params)
                job_id = cursor.fetchone()[0]

                # Store the job_id in the results table.
                cursor.execute(
                    """
                    UPDATE run_migration_results
                    SET job_id = %s
                    WHERE id = %s
                    """,
                    (job_id, result_id),
                )
        print(f"Launched background migration job (job_name = {job_name})")


def run_clickhouse(execute=False):
    try:
        conn = connect_to_clickhouse()
    except Exception as e:
        print("Cannot connect to Clickhouse. This is likely ok!", file=sys.stderr)
        print(e, file=sys.stderr)
        return

    last_migration_id = _clickhouse_get_last_migration_id(conn)

    try:
        migrations, extra_migrations = collect_migration_files(CLICKHOUSE_MIGRATIONS_DIR, last_migration_id)
    except Exception as e:
        print(e, file=sys.stderr)
        return

    if len(migrations) == 0:
        print("No pending migrations found")
        return
    print("Found the following pending migration files:")
    for _, fpath in migrations:
        print(f"\t{fpath}")
    materializations_file = extra_migrations.get("materializations")
    if materializations_file:
        print(f"Found materializations file {materializations_file}")

    # Check that none of the regular migrations have any `MATERIALIZE COLUMN`
    # statements.
    MATERIALIZE_COLUMN_PATTERN = "alter.*table.*materialize.*column"
    for _, fpath in migrations:
        with open(fpath) as f:
            contents = f.read()
            if re.search(MATERIALIZE_COLUMN_PATTERN, contents, flags=re.IGNORECASE):
                raise Exception(
                    f"Migration {fpath} contains a MATERIALIZE COLUMN statement. These statements should be moved to the `materializations.sql` file."
                )

    if not execute:
        return

    if len(migrations) > 0:
        final_migration_id = migrations[-1][0]
        if materializations_file:
            migrations.append((None, materializations_file))
        print(f"Running the following migrations in the foreground:")
        for _, fpath in migrations:
            print(f"\t{fpath}")

        has_s3 = conn.query("SELECT COUNT(*) FROM system.disks WHERE name LIKE 's3%'").result_rows[0][0] > 0

        pg_creds = parse_db_url(PG_URL)
        template_vars = {
            "PG_HOST": pg_creds["host"] if pg_creds["host"] != "localhost" else "host.docker.internal",
            "PG_PORT": pg_creds["port"],
            "PG_USER": pg_creds["user"],
            "PG_PASSWORD": pg_creds["password"],
            "PG_DATABASE": pg_creds["database"],
            "STORAGE_SETTINGS": "SETTINGS index_granularity = 8192, storage_policy = 's3_main'" if has_s3 else "",
        }

        # Use postgres lock to synchronize
        with psycopg2.connect(PG_URL) as pg_conn:
            with pg_conn.cursor() as cursor:
                # Acquire an advisory lock to prevent other migrators from
                # running concurrently.
                # This must be kept in sync with advisory_locks.py (which defines the role of each lock id)
                cursor.execute("SELECT pg_advisory_xact_lock(0)")

                actual_last_migration_id = _clickhouse_get_last_migration_id(conn)
                if last_migration_id != actual_last_migration_id:
                    print(
                        f"Skipping migrations: Expected migration id {last_migration_id} does not match actual {actual_last_migration_id}."
                    )
                    return
                for _, fpath in migrations:
                    print(f"Running {fpath}")
                    with open(fpath) as f:
                        contents = Template(f.read()).substitute(template_vars)
                        for query in contents.split(";"):
                            query = query.strip()
                            if query:
                                conn.query(query)
                conn.query(f"INSERT INTO {MIGRATION_VERSION_TABLE}(last_migration_id) VALUES ({final_migration_id})")
        print("Success!")
        last_migration_id = final_migration_id


def run(execute=False, run_all_in_foreground=False, include_draft=False, insert_logs2=False):
    print("Running pg migrations")
    run_pg(execute, run_all_in_foreground, include_draft, insert_logs2)
    print("Running clickhouse migrations")
    run_clickhouse(execute)


def lambda_handler(event, context):
    include_draft = os.environ.get("BRAINTRUST_RUN_DRAFT_MIGRATIONS", "false").lower() == "true"
    insert_logs2 = os.environ.get("INSERT_LOGS2", "false").lower() == "true"
    run(execute=True, run_all_in_foreground=False, include_draft=include_draft, insert_logs2=insert_logs2)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="LambdaFunction",
        description=f'Run pending migrations. Migration files should be placed in {MIGRATIONS_DIR} and have a filename of the form "[migration_id]_[human-readable-tagline].sql"',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--execute",
        action="store_true",
        help="If provided, run the pending migrations",
    )
    parser.add_argument(
        "--run-all-in-foreground",
        action="store_true",
        help="If provided, run all migrations in foreground mode",
    )
    parser.add_argument(
        "--include-draft",
        action="store_true",
        help="If provided, include draft migrations",
    )
    parser.add_argument(
        "--insert-logs2",
        action="store_true",
        help="If provided, run migrations necessary to use the logs2 table",
    )

    args = parser.parse_args()
    include_draft = os.environ.get("BRAINTRUST_RUN_DRAFT_MIGRATIONS", "false").lower() == "true" or args.include_draft
    insert_logs2 = os.environ.get("INSERT_LOGS2", "false").lower() == "true" or args.insert_logs2
    run(
        execute=args.execute,
        run_all_in_foreground=args.run_all_in_foreground,
        include_draft=include_draft,
        insert_logs2=insert_logs2,
    )

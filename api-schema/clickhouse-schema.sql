CREATE TABLE migration_version(last_migration_id UInt64)
ENGINE = MergeTree()
PRIMARY KEY (last_migration_id)
$STORAGE_SETTINGS;

-- Map tables from postgres
CREATE TABLE pg_logs (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text
  , audit_data Nullable(text)

  , scores Nullable(text)

  , id text
  , span_id Nullable(text)
  , root_span_id Nullable(text)
  , _xact_id Nullable(UInt64)
  , _object_delete Nullable(boolean)
  , created Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
)
ENGINE = PostgreSQL('$PG_HOST:$PG_PORT', '$PG_DATABASE', 'logs', '$PG_USER', '$PG_PASSWORD');

CREATE TABLE pg_comments (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text

  , id text
  , _xact_id Nullable(UInt64)
  , _object_delete Nullable(boolean)
  , created Nullable(text)

  , origin_id Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
)
ENGINE = PostgreSQL('$PG_HOST:$PG_PORT', '$PG_DATABASE', 'comments', '$PG_USER', '$PG_PASSWORD');

-- Map UDFs from postgres
CREATE OR REPLACE FUNCTION make_object_id
AS
(
    project_id, experiment_id, dataset_id, prompt_session_id, log_id
)
-> COALESCE(
    'experiment:' || experiment_id,
    'dataset:' || dataset_id,
    'prompt_session:' || prompt_session_id,
    CASE
        WHEN log_id = 'g' THEN 'global_log:' || project_id
        WHEN log_id = 'p' THEN 'prompt:' || project_id
        ELSE 'log:' || log_id
    END);


-- Create clickhouse tables
create table logs (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text
  , audit_data Nullable(text)

  , scores Nullable(text)

  , id text
  , span_id Nullable(text)
  , root_span_id Nullable(text)
  , _xact_id UInt64
  , _object_delete Int8
  , created text

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
  , object_id text
)
ENGINE = ReplacingMergeTree(_xact_id)
PRIMARY KEY (object_id, id)
$STORAGE_SETTINGS;

ALTER TABLE logs ADD COLUMN metrics_map
  Map(LowCardinality(String), Nullable(Float64))
  MATERIALIZED JSONExtract(data, 'metrics', 'Map(LowCardinality(String), Nullable(Float64))');
ALTER TABLE logs MATERIALIZE COLUMN metrics_map;

ALTER TABLE logs ADD COLUMN scores_map
  Map(LowCardinality(String), Nullable(Float64))
  MATERIALIZED JSONExtract(data, 'scores', 'Map(LowCardinality(String), Nullable(Float64))');
ALTER TABLE logs MATERIALIZE COLUMN scores_map;

ALTER TABLE logs ADD COLUMN created_dt
  Nullable(DateTime('UTC'))
  MATERIALIZED parseDateTime64BestEffortOrNull(created);
ALTER TABLE logs MATERIALIZE COLUMN created_dt;

ALTER TABLE logs ADD COLUMN tags
  Array(String)
  MATERIALIZED arrayMap(x -> JSONExtractString(x), JSONExtractArrayRaw(data, 'tags'));
ALTER TABLE logs MATERIALIZE COLUMN tags;

CREATE TABLE comments (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text

  , id text
  , _xact_id UInt64
  , _object_delete Int8
  , created text

  , origin_id Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
  , object_id text
)
ENGINE = ReplacingMergeTree(_xact_id)
PRIMARY KEY (object_id, id)
$STORAGE_SETTINGS;

ALTER TABLE comments ADD COLUMN created_dt
  Nullable(DateTime('UTC'))
  MATERIALIZED parseDateTime64BestEffortOrNull(created);
ALTER TABLE comments MATERIALIZE COLUMN created_dt;

ALTER TABLE comments ADD COLUMN tags
  Array(String)
  MATERIALIZED arrayMap(x -> JSONExtractString(x), JSONExtractArrayRaw(data, 'tags'));
ALTER TABLE comments MATERIALIZE COLUMN tags;

ALTER TABLE logs ADD COLUMN error
  Nullable(String)
  MATERIALIZED NULLIF(NULLIF(JSONExtractRaw(data, 'error'), ''), 'null');
ALTER TABLE logs MATERIALIZE COLUMN error;

ALTER TABLE logs ADD COLUMN metadata_map
  Map(LowCardinality(String), String)
  MATERIALIZED JSONExtractKeysAndValuesRaw(data, 'metadata');
ALTER TABLE logs MATERIALIZE COLUMN metadata_map;

ALTER TABLE logs ADD COLUMN span_attributes_map
  Map(LowCardinality(String), String)
  MATERIALIZED JSONExtractKeysAndValuesRaw(data, 'span_attributes');
ALTER TABLE logs MATERIALIZE COLUMN span_attributes_map;

-- https://clickhouse.com/blog/storing-traces-and-spans-open-telemetry-in-clickhouse
-- This allows us to more selectively filter based on keys and values in the metadata and span_attributes maps
-- It requires that the value is a non-nullable string.
alter table logs ADD INDEX idx_metadata_map_keys mapKeys(metadata_map) TYPE bloom_filter(0.01) GRANULARITY 1;
alter table logs ADD INDEX idx_metadata_map_values mapValues(metadata_map) TYPE bloom_filter(0.01) GRANULARITY 1;

alter table logs ADD INDEX idx_span_attributes_map_keys mapKeys(span_attributes_map) TYPE bloom_filter(0.01) GRANULARITY 1;
alter table logs ADD INDEX idx_span_attributes_map_values mapValues(span_attributes_map) TYPE bloom_filter(0.01) GRANULARITY 1;

ALTER TABLE logs ADD COLUMN is_root
  Nullable(Boolean)
  MATERIALIZED JSONLength(JSONExtractRaw(data, 'span_parents')) = 0;
ALTER TABLE logs MATERIALIZE COLUMN is_root;

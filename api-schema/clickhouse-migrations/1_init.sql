-- Map tables from postgres
CREATE TABLE pg_logs (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text
  , audit_data Nullable(text)

  , scores Nullable(text)

  , id text
  , span_id Nullable(text)
  , root_span_id Nullable(text)
  , _xact_id Nullable(UInt64)
  , _object_delete Nullable(boolean)
  , created Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
)
ENGINE = PostgreSQL('$PG_HOST:$PG_PORT', '$PG_DATABASE', 'logs', '$PG_USER', '$PG_PASSWORD');

CREATE TABLE pg_comments (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text

  , id text
  , _xact_id Nullable(UInt64)
  , _object_delete Nullable(boolean)
  , created <PERSON>ullable(text)

  , origin_id Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
)
ENGINE = PostgreSQL('$PG_HOST:$PG_PORT', '$PG_DATABASE', 'comments', '$PG_USER', '$PG_PASSWORD');

-- Map UDFs from postgres
CREATE OR REPLACE FUNCTION make_object_id
AS
(
    project_id, experiment_id, dataset_id, prompt_session_id, log_id
)
-> COALESCE(
    'experiment:' || experiment_id,
    'dataset:' || dataset_id,
    'prompt_session:' || prompt_session_id,
    CASE
        WHEN log_id = 'g' THEN 'global_log:' || project_id
        WHEN log_id = 'p' THEN 'prompt:' || project_id
        ELSE 'log:' || log_id
    END);


-- Create clickhouse tables
create table logs (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text
  , audit_data Nullable(text)

  , scores Nullable(text)

  , id text
  , span_id Nullable(text)
  , root_span_id Nullable(text)
  , _xact_id UInt64
  , _object_delete Int8
  , created text

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
  , object_id text
)
ENGINE = ReplacingMergeTree(_xact_id)
PRIMARY KEY (object_id, id)
$STORAGE_SETTINGS;

ALTER TABLE logs ADD COLUMN metrics_map
  Map(LowCardinality(String), Nullable(Float64))
  MATERIALIZED JSONExtract(data, 'metrics', 'Map(LowCardinality(String), Nullable(Float64))');

ALTER TABLE logs ADD COLUMN scores_map
  Map(LowCardinality(String), Nullable(Float64))
  MATERIALIZED JSONExtract(data, 'scores', 'Map(LowCardinality(String), Nullable(Float64))');

ALTER TABLE logs ADD COLUMN created_dt
  Nullable(DateTime('UTC'))
  MATERIALIZED parseDateTime64BestEffortOrNull(created);

ALTER TABLE logs ADD COLUMN tags
  Array(String)
  MATERIALIZED arrayMap(x -> JSONExtractString(x), JSONExtractArrayRaw(data, 'tags'));

CREATE TABLE comments (
    sequence_id UInt64
  , row_created datetime('UTC')
  , data text

  , id text
  , _xact_id UInt64
  , _object_delete Int8
  , created text

  , origin_id Nullable(text)

  , org_id Nullable(text)
  , project_id Nullable(text)
  , experiment_id Nullable(text)
  , dataset_id Nullable(text)
  , prompt_session_id Nullable(text)
  , log_id Nullable(text)
  , object_id text
)
ENGINE = ReplacingMergeTree(_xact_id)
PRIMARY KEY (object_id, id)
$STORAGE_SETTINGS;

ALTER TABLE comments ADD COLUMN created_dt
  Nullable(DateTime('UTC'))
  MATERIALIZED parseDateTime64BestEffortOrNull(created);

ALTER TABLE comments ADD COLUMN tags
  Array(String)
  MATERIALIZED arrayMap(x -> JSONExtractString(x), JSONExtractArrayRaw(data, 'tags'));

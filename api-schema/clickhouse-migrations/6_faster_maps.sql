ALTER TABLE logs DROP COLUMN IF EXISTS metadata_map; -- For good measure...
ALTER TABLE logs DROP COLUMN IF EXISTS metadata_map2;
ALTER TABLE logs DROP COLUMN IF EXISTS span_attributes_map;

ALTER TABLE logs ADD COLUMN metadata_map
  Map(LowCardinality(String), String)
  MATERIALIZED JSONExtractKeysAndValuesRaw(data, 'metadata');

ALTER TABLE logs ADD COLUMN span_attributes_map
  Map(LowCardinality(String), String)
  MATERIALIZED JSONExtractKeysAndValuesRaw(data, 'span_attributes');

alter table logs ADD INDEX idx_metadata_map_keys mapKeys(metadata_map) TYPE bloom_filter(0.01) GRANULARITY 1;
alter table logs ADD INDEX idx_metadata_map_values mapValues(metadata_map) TYPE bloom_filter(0.01) GRANULARITY 1;

alter table logs ADD INDEX idx_span_attributes_map_keys mapKeys(span_attributes_map) TYPE bloom_filter(0.01) GRANULARITY 1;
alter table logs ADD INDEX idx_span_attributes_map_values mapValues(span_attributes_map) TYPE bloom_filter(0.01) GRANULARITY 1;
